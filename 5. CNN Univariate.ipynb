{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "forex_deneme.ipynb", "provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}}, "cells": [{"source": ["# CNN Univariate"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "metadata": {"id": "hINrTZRqjHIh", "colab_type": "code", "colab": {}, "tags": []}, "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import mean_squared_error\n", "import tensorflow as tf\n", "import keras\n", "from keras.backend import sigmoid\n", "from keras.models import Sequential\n", "from keras import models, layers, backend, optimizers\n", "from keras.layers import Dropout, BatchNormalization, Flatten, Dense\n", "from keras.callbacks import EarlyStopping\n", "from keras.layers.convolutional import Conv1D\n", "from keras.layers.convolutional import MaxPooling1D\n", "import time\n", "from math import sqrt"], "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["Using TensorFlow backend.\n"]}]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["               AUD     EUR     NZD     GBP     BRL     CAD     CNY     DKK  \\\n", "Date                                                                         \n", "2001-01-02  0.5592  0.9465  0.4432  1.4977  1.9380  1.4963  8.2779  7.8845   \n", "2001-01-03  0.5635  0.9473  0.4463  1.5045  1.9460  1.4982  8.2773  7.8750   \n", "2001-01-04  0.5655  0.9448  0.4457  1.4930  1.9380  1.4985  8.2781  7.8991   \n", "2001-01-05  0.5712  0.9535  0.4518  1.4990  1.9530  1.5003  8.2775  7.8260   \n", "2001-01-08  0.5660  0.9486  0.4505  1.4969  1.9540  1.4944  8.2778  7.8705   \n", "...            ...     ...     ...     ...     ...     ...     ...     ...   \n", "2020-08-17  0.7211  1.1869  0.6547  1.3105  5.4734  1.3202  6.9318  6.2727   \n", "2020-08-18  0.7235  1.1928  0.6588  1.3228  5.4845  1.3181  6.9215  6.2406   \n", "2020-08-19  0.7234  1.1898  0.6607  1.3191  5.5045  1.3173  6.9192  6.2580   \n", "2020-08-20  0.7178  1.1862  0.6519  1.3190  5.6370  1.3177  6.9143  6.2783   \n", "2020-08-21  0.7156  1.1775  0.6533  1.3098  5.5949  1.3201  6.9179  6.3235   \n", "\n", "               HKD    INR  ...     CHF     TWD    THB          VEB     gdpGBP  \\\n", "Date                       ...                                                  \n", "2001-01-02  7.8000  46.74  ...  1.6075  33.000  43.79       0.7008  99.977643   \n", "2001-01-03  7.8000  46.75  ...  1.6025  33.078  43.70       0.7002  99.980866   \n", "2001-01-04  7.7998  46.78  ...  1.6115  33.000  43.53       0.6994  99.984089   \n", "2001-01-05  7.7993  46.76  ...  1.6025  32.927  43.26       0.6988  99.987312   \n", "2001-01-08  7.7998  46.73  ...  1.6076  32.850  42.95       0.6990  99.990535   \n", "...            ...    ...  ...     ...     ...    ...          ...        ...   \n", "2020-08-17  7.7503  74.74  ...  0.9058  29.380  31.15  293920.7455  79.588906   \n", "2020-08-18  7.7501  74.62  ...  0.9029  29.390  31.16  289550.7506  79.588906   \n", "2020-08-19  7.7500  74.85  ...  0.9114  29.370  31.25  291796.8124  79.588906   \n", "2020-08-20  7.7500  75.01  ...  0.9082  29.420  31.41  296027.2710  79.588906   \n", "2020-08-21  7.7502  74.92  ...  0.9131  29.400  31.56  302779.1537  79.588906   \n", "\n", "                gdpUSD  GBR_Value  USA_Value  liborGBP  liborUSD  \n", "Date                                                              \n", "2001-01-02  100.815277  -1.815904  -4.084297   5.81094   6.65125  \n", "2001-01-03  100.807191  -1.824859  -4.076654   6.09750   6.65375  \n", "2001-01-04  100.799105  -1.833815  -4.069010   5.57125   6.09625  \n", "2001-01-05  100.791019  -1.842770  -4.061367   5.37813   6.01625  \n", "2001-01-08  100.782933  -1.851726  -4.053723   5.50000   6.01500  \n", "...                ...        ...        ...       ...       ...  \n", "2020-08-17   90.264283  -3.837938  -2.068833   0.05088   0.08475  \n", "2020-08-18   90.264283  -3.837938  -2.068833   0.04838   0.08363  \n", "2020-08-19   90.264283  -3.837938  -2.068833   0.04925   0.08463  \n", "2020-08-20   90.264283  -3.837938  -2.068833   0.05063   0.08300  \n", "2020-08-21   90.264283  -3.837938  -2.068833   0.05150   0.08175  \n", "\n", "[4920 rows x 29 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>AUD</th>\n      <th>EUR</th>\n      <th>NZD</th>\n      <th>GBP</th>\n      <th>BRL</th>\n      <th>CAD</th>\n      <th>CNY</th>\n      <th>DKK</th>\n      <th>HKD</th>\n      <th>INR</th>\n      <th>...</th>\n      <th>CHF</th>\n      <th>TWD</th>\n      <th>THB</th>\n      <th>VEB</th>\n      <th>gdpGBP</th>\n      <th>gdpUSD</th>\n      <th>GBR_Value</th>\n      <th>USA_Value</th>\n      <th>liborGBP</th>\n      <th>liborUSD</th>\n    </tr>\n    <tr>\n      <th>Date</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2001-01-02</th>\n      <td>0.5592</td>\n      <td>0.9465</td>\n      <td>0.4432</td>\n      <td>1.4977</td>\n      <td>1.9380</td>\n      <td>1.4963</td>\n      <td>8.2779</td>\n      <td>7.8845</td>\n      <td>7.8000</td>\n      <td>46.74</td>\n      <td>...</td>\n      <td>1.6075</td>\n      <td>33.000</td>\n      <td>43.79</td>\n      <td>0.7008</td>\n      <td>99.977643</td>\n      <td>100.815277</td>\n      <td>-1.815904</td>\n      <td>-4.084297</td>\n      <td>5.81094</td>\n      <td>6.65125</td>\n    </tr>\n    <tr>\n      <th>2001-01-03</th>\n      <td>0.5635</td>\n      <td>0.9473</td>\n      <td>0.4463</td>\n      <td>1.5045</td>\n      <td>1.9460</td>\n      <td>1.4982</td>\n      <td>8.2773</td>\n      <td>7.8750</td>\n      <td>7.8000</td>\n      <td>46.75</td>\n      <td>...</td>\n      <td>1.6025</td>\n      <td>33.078</td>\n      <td>43.70</td>\n      <td>0.7002</td>\n      <td>99.980866</td>\n      <td>100.807191</td>\n      <td>-1.824859</td>\n      <td>-4.076654</td>\n      <td>6.09750</td>\n      <td>6.65375</td>\n    </tr>\n    <tr>\n      <th>2001-01-04</th>\n      <td>0.5655</td>\n      <td>0.9448</td>\n      <td>0.4457</td>\n      <td>1.4930</td>\n      <td>1.9380</td>\n      <td>1.4985</td>\n      <td>8.2781</td>\n      <td>7.8991</td>\n      <td>7.7998</td>\n      <td>46.78</td>\n      <td>...</td>\n      <td>1.6115</td>\n      <td>33.000</td>\n      <td>43.53</td>\n      <td>0.6994</td>\n      <td>99.984089</td>\n      <td>100.799105</td>\n      <td>-1.833815</td>\n      <td>-4.069010</td>\n      <td>5.57125</td>\n      <td>6.09625</td>\n    </tr>\n    <tr>\n      <th>2001-01-05</th>\n      <td>0.5712</td>\n      <td>0.9535</td>\n      <td>0.4518</td>\n      <td>1.4990</td>\n      <td>1.9530</td>\n      <td>1.5003</td>\n      <td>8.2775</td>\n      <td>7.8260</td>\n      <td>7.7993</td>\n      <td>46.76</td>\n      <td>...</td>\n      <td>1.6025</td>\n      <td>32.927</td>\n      <td>43.26</td>\n      <td>0.6988</td>\n      <td>99.987312</td>\n      <td>100.791019</td>\n      <td>-1.842770</td>\n      <td>-4.061367</td>\n      <td>5.37813</td>\n      <td>6.01625</td>\n    </tr>\n    <tr>\n      <th>2001-01-08</th>\n      <td>0.5660</td>\n      <td>0.9486</td>\n      <td>0.4505</td>\n      <td>1.4969</td>\n      <td>1.9540</td>\n      <td>1.4944</td>\n      <td>8.2778</td>\n      <td>7.8705</td>\n      <td>7.7998</td>\n      <td>46.73</td>\n      <td>...</td>\n      <td>1.6076</td>\n      <td>32.850</td>\n      <td>42.95</td>\n      <td>0.6990</td>\n      <td>99.990535</td>\n      <td>100.782933</td>\n      <td>-1.851726</td>\n      <td>-4.053723</td>\n      <td>5.50000</td>\n      <td>6.01500</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2020-08-17</th>\n      <td>0.7211</td>\n      <td>1.1869</td>\n      <td>0.6547</td>\n      <td>1.3105</td>\n      <td>5.4734</td>\n      <td>1.3202</td>\n      <td>6.9318</td>\n      <td>6.2727</td>\n      <td>7.7503</td>\n      <td>74.74</td>\n      <td>...</td>\n      <td>0.9058</td>\n      <td>29.380</td>\n      <td>31.15</td>\n      <td>293920.7455</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05088</td>\n      <td>0.08475</td>\n    </tr>\n    <tr>\n      <th>2020-08-18</th>\n      <td>0.7235</td>\n      <td>1.1928</td>\n      <td>0.6588</td>\n      <td>1.3228</td>\n      <td>5.4845</td>\n      <td>1.3181</td>\n      <td>6.9215</td>\n      <td>6.2406</td>\n      <td>7.7501</td>\n      <td>74.62</td>\n      <td>...</td>\n      <td>0.9029</td>\n      <td>29.390</td>\n      <td>31.16</td>\n      <td>289550.7506</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.04838</td>\n      <td>0.08363</td>\n    </tr>\n    <tr>\n      <th>2020-08-19</th>\n      <td>0.7234</td>\n      <td>1.1898</td>\n      <td>0.6607</td>\n      <td>1.3191</td>\n      <td>5.5045</td>\n      <td>1.3173</td>\n      <td>6.9192</td>\n      <td>6.2580</td>\n      <td>7.7500</td>\n      <td>74.85</td>\n      <td>...</td>\n      <td>0.9114</td>\n      <td>29.370</td>\n      <td>31.25</td>\n      <td>291796.8124</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.04925</td>\n      <td>0.08463</td>\n    </tr>\n    <tr>\n      <th>2020-08-20</th>\n      <td>0.7178</td>\n      <td>1.1862</td>\n      <td>0.6519</td>\n      <td>1.3190</td>\n      <td>5.6370</td>\n      <td>1.3177</td>\n      <td>6.9143</td>\n      <td>6.2783</td>\n      <td>7.7500</td>\n      <td>75.01</td>\n      <td>...</td>\n      <td>0.9082</td>\n      <td>29.420</td>\n      <td>31.41</td>\n      <td>296027.2710</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05063</td>\n      <td>0.08300</td>\n    </tr>\n    <tr>\n      <th>2020-08-21</th>\n      <td>0.7156</td>\n      <td>1.1775</td>\n      <td>0.6533</td>\n      <td>1.3098</td>\n      <td>5.5949</td>\n      <td>1.3201</td>\n      <td>6.9179</td>\n      <td>6.3235</td>\n      <td>7.7502</td>\n      <td>74.92</td>\n      <td>...</td>\n      <td>0.9131</td>\n      <td>29.400</td>\n      <td>31.56</td>\n      <td>302779.1537</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05150</td>\n      <td>0.08175</td>\n    </tr>\n  </tbody>\n</table>\n<p>4920 rows × 29 columns</p>\n</div>"}, "metadata": {}, "execution_count": 2}], "source": ["# read the dataframe and assign the time column as the index values of the dataframe\n", "file = \"./DATA/Merged.csv\"\n", "df = pd.read_csv(file, index_col='Date', parse_dates=True)\n", "df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def CreateLaggedSequence(data, lag):\n", "    # make two lists for PredictorSequences and ResponseVariables\n", "    PredictorSequences, ResponseVariables = list(), list()\n", "    for i in range(len(data)):\n", "        # mark the range of the sequence\n", "        end_i = i + lag\n", "        # check when the data ends\n", "        if end_i+1 > len(data):\n", "            # stop sequence creation\n", "            break\n", "        # get the predictors and responses\n", "        PredictorSequence = data[i:end_i]\n", "        ResponseVariable = data[end_i]\n", "        # append them to the lists\n", "        PredictorSequences.append(PredictorSequence)\n", "        ResponseVariables.append(ResponseVariable)\n", "        # print(end_i)\n", "    return np.array(PredictorSequences), np.array(ResponseVariables)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["((4870, 50, 1), (4870,), 4870, 4870)"]}, "metadata": {}, "execution_count": 4}], "source": ["# Get the univariate series\n", "X = df['GBP']\n", "np_X = np.array(X)\n", "# Create the lagged values for the series (50 lags)\n", "X, y = CreateLaggedSequence(np_X, 50) #25\n", "lag = X.shape[1]\n", "NumberOfFeatures = 1 # for univariate input\n", "# reshape it for the process\n", "X = X.reshape(X.shape[0], X.shape[1], NumberOfFeatures) # a reshape is needed for the CNN architecture\n", "X.shape, y.shape, len(X), len(y)"]}, {"cell_type": "code", "metadata": {"id": "XtQxzkvSmKVF", "colab_type": "code", "colab": {}}, "source": ["# split the train and test sets (last 50 observation spared for the test)\n", "x_train, x_test = X[:-50], X[-50:]\n", "y_train, y_test = y[:-50], y[-50:]\n", "x_train.shape, x_test.shape, y_train.shape, y_test.shape"], "execution_count": 5, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["((4820, 50, 1), (50, 50, 1), (4820,), (50,))"]}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "code", "metadata": {"id": "ZDG6UWcamNyO", "colab_type": "code", "colab": {}}, "source": ["# define new activation function\n", "def swish(x, beta = 1):\n", "    return (x * sigmoid(beta * x))\n", "\n", "from keras.utils.generic_utils import get_custom_objects\n", "from keras.layers import Activation\n", "get_custom_objects().update({'swish': Activation(swish)})"], "execution_count": 6, "outputs": []}, {"cell_type": "code", "execution_count": 7, "metadata": {"tags": []}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Model: \"sequential_1\"\n", "_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "conv1d_1 (Conv1D)            (None, 50, 233)           932       \n", "_________________________________________________________________\n", "conv1d_2 (Conv1D)            (None, 50, 233)           163100    \n", "_________________________________________________________________\n", "flatten_1 (Flatten)          (None, 11650)             0         \n", "_________________________________________________________________\n", "dense_1 (<PERSON><PERSON>)              (None, 768)               8947968   \n", "_________________________________________________________________\n", "dropout_1 (Dropout)          (None, 768)               0         \n", "_________________________________________________________________\n", "dense_2 (<PERSON><PERSON>)              (None, 1)                 769       \n", "=================================================================\n", "Total params: 9,112,769\n", "Trainable params: 9,112,769\n", "Non-trainable params: 0\n", "_________________________________________________________________\n", "WARNING:tensorflow:From /home/<USER>/.local/lib/python3.6/site-packages/keras/backend/tensorflow_backend.py:422: The name tf.global_variables is deprecated. Please use tf.compat.v1.global_variables instead.\n", "\n", "Train on 3856 samples, validate on 964 samples\n", "Epoch 1/1024\n", "3856/3856 [==============================] - 2s 518us/step - loss: 2.9751 - mae: 1.7111 - val_loss: 1.1760 - val_mae: 1.0836\n", "Epoch 2/1024\n", "3856/3856 [==============================] - 2s 469us/step - loss: 1.9043 - mae: 1.3575 - val_loss: 0.4876 - val_mae: 0.6976\n", "Epoch 3/1024\n", "3856/3856 [==============================] - 2s 466us/step - loss: 0.7643 - mae: 0.8262 - val_loss: 0.0564 - val_mae: 0.2357\n", "Epoch 4/1024\n", "3856/3856 [==============================] - 2s 463us/step - loss: 0.1451 - mae: 0.3102 - val_loss: 0.0396 - val_mae: 0.1958\n", "Epoch 5/1024\n", "3856/3856 [==============================] - 2s 465us/step - loss: 0.2280 - mae: 0.3920 - val_loss: 0.1996 - val_mae: 0.4448\n", "Epoch 6/1024\n", "3856/3856 [==============================] - 2s 456us/step - loss: 0.5242 - mae: 0.6459 - val_loss: 0.2244 - val_mae: 0.4718\n", "Epoch 7/1024\n", "3856/3856 [==============================] - 2s 458us/step - loss: 0.5762 - mae: 0.6830 - val_loss: 0.1185 - val_mae: 0.3421\n", "Epoch 8/1024\n", "3856/3856 [==============================] - 2s 462us/step - loss: 0.3626 - mae: 0.5194 - val_loss: 0.0202 - val_mae: 0.1378\n", "Epoch 9/1024\n", "3856/3856 [==============================] - 2s 458us/step - loss: 0.1541 - mae: 0.3152 - val_loss: 0.0063 - val_mae: 0.0737\n", "Epoch 10/1024\n", "3856/3856 [==============================] - 2s 454us/step - loss: 0.0967 - mae: 0.2473 - val_loss: 0.0582 - val_mae: 0.2396\n", "Epoch 11/1024\n", "3856/3856 [==============================] - 2s 460us/step - loss: 0.1451 - mae: 0.3120 - val_loss: 0.1109 - val_mae: 0.3318\n", "Epoch 12/1024\n", "3856/3856 [==============================] - 2s 459us/step - loss: 0.2146 - mae: 0.3922 - val_loss: 0.1197 - val_mae: 0.3448\n", "Epoch 13/1024\n", "3856/3856 [==============================] - 2s 457us/step - loss: 0.2259 - mae: 0.4040 - val_loss: 0.0869 - val_mae: 0.2933\n", "Epoch 14/1024\n", "3856/3856 [==============================] - 2s 460us/step - loss: 0.1775 - mae: 0.3517 - val_loss: 0.0406 - val_mae: 0.1994\n", "Epoch 15/1024\n", "3856/3856 [==============================] - 2s 456us/step - loss: 0.1159 - mae: 0.2761 - val_loss: 0.0085 - val_mae: 0.0875\n", "Epoch 16/1024\n", "3856/3856 [==============================] - 2s 465us/step - loss: 0.0923 - mae: 0.2434 - val_loss: 0.0012 - val_mae: 0.0265\n", "Epoch 17/1024\n", "3856/3856 [==============================] - 2s 466us/step - loss: 0.1074 - mae: 0.2598 - val_loss: 0.0067 - val_mae: 0.0746\n", "Epoch 18/1024\n", "3856/3856 [==============================] - 2s 467us/step - loss: 0.1255 - mae: 0.2821 - val_loss: 0.0082 - val_mae: 0.0845\n", "Epoch 19/1024\n", "3856/3856 [==============================] - 2s 459us/step - loss: 0.1247 - mae: 0.2804 - val_loss: 0.0035 - val_mae: 0.0509\n", "Epoch 20/1024\n", "3856/3856 [==============================] - 2s 457us/step - loss: 0.1156 - mae: 0.2681 - val_loss: 0.0012 - val_mae: 0.0268\n", "Epoch 21/1024\n", "3856/3856 [==============================] - 2s 457us/step - loss: 0.0954 - mae: 0.2447 - val_loss: 0.0069 - val_mae: 0.0780\n", "Epoch 22/1024\n", "3856/3856 [==============================] - 2s 468us/step - loss: 0.0840 - mae: 0.2324 - val_loss: 0.0167 - val_mae: 0.1257\n", "Epoch 23/1024\n", "3856/3856 [==============================] - 2s 460us/step - loss: 0.0915 - mae: 0.2418 - val_loss: 0.0228 - val_mae: 0.1479\n", "Epoch 24/1024\n", "3856/3856 [==============================] - 2s 459us/step - loss: 0.0950 - mae: 0.2488 - val_loss: 0.0216 - val_mae: 0.1439\n", "Epoch 25/1024\n", "3856/3856 [==============================] - 2s 460us/step - loss: 0.0980 - mae: 0.2530 - val_loss: 0.0141 - val_mae: 0.1149\n", "Epoch 26/1024\n", "3856/3856 [==============================] - 2s 458us/step - loss: 0.0871 - mae: 0.2349 - val_loss: 0.0067 - val_mae: 0.0764\n", "Epoch 27/1024\n", "3856/3856 [==============================] - 2s 466us/step - loss: 0.0840 - mae: 0.2296 - val_loss: 0.0027 - val_mae: 0.0456\n", "Epoch 28/1024\n", "3856/3856 [==============================] - 2s 459us/step - loss: 0.0870 - mae: 0.2355 - val_loss: 0.0014 - val_mae: 0.0298\n", "Epoch 29/1024\n", "3856/3856 [==============================] - 2s 463us/step - loss: 0.0897 - mae: 0.2360 - val_loss: 0.0012 - val_mae: 0.0279\n", "Epoch 30/1024\n", "3856/3856 [==============================] - 2s 462us/step - loss: 0.0876 - mae: 0.2360 - val_loss: 0.0018 - val_mae: 0.0353\n", "Epoch 31/1024\n", "3856/3856 [==============================] - 2s 462us/step - loss: 0.0853 - mae: 0.2314 - val_loss: 0.0035 - val_mae: 0.0533\n", "Epoch 32/1024\n", "3856/3856 [==============================] - 2s 462us/step - loss: 0.0847 - mae: 0.2320 - val_loss: 0.0066 - val_mae: 0.0761\n", "Epoch 33/1024\n", "3856/3856 [==============================] - 2s 463us/step - loss: 0.0803 - mae: 0.2276 - val_loss: 0.0105 - val_mae: 0.0983\n", "Epoch 34/1024\n", "3856/3856 [==============================] - 2s 461us/step - loss: 0.0847 - mae: 0.2305 - val_loss: 0.0123 - val_mae: 0.1067\n", "Epoch 35/1024\n", "3856/3856 [==============================] - 2s 456us/step - loss: 0.0820 - mae: 0.2309 - val_loss: 0.0104 - val_mae: 0.0978\n", "Epoch 36/1024\n", "3856/3856 [==============================] - 2s 457us/step - loss: 0.0806 - mae: 0.2261 - val_loss: 0.0074 - val_mae: 0.0809\n", "Epoch 37/1024\n", "3856/3856 [==============================] - 2s 457us/step - loss: 0.0797 - mae: 0.2248 - val_loss: 0.0051 - val_mae: 0.0653\n", "Epoch 38/1024\n", "3856/3856 [==============================] - 2s 456us/step - loss: 0.0787 - mae: 0.2224 - val_loss: 0.0039 - val_mae: 0.0563\n", "Epoch 39/1024\n", "3856/3856 [==============================] - 2s 458us/step - loss: 0.0805 - mae: 0.2265 - val_loss: 0.0034 - val_mae: 0.0525\n", "Epoch 40/1024\n", "3856/3856 [==============================] - 2s 469us/step - loss: 0.0792 - mae: 0.2237 - val_loss: 0.0035 - val_mae: 0.0534\n", "Epoch 41/1024\n", "3856/3856 [==============================] - 2s 478us/step - loss: 0.0798 - mae: 0.2243 - val_loss: 0.0040 - val_mae: 0.0570\n", "Epoch 42/1024\n", "3856/3856 [==============================] - 2s 475us/step - loss: 0.0762 - mae: 0.2207 - val_loss: 0.0043 - val_mae: 0.0600\n", "Epoch 43/1024\n", "3856/3856 [==============================] - 2s 475us/step - loss: 0.0755 - mae: 0.2169 - val_loss: 0.0047 - val_mae: 0.0624\n", "Epoch 44/1024\n", "3856/3856 [==============================] - 2s 475us/step - loss: 0.0747 - mae: 0.2173 - val_loss: 0.0054 - val_mae: 0.0678\n", "Epoch 45/1024\n", "3856/3856 [==============================] - 2s 468us/step - loss: 0.0737 - mae: 0.2158 - val_loss: 0.0066 - val_mae: 0.0761\n", "Epoch 46/1024\n", "3856/3856 [==============================] - 2s 476us/step - loss: 0.0755 - mae: 0.2193 - val_loss: 0.0073 - val_mae: 0.0805\n", "Epoch 47/1024\n", "3856/3856 [==============================] - 2s 487us/step - loss: 0.0706 - mae: 0.2127 - val_loss: 0.0070 - val_mae: 0.0785\n", "Epoch 48/1024\n", "3856/3856 [==============================] - 2s 501us/step - loss: 0.0704 - mae: 0.2128 - val_loss: 0.0060 - val_mae: 0.0722\n", "Epoch 49/1024\n", "3856/3856 [==============================] - 2s 507us/step - loss: 0.0721 - mae: 0.2129 - val_loss: 0.0048 - val_mae: 0.0637\n", "Epoch 50/1024\n", "3856/3856 [==============================] - 2s 493us/step - loss: 0.0710 - mae: 0.2139 - val_loss: 0.0041 - val_mae: 0.0583\n", "Epoch 51/1024\n", "3856/3856 [==============================] - 2s 505us/step - loss: 0.0697 - mae: 0.2093 - val_loss: 0.0038 - val_mae: 0.0554\n", "Epoch 52/1024\n", "3856/3856 [==============================] - 2s 474us/step - loss: 0.0709 - mae: 0.2118 - val_loss: 0.0039 - val_mae: 0.0566\n", "Epoch 53/1024\n", "3856/3856 [==============================] - 2s 460us/step - loss: 0.0687 - mae: 0.2076 - val_loss: 0.0046 - val_mae: 0.0619\n", "Epoch 54/1024\n", "3856/3856 [==============================] - 2s 476us/step - loss: 0.0736 - mae: 0.2162 - val_loss: 0.0058 - val_mae: 0.0706\n", "Epoch 55/1024\n", "3856/3856 [==============================] - 2s 474us/step - loss: 0.0684 - mae: 0.2082 - val_loss: 0.0074 - val_mae: 0.0809\n", "Epoch 56/1024\n", "3856/3856 [==============================] - 2s 478us/step - loss: 0.0669 - mae: 0.2048 - val_loss: 0.0086 - val_mae: 0.0882\n", "Epoch 57/1024\n", "3856/3856 [==============================] - 2s 480us/step - loss: 0.0682 - mae: 0.2083 - val_loss: 0.0083 - val_mae: 0.0865\n", "Epoch 58/1024\n", "3856/3856 [==============================] - 2s 474us/step - loss: 0.0671 - mae: 0.2048 - val_loss: 0.0070 - val_mae: 0.0783\n", "Epoch 59/1024\n", "3856/3856 [==============================] - 2s 476us/step - loss: 0.0630 - mae: 0.1991 - val_loss: 0.0050 - val_mae: 0.0649\n", "Epoch 60/1024\n", "3856/3856 [==============================] - 2s 485us/step - loss: 0.0668 - mae: 0.2049 - val_loss: 0.0035 - val_mae: 0.0530\n", "Epoch 61/1024\n", "3856/3856 [==============================] - 2s 476us/step - loss: 0.0642 - mae: 0.2005 - val_loss: 0.0031 - val_mae: 0.0497\n", "Epoch 62/1024\n", "3856/3856 [==============================] - 2s 475us/step - loss: 0.0654 - mae: 0.2039 - val_loss: 0.0037 - val_mae: 0.0551\n", "Epoch 63/1024\n", "3856/3856 [==============================] - 2s 461us/step - loss: 0.0633 - mae: 0.2010 - val_loss: 0.0049 - val_mae: 0.0641\n", "Epoch 64/1024\n", "3856/3856 [==============================] - 2s 464us/step - loss: 0.0597 - mae: 0.1935 - val_loss: 0.0059 - val_mae: 0.0711\n", "Epoch 65/1024\n", "3856/3856 [==============================] - 2s 462us/step - loss: 0.0595 - mae: 0.1938 - val_loss: 0.0064 - val_mae: 0.0746\n", "Epoch 66/1024\n", "3856/3856 [==============================] - 2s 462us/step - loss: 0.0608 - mae: 0.1963 - val_loss: 0.0070 - val_mae: 0.0783\n", "Epoch 67/1024\n", "3856/3856 [==============================] - 2s 463us/step - loss: 0.0618 - mae: 0.1974 - val_loss: 0.0074 - val_mae: 0.0812\n", "Epoch 68/1024\n", "3856/3856 [==============================] - 2s 457us/step - loss: 0.0596 - mae: 0.1952 - val_loss: 0.0075 - val_mae: 0.0818\n", "Epoch 69/1024\n", "3856/3856 [==============================] - 2s 461us/step - loss: 0.0587 - mae: 0.1934 - val_loss: 0.0068 - val_mae: 0.0774\n", "Epoch 70/1024\n", "3856/3856 [==============================] - 2s 459us/step - loss: 0.0587 - mae: 0.1944 - val_loss: 0.0057 - val_mae: 0.0696\n", "Epoch 71/1024\n", "3856/3856 [==============================] - 2s 468us/step - loss: 0.0566 - mae: 0.1890 - val_loss: 0.0047 - val_mae: 0.0626\n", "Epoch 72/1024\n", "3856/3856 [==============================] - 2s 471us/step - loss: 0.0593 - mae: 0.1930 - val_loss: 0.0040 - val_mae: 0.0575\n", "Epoch 73/1024\n", "3856/3856 [==============================] - 2s 472us/step - loss: 0.0551 - mae: 0.1873 - val_loss: 0.0036 - val_mae: 0.0537\n", "Epoch 74/1024\n", "3856/3856 [==============================] - 2s 467us/step - loss: 0.0561 - mae: 0.1873 - val_loss: 0.0039 - val_mae: 0.0566\n", "Epoch 75/1024\n", "3856/3856 [==============================] - 2s 459us/step - loss: 0.0549 - mae: 0.1865 - val_loss: 0.0049 - val_mae: 0.0644\n", "Epoch 76/1024\n", "3856/3856 [==============================] - 2s 466us/step - loss: 0.0536 - mae: 0.1838 - val_loss: 0.0064 - val_mae: 0.0747\n", "Epoch 77/1024\n", "3856/3856 [==============================] - 2s 467us/step - loss: 0.0527 - mae: 0.1829 - val_loss: 0.0078 - val_mae: 0.0832\n", "Epoch 78/1024\n", "3856/3856 [==============================] - 2s 475us/step - loss: 0.0548 - mae: 0.1864 - val_loss: 0.0079 - val_mae: 0.0841\n", "Epoch 79/1024\n", "3856/3856 [==============================] - 2s 472us/step - loss: 0.0529 - mae: 0.1847 - val_loss: 0.0068 - val_mae: 0.0776\n", "Epoch 80/1024\n", "3856/3856 [==============================] - 2s 470us/step - loss: 0.0525 - mae: 0.1818 - val_loss: 0.0053 - val_mae: 0.0670\n", "50/50 [==============================] - 0s 312us/step\n", "test_acc: 0.025741634890437126\n", "Total time: 145.3722367286682 seconds\n"]}], "source": ["# create a start point for timer\n", "start = time.time()\n", "\n", "# design the model\n", "backend.clear_session()\n", "model = Sequential()\n", "\n", "model.add(Conv1D(filters=233, kernel_size=3, padding='same', activation=tf.keras.backend.sin, input_shape=(lag, NumberOfFeatures)))\n", "model.add(Conv1D(filters=233, kernel_size=3, padding='same', activation=tf.keras.backend.sin))\n", "\n", "model.add(<PERSON><PERSON>())\n", "\n", "model.add(layers.Dense(3*2**8, activation=tf.keras.backend.sin, kernel_initializer='he_uniform'))\n", "model.add(Dropout(0.5))\n", "\n", "model.add(layers.Dense(1, activation='swish'))\n", "\n", "# compile the model\n", "model.compile(optimizer = optimizers.<PERSON>(lr=0.000007), loss = 'mse', metrics = ['mae'])\n", "#get the summary of the model\n", "model.summary()\n", "\n", "################################################################################\n", "\n", "# fit the model\n", "history = model.fit(x_train, y_train, \n", "            epochs = 2**10, \n", "            batch_size = 3615, \n", "            validation_split = 0.20,\n", "            verbose = 1, \n", "            callbacks=[EarlyStopping(monitor='val_mae', patience=2**6, restore_best_weights = True)]) # restore the best values\n", "\n", "#evaluate the model\n", "test_loss, test_acc = model.evaluate(x_test, y_test)\n", "print('test_acc:', test_acc)\n", "\n", "# end the timer and print the total time passed\n", "end = time.time()\n", "print(\"Total time:\", end-start, \"seconds\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"output_type": "display_data", "data": {"text/plain": "<Figure size 432x288 with 1 Axes>", "image/svg+xml": "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n<!-- Created with matplotlib (https://matplotlib.org/) -->\n<svg height=\"277.314375pt\" version=\"1.1\" viewBox=\"0 0 392.14375 277.314375\" width=\"392.14375pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n <defs>\n  <style type=\"text/css\">\n*{stroke-linecap:butt;stroke-linejoin:round;}\n  </style>\n </defs>\n <g id=\"figure_1\">\n  <g id=\"patch_1\">\n   <path d=\"M 0 277.314375 \nL 392.14375 277.314375 \nL 392.14375 0 \nL 0 0 \nz\n\" style=\"fill:none;\"/>\n  </g>\n  <g id=\"axes_1\">\n   <g id=\"patch_2\">\n    <path d=\"M 50.14375 239.758125 \nL 384.94375 239.758125 \nL 384.94375 22.318125 \nL 50.14375 22.318125 \nz\n\" style=\"fill:#ffffff;\"/>\n   </g>\n   <g id=\"matplotlib.axis_1\">\n    <g id=\"xtick_1\">\n     <g id=\"line2d_1\">\n      <defs>\n       <path d=\"M 0 0 \nL 0 3.5 \n\" id=\"m353e10e234\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"61.509228\" xlink:href=\"#m353e10e234\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_1\">\n      <!-- 0 -->\n      <defs>\n       <path d=\"M 31.78125 66.40625 \nQ 24.171875 66.40625 20.328125 58.90625 \nQ 16.5 51.421875 16.5 36.375 \nQ 16.5 21.390625 20.328125 13.890625 \nQ 24.171875 6.390625 31.78125 6.390625 \nQ 39.453125 6.390625 43.28125 13.890625 \nQ 47.125 21.390625 47.125 36.375 \nQ 47.125 51.421875 43.28125 58.90625 \nQ 39.453125 66.40625 31.78125 66.40625 \nz\nM 31.78125 74.21875 \nQ 44.046875 74.21875 50.515625 64.515625 \nQ 56.984375 54.828125 56.984375 36.375 \nQ 56.984375 17.96875 50.515625 8.265625 \nQ 44.046875 -1.421875 31.78125 -1.421875 \nQ 19.53125 -1.421875 13.0625 8.265625 \nQ 6.59375 17.96875 6.59375 36.375 \nQ 6.59375 54.828125 13.0625 64.515625 \nQ 19.53125 74.21875 31.78125 74.21875 \nz\n\" id=\"DejaVuSans-48\"/>\n      </defs>\n      <g transform=\"translate(58.327978 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_2\">\n     <g id=\"line2d_2\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"100.03627\" xlink:href=\"#m353e10e234\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_2\">\n      <!-- 10 -->\n      <defs>\n       <path d=\"M 12.40625 8.296875 \nL 28.515625 8.296875 \nL 28.515625 63.921875 \nL 10.984375 60.40625 \nL 10.984375 69.390625 \nL 28.421875 72.90625 \nL 38.28125 72.90625 \nL 38.28125 8.296875 \nL 54.390625 8.296875 \nL 54.390625 0 \nL 12.40625 0 \nz\n\" id=\"DejaVuSans-49\"/>\n      </defs>\n      <g transform=\"translate(93.67377 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_3\">\n     <g id=\"line2d_3\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"138.563313\" xlink:href=\"#m353e10e234\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_3\">\n      <!-- 20 -->\n      <defs>\n       <path d=\"M 19.1875 8.296875 \nL 53.609375 8.296875 \nL 53.609375 0 \nL 7.328125 0 \nL 7.328125 8.296875 \nQ 12.9375 14.109375 22.625 23.890625 \nQ 32.328125 33.6875 34.8125 36.53125 \nQ 39.546875 41.84375 41.421875 45.53125 \nQ 43.3125 49.21875 43.3125 52.78125 \nQ 43.3125 58.59375 39.234375 62.25 \nQ 35.15625 65.921875 28.609375 65.921875 \nQ 23.96875 65.921875 18.8125 64.3125 \nQ 13.671875 62.703125 7.8125 59.421875 \nL 7.8125 69.390625 \nQ 13.765625 71.78125 18.9375 73 \nQ 24.125 74.21875 28.421875 74.21875 \nQ 39.75 74.21875 46.484375 68.546875 \nQ 53.21875 62.890625 53.21875 53.421875 \nQ 53.21875 48.921875 51.53125 44.890625 \nQ 49.859375 40.875 45.40625 35.40625 \nQ 44.1875 33.984375 37.640625 27.21875 \nQ 31.109375 20.453125 19.1875 8.296875 \nz\n\" id=\"DejaVuSans-50\"/>\n      </defs>\n      <g transform=\"translate(132.200813 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_4\">\n     <g id=\"line2d_4\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"177.090355\" xlink:href=\"#m353e10e234\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_4\">\n      <!-- 30 -->\n      <defs>\n       <path d=\"M 40.578125 39.3125 \nQ 47.65625 37.796875 51.625 33 \nQ 55.609375 28.21875 55.609375 21.1875 \nQ 55.609375 10.40625 48.1875 4.484375 \nQ 40.765625 -1.421875 27.09375 -1.421875 \nQ 22.515625 -1.421875 17.65625 -0.515625 \nQ 12.796875 0.390625 7.625 2.203125 \nL 7.625 11.71875 \nQ 11.71875 9.328125 16.59375 8.109375 \nQ 21.484375 6.890625 26.8125 6.890625 \nQ 36.078125 6.890625 40.9375 10.546875 \nQ 45.796875 14.203125 45.796875 21.1875 \nQ 45.796875 27.640625 41.28125 31.265625 \nQ 36.765625 34.90625 28.71875 34.90625 \nL 20.21875 34.90625 \nL 20.21875 43.015625 \nL 29.109375 43.015625 \nQ 36.375 43.015625 40.234375 45.921875 \nQ 44.09375 48.828125 44.09375 54.296875 \nQ 44.09375 59.90625 40.109375 62.90625 \nQ 36.140625 65.921875 28.71875 65.921875 \nQ 24.65625 65.921875 20.015625 65.03125 \nQ 15.375 64.15625 9.8125 62.3125 \nL 9.8125 71.09375 \nQ 15.4375 72.65625 20.34375 73.4375 \nQ 25.25 74.21875 29.59375 74.21875 \nQ 40.828125 74.21875 47.359375 69.109375 \nQ 53.90625 64.015625 53.90625 55.328125 \nQ 53.90625 49.265625 50.4375 45.09375 \nQ 46.96875 40.921875 40.578125 39.3125 \nz\n\" id=\"DejaVuSans-51\"/>\n      </defs>\n      <g transform=\"translate(170.727855 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-51\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_5\">\n     <g id=\"line2d_5\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"215.617398\" xlink:href=\"#m353e10e234\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_5\">\n      <!-- 40 -->\n      <defs>\n       <path d=\"M 37.796875 64.3125 \nL 12.890625 25.390625 \nL 37.796875 25.390625 \nz\nM 35.203125 72.90625 \nL 47.609375 72.90625 \nL 47.609375 25.390625 \nL 58.015625 25.390625 \nL 58.015625 17.1875 \nL 47.609375 17.1875 \nL 47.609375 0 \nL 37.796875 0 \nL 37.796875 17.1875 \nL 4.890625 17.1875 \nL 4.890625 26.703125 \nz\n\" id=\"DejaVuSans-52\"/>\n      </defs>\n      <g transform=\"translate(209.254898 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-52\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_6\">\n     <g id=\"line2d_6\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"254.14444\" xlink:href=\"#m353e10e234\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_6\">\n      <!-- 50 -->\n      <defs>\n       <path d=\"M 10.796875 72.90625 \nL 49.515625 72.90625 \nL 49.515625 64.59375 \nL 19.828125 64.59375 \nL 19.828125 46.734375 \nQ 21.96875 47.46875 24.109375 47.828125 \nQ 26.265625 48.1875 28.421875 48.1875 \nQ 40.625 48.1875 47.75 41.5 \nQ 54.890625 34.8125 54.890625 23.390625 \nQ 54.890625 11.625 47.5625 5.09375 \nQ 40.234375 -1.421875 26.90625 -1.421875 \nQ 22.3125 -1.421875 17.546875 -0.640625 \nQ 12.796875 0.140625 7.71875 1.703125 \nL 7.71875 11.625 \nQ 12.109375 9.234375 16.796875 8.0625 \nQ 21.484375 6.890625 26.703125 6.890625 \nQ 35.15625 6.890625 40.078125 11.328125 \nQ 45.015625 15.765625 45.015625 23.390625 \nQ 45.015625 31 40.078125 35.4375 \nQ 35.15625 39.890625 26.703125 39.890625 \nQ 22.75 39.890625 18.8125 39.015625 \nQ 14.890625 38.140625 10.796875 36.28125 \nz\n\" id=\"DejaVuSans-53\"/>\n      </defs>\n      <g transform=\"translate(247.78194 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-53\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_7\">\n     <g id=\"line2d_7\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"292.671483\" xlink:href=\"#m353e10e234\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_7\">\n      <!-- 60 -->\n      <defs>\n       <path d=\"M 33.015625 40.375 \nQ 26.375 40.375 22.484375 35.828125 \nQ 18.609375 31.296875 18.609375 23.390625 \nQ 18.609375 15.53125 22.484375 10.953125 \nQ 26.375 6.390625 33.015625 6.390625 \nQ 39.65625 6.390625 43.53125 10.953125 \nQ 47.40625 15.53125 47.40625 23.390625 \nQ 47.40625 31.296875 43.53125 35.828125 \nQ 39.65625 40.375 33.015625 40.375 \nz\nM 52.59375 71.296875 \nL 52.59375 62.3125 \nQ 48.875 64.0625 45.09375 64.984375 \nQ 41.3125 65.921875 37.59375 65.921875 \nQ 27.828125 65.921875 22.671875 59.328125 \nQ 17.53125 52.734375 16.796875 39.40625 \nQ 19.671875 43.65625 24.015625 45.921875 \nQ 28.375 48.1875 33.59375 48.1875 \nQ 44.578125 48.1875 50.953125 41.515625 \nQ 57.328125 34.859375 57.328125 23.390625 \nQ 57.328125 12.15625 50.6875 5.359375 \nQ 44.046875 -1.421875 33.015625 -1.421875 \nQ 20.359375 -1.421875 13.671875 8.265625 \nQ 6.984375 17.96875 6.984375 36.375 \nQ 6.984375 53.65625 15.1875 63.9375 \nQ 23.390625 74.21875 37.203125 74.21875 \nQ 40.921875 74.21875 44.703125 73.484375 \nQ 48.484375 72.75 52.59375 71.296875 \nz\n\" id=\"DejaVuSans-54\"/>\n      </defs>\n      <g transform=\"translate(286.308983 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_8\">\n     <g id=\"line2d_8\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"331.198526\" xlink:href=\"#m353e10e234\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_8\">\n      <!-- 70 -->\n      <defs>\n       <path d=\"M 8.203125 72.90625 \nL 55.078125 72.90625 \nL 55.078125 68.703125 \nL 28.609375 0 \nL 18.3125 0 \nL 43.21875 64.59375 \nL 8.203125 64.59375 \nz\n\" id=\"DejaVuSans-55\"/>\n      </defs>\n      <g transform=\"translate(324.836026 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_9\">\n     <g id=\"line2d_9\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"369.725568\" xlink:href=\"#m353e10e234\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_9\">\n      <!-- 80 -->\n      <defs>\n       <path d=\"M 31.78125 34.625 \nQ 24.75 34.625 20.71875 30.859375 \nQ 16.703125 27.09375 16.703125 20.515625 \nQ 16.703125 13.921875 20.71875 10.15625 \nQ 24.75 6.390625 31.78125 6.390625 \nQ 38.8125 6.390625 42.859375 10.171875 \nQ 46.921875 13.96875 46.921875 20.515625 \nQ 46.921875 27.09375 42.890625 30.859375 \nQ 38.875 34.625 31.78125 34.625 \nz\nM 21.921875 38.8125 \nQ 15.578125 40.375 12.03125 44.71875 \nQ 8.5 49.078125 8.5 55.328125 \nQ 8.5 64.0625 14.71875 69.140625 \nQ 20.953125 74.21875 31.78125 74.21875 \nQ 42.671875 74.21875 48.875 69.140625 \nQ 55.078125 64.0625 55.078125 55.328125 \nQ 55.078125 49.078125 51.53125 44.71875 \nQ 48 40.375 41.703125 38.8125 \nQ 48.828125 37.15625 52.796875 32.3125 \nQ 56.78125 27.484375 56.78125 20.515625 \nQ 56.78125 9.90625 50.3125 4.234375 \nQ 43.84375 -1.421875 31.78125 -1.421875 \nQ 19.734375 -1.421875 13.25 4.234375 \nQ 6.78125 9.90625 6.78125 20.515625 \nQ 6.78125 27.484375 10.78125 32.3125 \nQ 14.796875 37.15625 21.921875 38.8125 \nz\nM 18.3125 54.390625 \nQ 18.3125 48.734375 21.84375 45.5625 \nQ 25.390625 42.390625 31.78125 42.390625 \nQ 38.140625 42.390625 41.71875 45.5625 \nQ 45.3125 48.734375 45.3125 54.390625 \nQ 45.3125 60.0625 41.71875 63.234375 \nQ 38.140625 66.40625 31.78125 66.40625 \nQ 25.390625 66.40625 21.84375 63.234375 \nQ 18.3125 60.0625 18.3125 54.390625 \nz\n\" id=\"DejaVuSans-56\"/>\n      </defs>\n      <g transform=\"translate(363.363068 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"text_10\">\n     <!-- Epochs -->\n     <defs>\n      <path d=\"M 9.8125 72.90625 \nL 55.90625 72.90625 \nL 55.90625 64.59375 \nL 19.671875 64.59375 \nL 19.671875 43.015625 \nL 54.390625 43.015625 \nL 54.390625 34.71875 \nL 19.671875 34.71875 \nL 19.671875 8.296875 \nL 56.78125 8.296875 \nL 56.78125 0 \nL 9.8125 0 \nz\n\" id=\"DejaVuSans-69\"/>\n      <path d=\"M 18.109375 8.203125 \nL 18.109375 -20.796875 \nL 9.078125 -20.796875 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.390625 \nQ 20.953125 51.265625 25.265625 53.625 \nQ 29.59375 56 35.59375 56 \nQ 45.5625 56 51.78125 48.09375 \nQ 58.015625 40.1875 58.015625 27.296875 \nQ 58.015625 14.40625 51.78125 6.484375 \nQ 45.5625 -1.421875 35.59375 -1.421875 \nQ 29.59375 -1.421875 25.265625 0.953125 \nQ 20.953125 3.328125 18.109375 8.203125 \nz\nM 48.6875 27.296875 \nQ 48.6875 37.203125 44.609375 42.84375 \nQ 40.53125 48.484375 33.40625 48.484375 \nQ 26.265625 48.484375 22.1875 42.84375 \nQ 18.109375 37.203125 18.109375 27.296875 \nQ 18.109375 17.390625 22.1875 11.75 \nQ 26.265625 6.109375 33.40625 6.109375 \nQ 40.53125 6.109375 44.609375 11.75 \nQ 48.6875 17.390625 48.6875 27.296875 \nz\n\" id=\"DejaVuSans-112\"/>\n      <path d=\"M 30.609375 48.390625 \nQ 23.390625 48.390625 19.1875 42.75 \nQ 14.984375 37.109375 14.984375 27.296875 \nQ 14.984375 17.484375 19.15625 11.84375 \nQ 23.34375 6.203125 30.609375 6.203125 \nQ 37.796875 6.203125 41.984375 11.859375 \nQ 46.1875 17.53125 46.1875 27.296875 \nQ 46.1875 37.015625 41.984375 42.703125 \nQ 37.796875 48.390625 30.609375 48.390625 \nz\nM 30.609375 56 \nQ 42.328125 56 49.015625 48.375 \nQ 55.71875 40.765625 55.71875 27.296875 \nQ 55.71875 13.875 49.015625 6.21875 \nQ 42.328125 -1.421875 30.609375 -1.421875 \nQ 18.84375 -1.421875 12.171875 6.21875 \nQ 5.515625 13.875 5.515625 27.296875 \nQ 5.515625 40.765625 12.171875 48.375 \nQ 18.84375 56 30.609375 56 \nz\n\" id=\"DejaVuSans-111\"/>\n      <path d=\"M 48.78125 52.59375 \nL 48.78125 44.1875 \nQ 44.96875 46.296875 41.140625 47.34375 \nQ 37.3125 48.390625 33.40625 48.390625 \nQ 24.65625 48.390625 19.8125 42.84375 \nQ 14.984375 37.3125 14.984375 27.296875 \nQ 14.984375 17.28125 19.8125 11.734375 \nQ 24.65625 6.203125 33.40625 6.203125 \nQ 37.3125 6.203125 41.140625 7.25 \nQ 44.96875 8.296875 48.78125 10.40625 \nL 48.78125 2.09375 \nQ 45.015625 0.34375 40.984375 -0.53125 \nQ 36.96875 -1.421875 32.421875 -1.421875 \nQ 20.0625 -1.421875 12.78125 6.34375 \nQ 5.515625 14.109375 5.515625 27.296875 \nQ 5.515625 40.671875 12.859375 48.328125 \nQ 20.21875 56 33.015625 56 \nQ 37.15625 56 41.109375 55.140625 \nQ 45.0625 54.296875 48.78125 52.59375 \nz\n\" id=\"DejaVuSans-99\"/>\n      <path d=\"M 54.890625 33.015625 \nL 54.890625 0 \nL 45.90625 0 \nL 45.90625 32.71875 \nQ 45.90625 40.484375 42.875 44.328125 \nQ 39.84375 48.1875 33.796875 48.1875 \nQ 26.515625 48.1875 22.3125 43.546875 \nQ 18.109375 38.921875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 75.984375 \nL 18.109375 75.984375 \nL 18.109375 46.1875 \nQ 21.34375 51.125 25.703125 53.5625 \nQ 30.078125 56 35.796875 56 \nQ 45.21875 56 50.046875 50.171875 \nQ 54.890625 44.34375 54.890625 33.015625 \nz\n\" id=\"DejaVuSans-104\"/>\n      <path d=\"M 44.28125 53.078125 \nL 44.28125 44.578125 \nQ 40.484375 46.53125 36.375 47.5 \nQ 32.28125 48.484375 27.875 48.484375 \nQ 21.1875 48.484375 17.84375 46.4375 \nQ 14.5 44.390625 14.5 40.28125 \nQ 14.5 37.15625 16.890625 35.375 \nQ 19.28125 33.59375 26.515625 31.984375 \nL 29.59375 31.296875 \nQ 39.15625 29.25 43.1875 25.515625 \nQ 47.21875 21.78125 47.21875 15.09375 \nQ 47.21875 7.46875 41.1875 3.015625 \nQ 35.15625 -1.421875 24.609375 -1.421875 \nQ 20.21875 -1.421875 15.453125 -0.5625 \nQ 10.6875 0.296875 5.421875 2 \nL 5.421875 11.28125 \nQ 10.40625 8.6875 15.234375 7.390625 \nQ 20.0625 6.109375 24.8125 6.109375 \nQ 31.15625 6.109375 34.5625 8.28125 \nQ 37.984375 10.453125 37.984375 14.40625 \nQ 37.984375 18.0625 35.515625 20.015625 \nQ 33.0625 21.96875 24.703125 23.78125 \nL 21.578125 24.515625 \nQ 13.234375 26.265625 9.515625 29.90625 \nQ 5.8125 33.546875 5.8125 39.890625 \nQ 5.8125 47.609375 11.28125 51.796875 \nQ 16.75 56 26.8125 56 \nQ 31.78125 56 36.171875 55.265625 \nQ 40.578125 54.546875 44.28125 53.078125 \nz\n\" id=\"DejaVuSans-115\"/>\n     </defs>\n     <g transform=\"translate(199.628125 268.034687)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-69\"/>\n      <use x=\"63.183594\" xlink:href=\"#DejaVuSans-112\"/>\n      <use x=\"126.660156\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"187.841797\" xlink:href=\"#DejaVuSans-99\"/>\n      <use x=\"242.822266\" xlink:href=\"#DejaVuSans-104\"/>\n      <use x=\"306.201172\" xlink:href=\"#DejaVuSans-115\"/>\n     </g>\n    </g>\n   </g>\n   <g id=\"matplotlib.axis_2\">\n    <g id=\"ytick_1\">\n     <g id=\"line2d_10\">\n      <defs>\n       <path d=\"M 0 0 \nL -3.5 0 \n\" id=\"mcc63ffe6a5\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mcc63ffe6a5\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_11\">\n      <!-- 0.00 -->\n      <defs>\n       <path d=\"M 10.6875 12.40625 \nL 21 12.40625 \nL 21 0 \nL 10.6875 0 \nz\n\" id=\"DejaVuSans-46\"/>\n      </defs>\n      <g transform=\"translate(20.878125 243.557344)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_2\">\n     <g id=\"line2d_11\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mcc63ffe6a5\" y=\"192.48856\"/>\n      </g>\n     </g>\n     <g id=\"text_12\">\n      <!-- 0.05 -->\n      <g transform=\"translate(20.878125 196.287779)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-53\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_3\">\n     <g id=\"line2d_12\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mcc63ffe6a5\" y=\"145.218995\"/>\n      </g>\n     </g>\n     <g id=\"text_13\">\n      <!-- 0.10 -->\n      <g transform=\"translate(20.878125 149.018213)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_4\">\n     <g id=\"line2d_13\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mcc63ffe6a5\" y=\"97.949429\"/>\n      </g>\n     </g>\n     <g id=\"text_14\">\n      <!-- 0.15 -->\n      <g transform=\"translate(20.878125 101.748648)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-53\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_5\">\n     <g id=\"line2d_14\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mcc63ffe6a5\" y=\"50.679864\"/>\n      </g>\n     </g>\n     <g id=\"text_15\">\n      <!-- 0.20 -->\n      <g transform=\"translate(20.878125 54.479083)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"text_16\">\n     <!-- Loss -->\n     <defs>\n      <path d=\"M 9.8125 72.90625 \nL 19.671875 72.90625 \nL 19.671875 8.296875 \nL 55.171875 8.296875 \nL 55.171875 0 \nL 9.8125 0 \nz\n\" id=\"DejaVuSans-76\"/>\n     </defs>\n     <g transform=\"translate(14.798438 142.005312)rotate(-90)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-76\"/>\n      <use x=\"53.962891\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"115.144531\" xlink:href=\"#DejaVuSans-115\"/>\n      <use x=\"167.244141\" xlink:href=\"#DejaVuSans-115\"/>\n     </g>\n    </g>\n   </g>\n   <g id=\"line2d_15\">\n    <path clip-path=\"url(#pe055deb59b)\" d=\"M 76.238437 -1 \nL 76.920045 102.551762 \nL 80.772749 24.221419 \nL 81.119778 -1 \nM 94.324655 -1 \nL 96.183566 94.107705 \nL 100.03627 148.2953 \nL 103.888974 102.607877 \nL 107.741679 36.92248 \nL 111.594383 26.201897 \nL 115.447087 71.927232 \nL 119.299791 130.172547 \nL 123.152496 152.479945 \nL 127.0052 138.194423 \nL 130.857904 121.11299 \nL 134.710608 121.83008 \nL 138.563313 130.449816 \nL 142.416017 149.527725 \nL 146.268721 160.338701 \nL 150.121425 153.239712 \nL 153.97413 149.906617 \nL 157.826834 147.11782 \nL 161.679538 157.411599 \nL 165.532243 160.321825 \nL 169.384947 157.485532 \nL 173.237651 154.924182 \nL 177.090355 156.916192 \nL 180.94306 159.149789 \nL 184.795764 159.691781 \nL 188.648468 163.865743 \nL 192.501172 159.673207 \nL 196.353877 162.263712 \nL 200.206581 163.519775 \nL 204.059285 164.443048 \nL 207.911989 165.310541 \nL 211.764694 163.683899 \nL 215.617398 164.894373 \nL 219.470102 164.280822 \nL 223.322806 167.686579 \nL 227.175511 168.4024 \nL 231.028215 169.097406 \nL 234.880919 170.094626 \nL 238.733623 168.33574 \nL 242.586328 172.996257 \nL 246.439032 173.224111 \nL 250.291736 171.585591 \nL 254.14444 172.675764 \nL 257.997145 173.888963 \nL 261.849849 172.706719 \nL 265.702553 174.841516 \nL 269.555257 170.144641 \nL 273.407962 175.086489 \nL 277.260666 176.556796 \nL 281.11337 175.322328 \nL 284.966075 176.293608 \nL 288.818779 180.162895 \nL 292.671483 176.652006 \nL 296.524187 179.096385 \nL 300.376892 177.925836 \nL 304.229596 179.902927 \nL 308.0823 183.352379 \nL 311.935004 183.477828 \nL 315.787709 182.237409 \nL 319.640413 181.327338 \nL 323.493117 183.439852 \nL 327.345821 184.225816 \nL 331.198526 184.253776 \nL 335.05123 186.239125 \nL 338.903934 183.65065 \nL 342.756638 187.640556 \nL 346.609343 186.754275 \nL 350.462047 187.861569 \nL 354.314751 189.098815 \nL 358.167455 189.908578 \nL 362.02016 187.960638 \nL 365.872864 189.772301 \nL 369.725568 190.112557 \n\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;stroke-width:1.5;\"/>\n   </g>\n   <g id=\"line2d_16\">\n    <path clip-path=\"url(#pe055deb59b)\" d=\"M 71.295905 -1 \nL 73.06734 186.452758 \nL 76.920045 202.304288 \nL 80.772749 51.038245 \nL 84.625453 27.633787 \nL 88.478157 127.700107 \nL 92.330862 220.683804 \nL 96.183566 233.837047 \nL 100.03627 184.711925 \nL 103.888974 134.927556 \nL 107.741679 126.600358 \nL 111.594383 157.640407 \nL 115.447087 201.360311 \nL 119.299791 231.713554 \nL 123.152496 238.606187 \nL 127.0052 233.46389 \nL 130.857904 231.960726 \nL 134.710608 236.412241 \nL 138.563313 238.664275 \nL 142.416017 233.217486 \nL 146.268721 223.981174 \nL 150.121425 218.245546 \nL 153.97413 219.342244 \nL 157.826834 226.444301 \nL 161.679538 233.458515 \nL 165.532243 237.222658 \nL 169.384947 238.475056 \nL 173.237651 238.589608 \nL 177.090355 238.102805 \nL 180.94306 236.435836 \nL 184.795764 233.504097 \nL 188.648468 229.795256 \nL 192.501172 228.163936 \nL 196.353877 229.890618 \nL 200.206581 232.774666 \nL 204.059285 234.980817 \nL 207.911989 236.100911 \nL 211.764694 236.528134 \nL 215.617398 236.426364 \nL 219.470102 236.014414 \nL 223.322806 235.661747 \nL 227.175511 235.357046 \nL 231.028215 234.651816 \nL 234.880919 233.495016 \nL 238.733623 232.838747 \nL 242.586328 233.149377 \nL 246.439032 234.059453 \nL 250.291736 235.191588 \nL 254.14444 235.871038 \nL 257.997145 236.210797 \nL 261.849849 236.068326 \nL 265.702553 235.420081 \nL 269.555257 234.272701 \nL 273.407962 232.778078 \nL 277.260666 231.605371 \nL 281.11337 231.891542 \nL 284.966075 233.176861 \nL 288.818779 235.031069 \nL 292.671483 236.482081 \nL 296.524187 236.823957 \nL 300.376892 236.240903 \nL 304.229596 235.140763 \nL 308.0823 234.202985 \nL 311.935004 233.72605 \nL 315.787709 233.182367 \nL 319.640413 232.735825 \nL 323.493117 232.640679 \nL 327.345821 233.314673 \nL 331.198526 234.411375 \nL 335.05123 235.334246 \nL 338.903934 235.962314 \nL 342.756638 236.401857 \nL 346.609343 236.069673 \nL 350.462047 235.103035 \nL 354.314751 233.713563 \nL 358.167455 232.423204 \nL 362.02016 232.279432 \nL 365.872864 233.294061 \nL 369.725568 234.759545 \n\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;stroke-width:1.5;\"/>\n   </g>\n   <g id=\"patch_3\">\n    <path d=\"M 50.14375 239.758125 \nL 50.14375 22.318125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_4\">\n    <path d=\"M 384.94375 239.758125 \nL 384.94375 22.318125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_5\">\n    <path d=\"M 50.14375 239.758125 \nL 384.94375 239.758125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_6\">\n    <path d=\"M 50.14375 22.318125 \nL 384.94375 22.318125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"text_17\">\n    <!-- Training and validation loss -->\n    <defs>\n     <path d=\"M -0.296875 72.90625 \nL 61.375 72.90625 \nL 61.375 64.59375 \nL 35.5 64.59375 \nL 35.5 0 \nL 25.59375 0 \nL 25.59375 64.59375 \nL -0.296875 64.59375 \nz\n\" id=\"DejaVuSans-84\"/>\n     <path d=\"M 41.109375 46.296875 \nQ 39.59375 47.171875 37.8125 47.578125 \nQ 36.03125 48 33.890625 48 \nQ 26.265625 48 22.1875 43.046875 \nQ 18.109375 38.09375 18.109375 28.8125 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 20.953125 51.171875 25.484375 53.578125 \nQ 30.03125 56 36.53125 56 \nQ 37.453125 56 38.578125 55.875 \nQ 39.703125 55.765625 41.0625 55.515625 \nz\n\" id=\"DejaVuSans-114\"/>\n     <path d=\"M 34.28125 27.484375 \nQ 23.390625 27.484375 19.1875 25 \nQ 14.984375 22.515625 14.984375 16.5 \nQ 14.984375 11.71875 18.140625 8.90625 \nQ 21.296875 6.109375 26.703125 6.109375 \nQ 34.1875 6.109375 38.703125 11.40625 \nQ 43.21875 16.703125 43.21875 25.484375 \nL 43.21875 27.484375 \nz\nM 52.203125 31.203125 \nL 52.203125 0 \nL 43.21875 0 \nL 43.21875 8.296875 \nQ 40.140625 3.328125 35.546875 0.953125 \nQ 30.953125 -1.421875 24.3125 -1.421875 \nQ 15.921875 -1.421875 10.953125 3.296875 \nQ 6 8.015625 6 15.921875 \nQ 6 25.140625 12.171875 29.828125 \nQ 18.359375 34.515625 30.609375 34.515625 \nL 43.21875 34.515625 \nL 43.21875 35.40625 \nQ 43.21875 41.609375 39.140625 45 \nQ 35.0625 48.390625 27.6875 48.390625 \nQ 23 48.390625 18.546875 47.265625 \nQ 14.109375 46.140625 10.015625 43.890625 \nL 10.015625 52.203125 \nQ 14.9375 54.109375 19.578125 55.046875 \nQ 24.21875 56 28.609375 56 \nQ 40.484375 56 46.34375 49.84375 \nQ 52.203125 43.703125 52.203125 31.203125 \nz\n\" id=\"DejaVuSans-97\"/>\n     <path d=\"M 9.421875 54.6875 \nL 18.40625 54.6875 \nL 18.40625 0 \nL 9.421875 0 \nz\nM 9.421875 75.984375 \nL 18.40625 75.984375 \nL 18.40625 64.59375 \nL 9.421875 64.59375 \nz\n\" id=\"DejaVuSans-105\"/>\n     <path d=\"M 54.890625 33.015625 \nL 54.890625 0 \nL 45.90625 0 \nL 45.90625 32.71875 \nQ 45.90625 40.484375 42.875 44.328125 \nQ 39.84375 48.1875 33.796875 48.1875 \nQ 26.515625 48.1875 22.3125 43.546875 \nQ 18.109375 38.921875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 21.34375 51.125 25.703125 53.5625 \nQ 30.078125 56 35.796875 56 \nQ 45.21875 56 50.046875 50.171875 \nQ 54.890625 44.34375 54.890625 33.015625 \nz\n\" id=\"DejaVuSans-110\"/>\n     <path d=\"M 45.40625 27.984375 \nQ 45.40625 37.75 41.375 43.109375 \nQ 37.359375 48.484375 30.078125 48.484375 \nQ 22.859375 48.484375 18.828125 43.109375 \nQ 14.796875 37.75 14.796875 27.984375 \nQ 14.796875 18.265625 18.828125 12.890625 \nQ 22.859375 7.515625 30.078125 7.515625 \nQ 37.359375 7.515625 41.375 12.890625 \nQ 45.40625 18.265625 45.40625 27.984375 \nz\nM 54.390625 6.78125 \nQ 54.390625 -7.171875 48.1875 -13.984375 \nQ 42 -20.796875 29.203125 -20.796875 \nQ 24.46875 -20.796875 20.265625 -20.09375 \nQ 16.0625 -19.390625 12.109375 -17.921875 \nL 12.109375 -9.1875 \nQ 16.0625 -11.328125 19.921875 -12.34375 \nQ 23.78125 -13.375 27.78125 -13.375 \nQ 36.625 -13.375 41.015625 -8.765625 \nQ 45.40625 -4.15625 45.40625 5.171875 \nL 45.40625 9.625 \nQ 42.625 4.78125 38.28125 2.390625 \nQ 33.9375 0 27.875 0 \nQ 17.828125 0 11.671875 7.65625 \nQ 5.515625 15.328125 5.515625 27.984375 \nQ 5.515625 40.671875 11.671875 48.328125 \nQ 17.828125 56 27.875 56 \nQ 33.9375 56 38.28125 53.609375 \nQ 42.625 51.21875 45.40625 46.390625 \nL 45.40625 54.6875 \nL 54.390625 54.6875 \nz\n\" id=\"DejaVuSans-103\"/>\n     <path id=\"DejaVuSans-32\"/>\n     <path d=\"M 45.40625 46.390625 \nL 45.40625 75.984375 \nL 54.390625 75.984375 \nL 54.390625 0 \nL 45.40625 0 \nL 45.40625 8.203125 \nQ 42.578125 3.328125 38.25 0.953125 \nQ 33.9375 -1.421875 27.875 -1.421875 \nQ 17.96875 -1.421875 11.734375 6.484375 \nQ 5.515625 14.40625 5.515625 27.296875 \nQ 5.515625 40.1875 11.734375 48.09375 \nQ 17.96875 56 27.875 56 \nQ 33.9375 56 38.25 53.625 \nQ 42.578125 51.265625 45.40625 46.390625 \nz\nM 14.796875 27.296875 \nQ 14.796875 17.390625 18.875 11.75 \nQ 22.953125 6.109375 30.078125 6.109375 \nQ 37.203125 6.109375 41.296875 11.75 \nQ 45.40625 17.390625 45.40625 27.296875 \nQ 45.40625 37.203125 41.296875 42.84375 \nQ 37.203125 48.484375 30.078125 48.484375 \nQ 22.953125 48.484375 18.875 42.84375 \nQ 14.796875 37.203125 14.796875 27.296875 \nz\n\" id=\"DejaVuSans-100\"/>\n     <path d=\"M 2.984375 54.6875 \nL 12.5 54.6875 \nL 29.59375 8.796875 \nL 46.6875 54.6875 \nL 56.203125 54.6875 \nL 35.6875 0 \nL 23.484375 0 \nz\n\" id=\"DejaVuSans-118\"/>\n     <path d=\"M 9.421875 75.984375 \nL 18.40625 75.984375 \nL 18.40625 0 \nL 9.421875 0 \nz\n\" id=\"DejaVuSans-108\"/>\n     <path d=\"M 18.3125 70.21875 \nL 18.3125 54.6875 \nL 36.8125 54.6875 \nL 36.8125 47.703125 \nL 18.3125 47.703125 \nL 18.3125 18.015625 \nQ 18.3125 11.328125 20.140625 9.421875 \nQ 21.96875 7.515625 27.59375 7.515625 \nL 36.8125 7.515625 \nL 36.8125 0 \nL 27.59375 0 \nQ 17.1875 0 13.234375 3.875 \nQ 9.28125 7.765625 9.28125 18.015625 \nL 9.28125 47.703125 \nL 2.6875 47.703125 \nL 2.6875 54.6875 \nL 9.28125 54.6875 \nL 9.28125 70.21875 \nz\n\" id=\"DejaVuSans-116\"/>\n    </defs>\n    <g transform=\"translate(135.73375 16.318125)scale(0.12 -0.12)\">\n     <use xlink:href=\"#DejaVuSans-84\"/>\n     <use x=\"46.333984\" xlink:href=\"#DejaVuSans-114\"/>\n     <use x=\"87.447266\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"148.726562\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"176.509766\" xlink:href=\"#DejaVuSans-110\"/>\n     <use x=\"239.888672\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"267.671875\" xlink:href=\"#DejaVuSans-110\"/>\n     <use x=\"331.050781\" xlink:href=\"#DejaVuSans-103\"/>\n     <use x=\"394.527344\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"426.314453\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"487.59375\" xlink:href=\"#DejaVuSans-110\"/>\n     <use x=\"550.972656\" xlink:href=\"#DejaVuSans-100\"/>\n     <use x=\"614.449219\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"646.236328\" xlink:href=\"#DejaVuSans-118\"/>\n     <use x=\"705.416016\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"766.695312\" xlink:href=\"#DejaVuSans-108\"/>\n     <use x=\"794.478516\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"822.261719\" xlink:href=\"#DejaVuSans-100\"/>\n     <use x=\"885.738281\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"947.017578\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"986.226562\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"1014.009766\" xlink:href=\"#DejaVuSans-111\"/>\n     <use x=\"1075.191406\" xlink:href=\"#DejaVuSans-110\"/>\n     <use x=\"1138.570312\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"1170.357422\" xlink:href=\"#DejaVuSans-108\"/>\n     <use x=\"1198.140625\" xlink:href=\"#DejaVuSans-111\"/>\n     <use x=\"1259.322266\" xlink:href=\"#DejaVuSans-115\"/>\n     <use x=\"1311.421875\" xlink:href=\"#DejaVuSans-115\"/>\n    </g>\n   </g>\n   <g id=\"legend_1\">\n    <g id=\"patch_7\">\n     <path d=\"M 274.06875 59.674375 \nL 377.94375 59.674375 \nQ 379.94375 59.674375 379.94375 57.674375 \nL 379.94375 29.318125 \nQ 379.94375 27.318125 377.94375 27.318125 \nL 274.06875 27.318125 \nQ 272.06875 27.318125 272.06875 29.318125 \nL 272.06875 57.674375 \nQ 272.06875 59.674375 274.06875 59.674375 \nz\n\" style=\"fill:#ffffff;opacity:0.8;stroke:#cccccc;stroke-linejoin:miter;\"/>\n    </g>\n    <g id=\"line2d_17\">\n     <path d=\"M 276.06875 35.416562 \nL 296.06875 35.416562 \n\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;stroke-width:1.5;\"/>\n    </g>\n    <g id=\"line2d_18\"/>\n    <g id=\"text_18\">\n     <!-- Training loss -->\n     <g transform=\"translate(304.06875 38.916562)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-84\"/>\n      <use x=\"46.333984\" xlink:href=\"#DejaVuSans-114\"/>\n      <use x=\"87.447266\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"148.726562\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"176.509766\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"239.888672\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"267.671875\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"331.050781\" xlink:href=\"#DejaVuSans-103\"/>\n      <use x=\"394.527344\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"426.314453\" xlink:href=\"#DejaVuSans-108\"/>\n      <use x=\"454.097656\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"515.279297\" xlink:href=\"#DejaVuSans-115\"/>\n      <use x=\"567.378906\" xlink:href=\"#DejaVuSans-115\"/>\n     </g>\n    </g>\n    <g id=\"line2d_19\">\n     <path d=\"M 276.06875 50.094687 \nL 296.06875 50.094687 \n\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;stroke-width:1.5;\"/>\n    </g>\n    <g id=\"line2d_20\"/>\n    <g id=\"text_19\">\n     <!-- Validation loss -->\n     <defs>\n      <path d=\"M 28.609375 0 \nL 0.78125 72.90625 \nL 11.078125 72.90625 \nL 34.1875 11.53125 \nL 57.328125 72.90625 \nL 67.578125 72.90625 \nL 39.796875 0 \nz\n\" id=\"DejaVuSans-86\"/>\n     </defs>\n     <g transform=\"translate(304.06875 53.594687)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-86\"/>\n      <use x=\"60.658203\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"121.9375\" xlink:href=\"#DejaVuSans-108\"/>\n      <use x=\"149.720703\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"177.503906\" xlink:href=\"#DejaVuSans-100\"/>\n      <use x=\"240.980469\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"302.259766\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"341.46875\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"369.251953\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"430.433594\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"493.8125\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"525.599609\" xlink:href=\"#DejaVuSans-108\"/>\n      <use x=\"553.382812\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"614.564453\" xlink:href=\"#DejaVuSans-115\"/>\n      <use x=\"666.664062\" xlink:href=\"#DejaVuSans-115\"/>\n     </g>\n    </g>\n   </g>\n  </g>\n </g>\n <defs>\n  <clipPath id=\"pe055deb59b\">\n   <rect height=\"217.44\" width=\"334.8\" x=\"50.14375\" y=\"22.318125\"/>\n  </clipPath>\n </defs>\n</svg>\n", "image/png": "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\n"}, "metadata": {"needs_background": "light"}}, {"output_type": "display_data", "data": {"text/plain": "<Figure size 432x288 with 1 Axes>", "image/svg+xml": "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n<!-- Created with matplotlib (https://matplotlib.org/) -->\n<svg height=\"277.314375pt\" version=\"1.1\" viewBox=\"0 0 392.14375 277.314375\" width=\"392.14375pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n <defs>\n  <style type=\"text/css\">\n*{stroke-linecap:butt;stroke-linejoin:round;}\n  </style>\n </defs>\n <g id=\"figure_1\">\n  <g id=\"patch_1\">\n   <path d=\"M 0 277.314375 \nL 392.14375 277.314375 \nL 392.14375 0 \nL 0 0 \nz\n\" style=\"fill:none;\"/>\n  </g>\n  <g id=\"axes_1\">\n   <g id=\"patch_2\">\n    <path d=\"M 50.14375 239.758125 \nL 384.94375 239.758125 \nL 384.94375 22.318125 \nL 50.14375 22.318125 \nz\n\" style=\"fill:#ffffff;\"/>\n   </g>\n   <g id=\"matplotlib.axis_1\">\n    <g id=\"xtick_1\">\n     <g id=\"line2d_1\">\n      <defs>\n       <path d=\"M 0 0 \nL 0 3.5 \n\" id=\"m78f9f8d63d\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"61.509228\" xlink:href=\"#m78f9f8d63d\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_1\">\n      <!-- 0 -->\n      <defs>\n       <path d=\"M 31.78125 66.40625 \nQ 24.171875 66.40625 20.328125 58.90625 \nQ 16.5 51.421875 16.5 36.375 \nQ 16.5 21.390625 20.328125 13.890625 \nQ 24.171875 6.390625 31.78125 6.390625 \nQ 39.453125 6.390625 43.28125 13.890625 \nQ 47.125 21.390625 47.125 36.375 \nQ 47.125 51.421875 43.28125 58.90625 \nQ 39.453125 66.40625 31.78125 66.40625 \nz\nM 31.78125 74.21875 \nQ 44.046875 74.21875 50.515625 64.515625 \nQ 56.984375 54.828125 56.984375 36.375 \nQ 56.984375 17.96875 50.515625 8.265625 \nQ 44.046875 -1.421875 31.78125 -1.421875 \nQ 19.53125 -1.421875 13.0625 8.265625 \nQ 6.59375 17.96875 6.59375 36.375 \nQ 6.59375 54.828125 13.0625 64.515625 \nQ 19.53125 74.21875 31.78125 74.21875 \nz\n\" id=\"DejaVuSans-48\"/>\n      </defs>\n      <g transform=\"translate(58.327978 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_2\">\n     <g id=\"line2d_2\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"100.03627\" xlink:href=\"#m78f9f8d63d\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_2\">\n      <!-- 10 -->\n      <defs>\n       <path d=\"M 12.40625 8.296875 \nL 28.515625 8.296875 \nL 28.515625 63.921875 \nL 10.984375 60.40625 \nL 10.984375 69.390625 \nL 28.421875 72.90625 \nL 38.28125 72.90625 \nL 38.28125 8.296875 \nL 54.390625 8.296875 \nL 54.390625 0 \nL 12.40625 0 \nz\n\" id=\"DejaVuSans-49\"/>\n      </defs>\n      <g transform=\"translate(93.67377 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_3\">\n     <g id=\"line2d_3\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"138.563313\" xlink:href=\"#m78f9f8d63d\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_3\">\n      <!-- 20 -->\n      <defs>\n       <path d=\"M 19.1875 8.296875 \nL 53.609375 8.296875 \nL 53.609375 0 \nL 7.328125 0 \nL 7.328125 8.296875 \nQ 12.9375 14.109375 22.625 23.890625 \nQ 32.328125 33.6875 34.8125 36.53125 \nQ 39.546875 41.84375 41.421875 45.53125 \nQ 43.3125 49.21875 43.3125 52.78125 \nQ 43.3125 58.59375 39.234375 62.25 \nQ 35.15625 65.921875 28.609375 65.921875 \nQ 23.96875 65.921875 18.8125 64.3125 \nQ 13.671875 62.703125 7.8125 59.421875 \nL 7.8125 69.390625 \nQ 13.765625 71.78125 18.9375 73 \nQ 24.125 74.21875 28.421875 74.21875 \nQ 39.75 74.21875 46.484375 68.546875 \nQ 53.21875 62.890625 53.21875 53.421875 \nQ 53.21875 48.921875 51.53125 44.890625 \nQ 49.859375 40.875 45.40625 35.40625 \nQ 44.1875 33.984375 37.640625 27.21875 \nQ 31.109375 20.453125 19.1875 8.296875 \nz\n\" id=\"DejaVuSans-50\"/>\n      </defs>\n      <g transform=\"translate(132.200813 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_4\">\n     <g id=\"line2d_4\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"177.090355\" xlink:href=\"#m78f9f8d63d\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_4\">\n      <!-- 30 -->\n      <defs>\n       <path d=\"M 40.578125 39.3125 \nQ 47.65625 37.796875 51.625 33 \nQ 55.609375 28.21875 55.609375 21.1875 \nQ 55.609375 10.40625 48.1875 4.484375 \nQ 40.765625 -1.421875 27.09375 -1.421875 \nQ 22.515625 -1.421875 17.65625 -0.515625 \nQ 12.796875 0.390625 7.625 2.203125 \nL 7.625 11.71875 \nQ 11.71875 9.328125 16.59375 8.109375 \nQ 21.484375 6.890625 26.8125 6.890625 \nQ 36.078125 6.890625 40.9375 10.546875 \nQ 45.796875 14.203125 45.796875 21.1875 \nQ 45.796875 27.640625 41.28125 31.265625 \nQ 36.765625 34.90625 28.71875 34.90625 \nL 20.21875 34.90625 \nL 20.21875 43.015625 \nL 29.109375 43.015625 \nQ 36.375 43.015625 40.234375 45.921875 \nQ 44.09375 48.828125 44.09375 54.296875 \nQ 44.09375 59.90625 40.109375 62.90625 \nQ 36.140625 65.921875 28.71875 65.921875 \nQ 24.65625 65.921875 20.015625 65.03125 \nQ 15.375 64.15625 9.8125 62.3125 \nL 9.8125 71.09375 \nQ 15.4375 72.65625 20.34375 73.4375 \nQ 25.25 74.21875 29.59375 74.21875 \nQ 40.828125 74.21875 47.359375 69.109375 \nQ 53.90625 64.015625 53.90625 55.328125 \nQ 53.90625 49.265625 50.4375 45.09375 \nQ 46.96875 40.921875 40.578125 39.3125 \nz\n\" id=\"DejaVuSans-51\"/>\n      </defs>\n      <g transform=\"translate(170.727855 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-51\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_5\">\n     <g id=\"line2d_5\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"215.617398\" xlink:href=\"#m78f9f8d63d\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_5\">\n      <!-- 40 -->\n      <defs>\n       <path d=\"M 37.796875 64.3125 \nL 12.890625 25.390625 \nL 37.796875 25.390625 \nz\nM 35.203125 72.90625 \nL 47.609375 72.90625 \nL 47.609375 25.390625 \nL 58.015625 25.390625 \nL 58.015625 17.1875 \nL 47.609375 17.1875 \nL 47.609375 0 \nL 37.796875 0 \nL 37.796875 17.1875 \nL 4.890625 17.1875 \nL 4.890625 26.703125 \nz\n\" id=\"DejaVuSans-52\"/>\n      </defs>\n      <g transform=\"translate(209.254898 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-52\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_6\">\n     <g id=\"line2d_6\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"254.14444\" xlink:href=\"#m78f9f8d63d\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_6\">\n      <!-- 50 -->\n      <defs>\n       <path d=\"M 10.796875 72.90625 \nL 49.515625 72.90625 \nL 49.515625 64.59375 \nL 19.828125 64.59375 \nL 19.828125 46.734375 \nQ 21.96875 47.46875 24.109375 47.828125 \nQ 26.265625 48.1875 28.421875 48.1875 \nQ 40.625 48.1875 47.75 41.5 \nQ 54.890625 34.8125 54.890625 23.390625 \nQ 54.890625 11.625 47.5625 5.09375 \nQ 40.234375 -1.421875 26.90625 -1.421875 \nQ 22.3125 -1.421875 17.546875 -0.640625 \nQ 12.796875 0.140625 7.71875 1.703125 \nL 7.71875 11.625 \nQ 12.109375 9.234375 16.796875 8.0625 \nQ 21.484375 6.890625 26.703125 6.890625 \nQ 35.15625 6.890625 40.078125 11.328125 \nQ 45.015625 15.765625 45.015625 23.390625 \nQ 45.015625 31 40.078125 35.4375 \nQ 35.15625 39.890625 26.703125 39.890625 \nQ 22.75 39.890625 18.8125 39.015625 \nQ 14.890625 38.140625 10.796875 36.28125 \nz\n\" id=\"DejaVuSans-53\"/>\n      </defs>\n      <g transform=\"translate(247.78194 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-53\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_7\">\n     <g id=\"line2d_7\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"292.671483\" xlink:href=\"#m78f9f8d63d\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_7\">\n      <!-- 60 -->\n      <defs>\n       <path d=\"M 33.015625 40.375 \nQ 26.375 40.375 22.484375 35.828125 \nQ 18.609375 31.296875 18.609375 23.390625 \nQ 18.609375 15.53125 22.484375 10.953125 \nQ 26.375 6.390625 33.015625 6.390625 \nQ 39.65625 6.390625 43.53125 10.953125 \nQ 47.40625 15.53125 47.40625 23.390625 \nQ 47.40625 31.296875 43.53125 35.828125 \nQ 39.65625 40.375 33.015625 40.375 \nz\nM 52.59375 71.296875 \nL 52.59375 62.3125 \nQ 48.875 64.0625 45.09375 64.984375 \nQ 41.3125 65.921875 37.59375 65.921875 \nQ 27.828125 65.921875 22.671875 59.328125 \nQ 17.53125 52.734375 16.796875 39.40625 \nQ 19.671875 43.65625 24.015625 45.921875 \nQ 28.375 48.1875 33.59375 48.1875 \nQ 44.578125 48.1875 50.953125 41.515625 \nQ 57.328125 34.859375 57.328125 23.390625 \nQ 57.328125 12.15625 50.6875 5.359375 \nQ 44.046875 -1.421875 33.015625 -1.421875 \nQ 20.359375 -1.421875 13.671875 8.265625 \nQ 6.984375 17.96875 6.984375 36.375 \nQ 6.984375 53.65625 15.1875 63.9375 \nQ 23.390625 74.21875 37.203125 74.21875 \nQ 40.921875 74.21875 44.703125 73.484375 \nQ 48.484375 72.75 52.59375 71.296875 \nz\n\" id=\"DejaVuSans-54\"/>\n      </defs>\n      <g transform=\"translate(286.308983 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_8\">\n     <g id=\"line2d_8\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"331.198526\" xlink:href=\"#m78f9f8d63d\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_8\">\n      <!-- 70 -->\n      <defs>\n       <path d=\"M 8.203125 72.90625 \nL 55.078125 72.90625 \nL 55.078125 68.703125 \nL 28.609375 0 \nL 18.3125 0 \nL 43.21875 64.59375 \nL 8.203125 64.59375 \nz\n\" id=\"DejaVuSans-55\"/>\n      </defs>\n      <g transform=\"translate(324.836026 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_9\">\n     <g id=\"line2d_9\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"369.725568\" xlink:href=\"#m78f9f8d63d\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_9\">\n      <!-- 80 -->\n      <defs>\n       <path d=\"M 31.78125 34.625 \nQ 24.75 34.625 20.71875 30.859375 \nQ 16.703125 27.09375 16.703125 20.515625 \nQ 16.703125 13.921875 20.71875 10.15625 \nQ 24.75 6.390625 31.78125 6.390625 \nQ 38.8125 6.390625 42.859375 10.171875 \nQ 46.921875 13.96875 46.921875 20.515625 \nQ 46.921875 27.09375 42.890625 30.859375 \nQ 38.875 34.625 31.78125 34.625 \nz\nM 21.921875 38.8125 \nQ 15.578125 40.375 12.03125 44.71875 \nQ 8.5 49.078125 8.5 55.328125 \nQ 8.5 64.0625 14.71875 69.140625 \nQ 20.953125 74.21875 31.78125 74.21875 \nQ 42.671875 74.21875 48.875 69.140625 \nQ 55.078125 64.0625 55.078125 55.328125 \nQ 55.078125 49.078125 51.53125 44.71875 \nQ 48 40.375 41.703125 38.8125 \nQ 48.828125 37.15625 52.796875 32.3125 \nQ 56.78125 27.484375 56.78125 20.515625 \nQ 56.78125 9.90625 50.3125 4.234375 \nQ 43.84375 -1.421875 31.78125 -1.421875 \nQ 19.734375 -1.421875 13.25 4.234375 \nQ 6.78125 9.90625 6.78125 20.515625 \nQ 6.78125 27.484375 10.78125 32.3125 \nQ 14.796875 37.15625 21.921875 38.8125 \nz\nM 18.3125 54.390625 \nQ 18.3125 48.734375 21.84375 45.5625 \nQ 25.390625 42.390625 31.78125 42.390625 \nQ 38.140625 42.390625 41.71875 45.5625 \nQ 45.3125 48.734375 45.3125 54.390625 \nQ 45.3125 60.0625 41.71875 63.234375 \nQ 38.140625 66.40625 31.78125 66.40625 \nQ 25.390625 66.40625 21.84375 63.234375 \nQ 18.3125 60.0625 18.3125 54.390625 \nz\n\" id=\"DejaVuSans-56\"/>\n      </defs>\n      <g transform=\"translate(363.363068 254.356562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"text_10\">\n     <!-- Epochs -->\n     <defs>\n      <path d=\"M 9.8125 72.90625 \nL 55.90625 72.90625 \nL 55.90625 64.59375 \nL 19.671875 64.59375 \nL 19.671875 43.015625 \nL 54.390625 43.015625 \nL 54.390625 34.71875 \nL 19.671875 34.71875 \nL 19.671875 8.296875 \nL 56.78125 8.296875 \nL 56.78125 0 \nL 9.8125 0 \nz\n\" id=\"DejaVuSans-69\"/>\n      <path d=\"M 18.109375 8.203125 \nL 18.109375 -20.796875 \nL 9.078125 -20.796875 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.390625 \nQ 20.953125 51.265625 25.265625 53.625 \nQ 29.59375 56 35.59375 56 \nQ 45.5625 56 51.78125 48.09375 \nQ 58.015625 40.1875 58.015625 27.296875 \nQ 58.015625 14.40625 51.78125 6.484375 \nQ 45.5625 -1.421875 35.59375 -1.421875 \nQ 29.59375 -1.421875 25.265625 0.953125 \nQ 20.953125 3.328125 18.109375 8.203125 \nz\nM 48.6875 27.296875 \nQ 48.6875 37.203125 44.609375 42.84375 \nQ 40.53125 48.484375 33.40625 48.484375 \nQ 26.265625 48.484375 22.1875 42.84375 \nQ 18.109375 37.203125 18.109375 27.296875 \nQ 18.109375 17.390625 22.1875 11.75 \nQ 26.265625 6.109375 33.40625 6.109375 \nQ 40.53125 6.109375 44.609375 11.75 \nQ 48.6875 17.390625 48.6875 27.296875 \nz\n\" id=\"DejaVuSans-112\"/>\n      <path d=\"M 30.609375 48.390625 \nQ 23.390625 48.390625 19.1875 42.75 \nQ 14.984375 37.109375 14.984375 27.296875 \nQ 14.984375 17.484375 19.15625 11.84375 \nQ 23.34375 6.203125 30.609375 6.203125 \nQ 37.796875 6.203125 41.984375 11.859375 \nQ 46.1875 17.53125 46.1875 27.296875 \nQ 46.1875 37.015625 41.984375 42.703125 \nQ 37.796875 48.390625 30.609375 48.390625 \nz\nM 30.609375 56 \nQ 42.328125 56 49.015625 48.375 \nQ 55.71875 40.765625 55.71875 27.296875 \nQ 55.71875 13.875 49.015625 6.21875 \nQ 42.328125 -1.421875 30.609375 -1.421875 \nQ 18.84375 -1.421875 12.171875 6.21875 \nQ 5.515625 13.875 5.515625 27.296875 \nQ 5.515625 40.765625 12.171875 48.375 \nQ 18.84375 56 30.609375 56 \nz\n\" id=\"DejaVuSans-111\"/>\n      <path d=\"M 48.78125 52.59375 \nL 48.78125 44.1875 \nQ 44.96875 46.296875 41.140625 47.34375 \nQ 37.3125 48.390625 33.40625 48.390625 \nQ 24.65625 48.390625 19.8125 42.84375 \nQ 14.984375 37.3125 14.984375 27.296875 \nQ 14.984375 17.28125 19.8125 11.734375 \nQ 24.65625 6.203125 33.40625 6.203125 \nQ 37.3125 6.203125 41.140625 7.25 \nQ 44.96875 8.296875 48.78125 10.40625 \nL 48.78125 2.09375 \nQ 45.015625 0.34375 40.984375 -0.53125 \nQ 36.96875 -1.421875 32.421875 -1.421875 \nQ 20.0625 -1.421875 12.78125 6.34375 \nQ 5.515625 14.109375 5.515625 27.296875 \nQ 5.515625 40.671875 12.859375 48.328125 \nQ 20.21875 56 33.015625 56 \nQ 37.15625 56 41.109375 55.140625 \nQ 45.0625 54.296875 48.78125 52.59375 \nz\n\" id=\"DejaVuSans-99\"/>\n      <path d=\"M 54.890625 33.015625 \nL 54.890625 0 \nL 45.90625 0 \nL 45.90625 32.71875 \nQ 45.90625 40.484375 42.875 44.328125 \nQ 39.84375 48.1875 33.796875 48.1875 \nQ 26.515625 48.1875 22.3125 43.546875 \nQ 18.109375 38.921875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 75.984375 \nL 18.109375 75.984375 \nL 18.109375 46.1875 \nQ 21.34375 51.125 25.703125 53.5625 \nQ 30.078125 56 35.796875 56 \nQ 45.21875 56 50.046875 50.171875 \nQ 54.890625 44.34375 54.890625 33.015625 \nz\n\" id=\"DejaVuSans-104\"/>\n      <path d=\"M 44.28125 53.078125 \nL 44.28125 44.578125 \nQ 40.484375 46.53125 36.375 47.5 \nQ 32.28125 48.484375 27.875 48.484375 \nQ 21.1875 48.484375 17.84375 46.4375 \nQ 14.5 44.390625 14.5 40.28125 \nQ 14.5 37.15625 16.890625 35.375 \nQ 19.28125 33.59375 26.515625 31.984375 \nL 29.59375 31.296875 \nQ 39.15625 29.25 43.1875 25.515625 \nQ 47.21875 21.78125 47.21875 15.09375 \nQ 47.21875 7.46875 41.1875 3.015625 \nQ 35.15625 -1.421875 24.609375 -1.421875 \nQ 20.21875 -1.421875 15.453125 -0.5625 \nQ 10.6875 0.296875 5.421875 2 \nL 5.421875 11.28125 \nQ 10.40625 8.6875 15.234375 7.390625 \nQ 20.0625 6.109375 24.8125 6.109375 \nQ 31.15625 6.109375 34.5625 8.28125 \nQ 37.984375 10.453125 37.984375 14.40625 \nQ 37.984375 18.0625 35.515625 20.015625 \nQ 33.0625 21.96875 24.703125 23.78125 \nL 21.578125 24.515625 \nQ 13.234375 26.265625 9.515625 29.90625 \nQ 5.8125 33.546875 5.8125 39.890625 \nQ 5.8125 47.609375 11.28125 51.796875 \nQ 16.75 56 26.8125 56 \nQ 31.78125 56 36.171875 55.265625 \nQ 40.578125 54.546875 44.28125 53.078125 \nz\n\" id=\"DejaVuSans-115\"/>\n     </defs>\n     <g transform=\"translate(199.628125 268.034687)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-69\"/>\n      <use x=\"63.183594\" xlink:href=\"#DejaVuSans-112\"/>\n      <use x=\"126.660156\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"187.841797\" xlink:href=\"#DejaVuSans-99\"/>\n      <use x=\"242.822266\" xlink:href=\"#DejaVuSans-104\"/>\n      <use x=\"306.201172\" xlink:href=\"#DejaVuSans-115\"/>\n     </g>\n    </g>\n   </g>\n   <g id=\"matplotlib.axis_2\">\n    <g id=\"ytick_1\">\n     <g id=\"line2d_10\">\n      <defs>\n       <path d=\"M 0 0 \nL -3.5 0 \n\" id=\"mbb06034e40\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mbb06034e40\" y=\"239.758125\"/>\n      </g>\n     </g>\n     <g id=\"text_11\">\n      <!-- 0.00 -->\n      <defs>\n       <path d=\"M 10.6875 12.40625 \nL 21 12.40625 \nL 21 0 \nL 10.6875 0 \nz\n\" id=\"DejaVuSans-46\"/>\n      </defs>\n      <g transform=\"translate(20.878125 243.557344)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_2\">\n     <g id=\"line2d_11\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mbb06034e40\" y=\"203.518125\"/>\n      </g>\n     </g>\n     <g id=\"text_12\">\n      <!-- 0.01 -->\n      <g transform=\"translate(20.878125 207.317344)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_3\">\n     <g id=\"line2d_12\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mbb06034e40\" y=\"167.278125\"/>\n      </g>\n     </g>\n     <g id=\"text_13\">\n      <!-- 0.02 -->\n      <g transform=\"translate(20.878125 171.077344)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_4\">\n     <g id=\"line2d_13\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mbb06034e40\" y=\"131.038125\"/>\n      </g>\n     </g>\n     <g id=\"text_14\">\n      <!-- 0.03 -->\n      <g transform=\"translate(20.878125 134.837344)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-51\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_5\">\n     <g id=\"line2d_14\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mbb06034e40\" y=\"94.798125\"/>\n      </g>\n     </g>\n     <g id=\"text_15\">\n      <!-- 0.04 -->\n      <g transform=\"translate(20.878125 98.597344)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-52\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_6\">\n     <g id=\"line2d_15\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mbb06034e40\" y=\"58.558125\"/>\n      </g>\n     </g>\n     <g id=\"text_16\">\n      <!-- 0.05 -->\n      <g transform=\"translate(20.878125 62.357344)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-53\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_7\">\n     <g id=\"line2d_16\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mbb06034e40\" y=\"22.318125\"/>\n      </g>\n     </g>\n     <g id=\"text_17\">\n      <!-- 0.06 -->\n      <g transform=\"translate(20.878125 26.117344)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-54\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"text_18\">\n     <!-- mae -->\n     <defs>\n      <path d=\"M 52 44.1875 \nQ 55.375 50.25 60.0625 53.125 \nQ 64.75 56 71.09375 56 \nQ 79.640625 56 84.28125 50.015625 \nQ 88.921875 44.046875 88.921875 33.015625 \nL 88.921875 0 \nL 79.890625 0 \nL 79.890625 32.71875 \nQ 79.890625 40.578125 77.09375 44.375 \nQ 74.3125 48.1875 68.609375 48.1875 \nQ 61.625 48.1875 57.5625 43.546875 \nQ 53.515625 38.921875 53.515625 30.90625 \nL 53.515625 0 \nL 44.484375 0 \nL 44.484375 32.71875 \nQ 44.484375 40.625 41.703125 44.40625 \nQ 38.921875 48.1875 33.109375 48.1875 \nQ 26.21875 48.1875 22.15625 43.53125 \nQ 18.109375 38.875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 21.1875 51.21875 25.484375 53.609375 \nQ 29.78125 56 35.6875 56 \nQ 41.65625 56 45.828125 52.96875 \nQ 50 49.953125 52 44.1875 \nz\n\" id=\"DejaVuSans-109\"/>\n      <path d=\"M 34.28125 27.484375 \nQ 23.390625 27.484375 19.1875 25 \nQ 14.984375 22.515625 14.984375 16.5 \nQ 14.984375 11.71875 18.140625 8.90625 \nQ 21.296875 6.109375 26.703125 6.109375 \nQ 34.1875 6.109375 38.703125 11.40625 \nQ 43.21875 16.703125 43.21875 25.484375 \nL 43.21875 27.484375 \nz\nM 52.203125 31.203125 \nL 52.203125 0 \nL 43.21875 0 \nL 43.21875 8.296875 \nQ 40.140625 3.328125 35.546875 0.953125 \nQ 30.953125 -1.421875 24.3125 -1.421875 \nQ 15.921875 -1.421875 10.953125 3.296875 \nQ 6 8.015625 6 15.921875 \nQ 6 25.140625 12.171875 29.828125 \nQ 18.359375 34.515625 30.609375 34.515625 \nL 43.21875 34.515625 \nL 43.21875 35.40625 \nQ 43.21875 41.609375 39.140625 45 \nQ 35.0625 48.390625 27.6875 48.390625 \nQ 23 48.390625 18.546875 47.265625 \nQ 14.109375 46.140625 10.015625 43.890625 \nL 10.015625 52.203125 \nQ 14.9375 54.109375 19.578125 55.046875 \nQ 24.21875 56 28.609375 56 \nQ 40.484375 56 46.34375 49.84375 \nQ 52.203125 43.703125 52.203125 31.203125 \nz\n\" id=\"DejaVuSans-97\"/>\n      <path d=\"M 56.203125 29.59375 \nL 56.203125 25.203125 \nL 14.890625 25.203125 \nQ 15.484375 15.921875 20.484375 11.0625 \nQ 25.484375 6.203125 34.421875 6.203125 \nQ 39.59375 6.203125 44.453125 7.46875 \nQ 49.3125 8.734375 54.109375 11.28125 \nL 54.109375 2.78125 \nQ 49.265625 0.734375 44.1875 -0.34375 \nQ 39.109375 -1.421875 33.890625 -1.421875 \nQ 20.796875 -1.421875 13.15625 6.1875 \nQ 5.515625 13.8125 5.515625 26.8125 \nQ 5.515625 40.234375 12.765625 48.109375 \nQ 20.015625 56 32.328125 56 \nQ 43.359375 56 49.78125 48.890625 \nQ 56.203125 41.796875 56.203125 29.59375 \nz\nM 47.21875 32.234375 \nQ 47.125 39.59375 43.09375 43.984375 \nQ 39.0625 48.390625 32.421875 48.390625 \nQ 24.90625 48.390625 20.390625 44.140625 \nQ 15.875 39.890625 15.1875 32.171875 \nz\n\" id=\"DejaVuSans-101\"/>\n     </defs>\n     <g transform=\"translate(14.798438 142.049062)rotate(-90)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-109\"/>\n      <use x=\"97.412109\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"158.691406\" xlink:href=\"#DejaVuSans-101\"/>\n     </g>\n    </g>\n   </g>\n   <g id=\"line2d_17\">\n    <path clip-path=\"url(#p626fc9965e)\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;stroke-width:1.5;\"/>\n   </g>\n   <g id=\"line2d_18\">\n    <path clip-path=\"url(#p626fc9965e)\" d=\"M 120.628768 -1 \nL 123.152496 143.847559 \nL 126.351044 -1 \nM 132.926708 -1 \nL 134.710608 55.344893 \nL 138.563313 142.795921 \nL 141.546807 -1 \nM 162.924274 -1 \nL 165.532243 74.419223 \nL 169.384947 131.814613 \nL 173.237651 138.476424 \nL 177.090355 111.883733 \nL 180.94306 46.470371 \nL 183.163722 -1 \nM 203.782234 -1 \nL 204.059285 3.069145 \nL 207.911989 35.740117 \nL 211.764694 49.501776 \nL 215.617398 46.118347 \nL 219.470102 33.039244 \nL 223.322806 22.462747 \nL 227.175511 13.606477 \nL 230.051577 -1 \nM 249.048018 -1 \nL 250.291736 8.898154 \nL 254.14444 28.635504 \nL 257.997145 39.132065 \nL 261.849849 34.659163 \nL 265.702553 15.382759 \nL 267.702988 -1 \nM 288.390627 -1 \nL 288.818779 4.383873 \nL 292.671483 47.86463 \nL 296.524187 59.601572 \nL 300.376892 40.030669 \nL 304.229596 7.420044 \nL 305.502069 -1 \nM 332.953314 -1 \nL 335.05123 12.834211 \nL 338.903934 31.288276 \nL 342.756638 45.149608 \nL 346.609343 34.583547 \nL 350.462047 6.316112 \nL 351.219917 -1 \n\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;stroke-width:1.5;\"/>\n   </g>\n   <g id=\"patch_3\">\n    <path d=\"M 50.14375 239.758125 \nL 50.14375 22.318125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_4\">\n    <path d=\"M 384.94375 239.758125 \nL 384.94375 22.318125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_5\">\n    <path d=\"M 50.14375 239.758125 \nL 384.94375 239.758125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_6\">\n    <path d=\"M 50.14375 22.318125 \nL 384.94375 22.318125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"text_19\">\n    <!-- Training and validation mae -->\n    <defs>\n     <path d=\"M -0.296875 72.90625 \nL 61.375 72.90625 \nL 61.375 64.59375 \nL 35.5 64.59375 \nL 35.5 0 \nL 25.59375 0 \nL 25.59375 64.59375 \nL -0.296875 64.59375 \nz\n\" id=\"DejaVuSans-84\"/>\n     <path d=\"M 41.109375 46.296875 \nQ 39.59375 47.171875 37.8125 47.578125 \nQ 36.03125 48 33.890625 48 \nQ 26.265625 48 22.1875 43.046875 \nQ 18.109375 38.09375 18.109375 28.8125 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 20.953125 51.171875 25.484375 53.578125 \nQ 30.03125 56 36.53125 56 \nQ 37.453125 56 38.578125 55.875 \nQ 39.703125 55.765625 41.0625 55.515625 \nz\n\" id=\"DejaVuSans-114\"/>\n     <path d=\"M 9.421875 54.6875 \nL 18.40625 54.6875 \nL 18.40625 0 \nL 9.421875 0 \nz\nM 9.421875 75.984375 \nL 18.40625 75.984375 \nL 18.40625 64.59375 \nL 9.421875 64.59375 \nz\n\" id=\"DejaVuSans-105\"/>\n     <path d=\"M 54.890625 33.015625 \nL 54.890625 0 \nL 45.90625 0 \nL 45.90625 32.71875 \nQ 45.90625 40.484375 42.875 44.328125 \nQ 39.84375 48.1875 33.796875 48.1875 \nQ 26.515625 48.1875 22.3125 43.546875 \nQ 18.109375 38.921875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 21.34375 51.125 25.703125 53.5625 \nQ 30.078125 56 35.796875 56 \nQ 45.21875 56 50.046875 50.171875 \nQ 54.890625 44.34375 54.890625 33.015625 \nz\n\" id=\"DejaVuSans-110\"/>\n     <path d=\"M 45.40625 27.984375 \nQ 45.40625 37.75 41.375 43.109375 \nQ 37.359375 48.484375 30.078125 48.484375 \nQ 22.859375 48.484375 18.828125 43.109375 \nQ 14.796875 37.75 14.796875 27.984375 \nQ 14.796875 18.265625 18.828125 12.890625 \nQ 22.859375 7.515625 30.078125 7.515625 \nQ 37.359375 7.515625 41.375 12.890625 \nQ 45.40625 18.265625 45.40625 27.984375 \nz\nM 54.390625 6.78125 \nQ 54.390625 -7.171875 48.1875 -13.984375 \nQ 42 -20.796875 29.203125 -20.796875 \nQ 24.46875 -20.796875 20.265625 -20.09375 \nQ 16.0625 -19.390625 12.109375 -17.921875 \nL 12.109375 -9.1875 \nQ 16.0625 -11.328125 19.921875 -12.34375 \nQ 23.78125 -13.375 27.78125 -13.375 \nQ 36.625 -13.375 41.015625 -8.765625 \nQ 45.40625 -4.15625 45.40625 5.171875 \nL 45.40625 9.625 \nQ 42.625 4.78125 38.28125 2.390625 \nQ 33.9375 0 27.875 0 \nQ 17.828125 0 11.671875 7.65625 \nQ 5.515625 15.328125 5.515625 27.984375 \nQ 5.515625 40.671875 11.671875 48.328125 \nQ 17.828125 56 27.875 56 \nQ 33.9375 56 38.28125 53.609375 \nQ 42.625 51.21875 45.40625 46.390625 \nL 45.40625 54.6875 \nL 54.390625 54.6875 \nz\n\" id=\"DejaVuSans-103\"/>\n     <path id=\"DejaVuSans-32\"/>\n     <path d=\"M 45.40625 46.390625 \nL 45.40625 75.984375 \nL 54.390625 75.984375 \nL 54.390625 0 \nL 45.40625 0 \nL 45.40625 8.203125 \nQ 42.578125 3.328125 38.25 0.953125 \nQ 33.9375 -1.421875 27.875 -1.421875 \nQ 17.96875 -1.421875 11.734375 6.484375 \nQ 5.515625 14.40625 5.515625 27.296875 \nQ 5.515625 40.1875 11.734375 48.09375 \nQ 17.96875 56 27.875 56 \nQ 33.9375 56 38.25 53.625 \nQ 42.578125 51.265625 45.40625 46.390625 \nz\nM 14.796875 27.296875 \nQ 14.796875 17.390625 18.875 11.75 \nQ 22.953125 6.109375 30.078125 6.109375 \nQ 37.203125 6.109375 41.296875 11.75 \nQ 45.40625 17.390625 45.40625 27.296875 \nQ 45.40625 37.203125 41.296875 42.84375 \nQ 37.203125 48.484375 30.078125 48.484375 \nQ 22.953125 48.484375 18.875 42.84375 \nQ 14.796875 37.203125 14.796875 27.296875 \nz\n\" id=\"DejaVuSans-100\"/>\n     <path d=\"M 2.984375 54.6875 \nL 12.5 54.6875 \nL 29.59375 8.796875 \nL 46.6875 54.6875 \nL 56.203125 54.6875 \nL 35.6875 0 \nL 23.484375 0 \nz\n\" id=\"DejaVuSans-118\"/>\n     <path d=\"M 9.421875 75.984375 \nL 18.40625 75.984375 \nL 18.40625 0 \nL 9.421875 0 \nz\n\" id=\"DejaVuSans-108\"/>\n     <path d=\"M 18.3125 70.21875 \nL 18.3125 54.6875 \nL 36.8125 54.6875 \nL 36.8125 47.703125 \nL 18.3125 47.703125 \nL 18.3125 18.015625 \nQ 18.3125 11.328125 20.140625 9.421875 \nQ 21.96875 7.515625 27.59375 7.515625 \nL 36.8125 7.515625 \nL 36.8125 0 \nL 27.59375 0 \nQ 17.1875 0 13.234375 3.875 \nQ 9.28125 7.765625 9.28125 18.015625 \nL 9.28125 47.703125 \nL 2.6875 47.703125 \nL 2.6875 54.6875 \nL 9.28125 54.6875 \nL 9.28125 70.21875 \nz\n\" id=\"DejaVuSans-116\"/>\n    </defs>\n    <g transform=\"translate(134.11 16.318125)scale(0.12 -0.12)\">\n     <use xlink:href=\"#DejaVuSans-84\"/>\n     <use x=\"46.333984\" xlink:href=\"#DejaVuSans-114\"/>\n     <use x=\"87.447266\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"148.726562\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"176.509766\" xlink:href=\"#DejaVuSans-110\"/>\n     <use x=\"239.888672\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"267.671875\" xlink:href=\"#DejaVuSans-110\"/>\n     <use x=\"331.050781\" xlink:href=\"#DejaVuSans-103\"/>\n     <use x=\"394.527344\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"426.314453\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"487.59375\" xlink:href=\"#DejaVuSans-110\"/>\n     <use x=\"550.972656\" xlink:href=\"#DejaVuSans-100\"/>\n     <use x=\"614.449219\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"646.236328\" xlink:href=\"#DejaVuSans-118\"/>\n     <use x=\"705.416016\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"766.695312\" xlink:href=\"#DejaVuSans-108\"/>\n     <use x=\"794.478516\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"822.261719\" xlink:href=\"#DejaVuSans-100\"/>\n     <use x=\"885.738281\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"947.017578\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"986.226562\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"1014.009766\" xlink:href=\"#DejaVuSans-111\"/>\n     <use x=\"1075.191406\" xlink:href=\"#DejaVuSans-110\"/>\n     <use x=\"1138.570312\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"1170.357422\" xlink:href=\"#DejaVuSans-109\"/>\n     <use x=\"1267.769531\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"1329.048828\" xlink:href=\"#DejaVuSans-101\"/>\n    </g>\n   </g>\n   <g id=\"legend_1\">\n    <g id=\"patch_7\">\n     <path d=\"M 57.14375 234.758125 \nL 163.725 234.758125 \nQ 165.725 234.758125 165.725 232.758125 \nL 165.725 204.401875 \nQ 165.725 202.401875 163.725 202.401875 \nL 57.14375 202.401875 \nQ 55.14375 202.401875 55.14375 204.401875 \nL 55.14375 232.758125 \nQ 55.14375 234.758125 57.14375 234.758125 \nz\n\" style=\"fill:#ffffff;opacity:0.8;stroke:#cccccc;stroke-linejoin:miter;\"/>\n    </g>\n    <g id=\"line2d_19\">\n     <path d=\"M 59.14375 210.500312 \nL 79.14375 210.500312 \n\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;stroke-width:1.5;\"/>\n    </g>\n    <g id=\"line2d_20\"/>\n    <g id=\"text_20\">\n     <!-- Training mae -->\n     <g transform=\"translate(87.14375 214.000312)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-84\"/>\n      <use x=\"46.333984\" xlink:href=\"#DejaVuSans-114\"/>\n      <use x=\"87.447266\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"148.726562\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"176.509766\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"239.888672\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"267.671875\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"331.050781\" xlink:href=\"#DejaVuSans-103\"/>\n      <use x=\"394.527344\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"426.314453\" xlink:href=\"#DejaVuSans-109\"/>\n      <use x=\"523.726562\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"585.005859\" xlink:href=\"#DejaVuSans-101\"/>\n     </g>\n    </g>\n    <g id=\"line2d_21\">\n     <path d=\"M 59.14375 225.178437 \nL 79.14375 225.178437 \n\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;stroke-width:1.5;\"/>\n    </g>\n    <g id=\"line2d_22\"/>\n    <g id=\"text_21\">\n     <!-- Validation mae -->\n     <defs>\n      <path d=\"M 28.609375 0 \nL 0.78125 72.90625 \nL 11.078125 72.90625 \nL 34.1875 11.53125 \nL 57.328125 72.90625 \nL 67.578125 72.90625 \nL 39.796875 0 \nz\n\" id=\"DejaVuSans-86\"/>\n     </defs>\n     <g transform=\"translate(87.14375 228.678437)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-86\"/>\n      <use x=\"60.658203\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"121.9375\" xlink:href=\"#DejaVuSans-108\"/>\n      <use x=\"149.720703\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"177.503906\" xlink:href=\"#DejaVuSans-100\"/>\n      <use x=\"240.980469\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"302.259766\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"341.46875\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"369.251953\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"430.433594\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"493.8125\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"525.599609\" xlink:href=\"#DejaVuSans-109\"/>\n      <use x=\"623.011719\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"684.291016\" xlink:href=\"#DejaVuSans-101\"/>\n     </g>\n    </g>\n   </g>\n  </g>\n </g>\n <defs>\n  <clipPath id=\"p626fc9965e\">\n   <rect height=\"217.44\" width=\"334.8\" x=\"50.14375\" y=\"22.318125\"/>\n  </clipPath>\n </defs>\n</svg>\n", "image/png": "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\n"}, "metadata": {"needs_background": "light"}}], "source": ["# get the history of the fitting process\n", "history_dict = history.history\n", "loss_values = history_dict['loss']\n", "val_loss_values = history_dict['val_loss']\n", "mae_values = history_dict['mae']\n", "val_mae_values = history_dict['val_mae']\n", "epochs = range(1, len(history_dict['mae']) + 1)\n", "# plot the training and validation losses\n", "plt.plot(epochs, loss_values, label = 'Training loss', color='blue')\n", "plt.plot(epochs, val_loss_values, label = 'Validation loss', color='red')\n", "plt.ylim(0,0.23)\n", "plt.title('Training and validation loss')\n", "plt.xlabel('Epochs')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.show()\n", "# plot the training and validation mae (mean absolute error)\n", "plt.plot(epochs, mae_values, label = 'Training mae', color='blue')\n", "plt.plot(epochs, val_mae_values, label = 'Validation mae', color='red')\n", "plt.ylim(0,0.06)\n", "plt.title('Training and validation mae')\n", "plt.xlabel('Epochs')\n", "plt.ylabel('mae')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["50/50 [==============================] - 0s 1ms/step\n"]}], "source": ["# predict the model on the test set\n", "yhat = model.predict(x_test, verbose=1)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# convert the predictions series\n", "predictions = pd.Series(yhat.reshape((50)), index=df['GBP'].index[-50:])\n", "# extract the actual values\n", "actuals = df['GBP'][-50:]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"output_type": "display_data", "data": {"text/plain": "<Figure size 1440x360 with 1 Axes>", "image/svg+xml": "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n<!-- Created with matplotlib (https://matplotlib.org/) -->\n<svg height=\"351.315625pt\" version=\"1.1\" viewBox=\"0 0 1159.665625 351.315625\" width=\"1159.665625pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n <defs>\n  <style type=\"text/css\">\n*{stroke-linecap:butt;stroke-linejoin:round;}\n  </style>\n </defs>\n <g id=\"figure_1\">\n  <g id=\"patch_1\">\n   <path d=\"M 0 351.315625 \nL 1159.665625 351.315625 \nL 1159.665625 0 \nL 0 0 \nz\n\" style=\"fill:none;\"/>\n  </g>\n  <g id=\"axes_1\">\n   <g id=\"patch_2\">\n    <path d=\"M 36.465625 279 \nL 1152.465625 279 \nL 1152.465625 7.2 \nL 36.465625 7.2 \nz\n\" style=\"fill:#ffffff;\"/>\n   </g>\n   <g id=\"matplotlib.axis_1\">\n    <g id=\"xtick_1\">\n     <g id=\"line2d_1\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 87.192898 279 \nL 87.192898 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_2\">\n      <defs>\n       <path d=\"M 0 0 \nL 0 3.5 \n\" id=\"m948fc8169c\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"87.192898\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_1\">\n      <!-- 2020-06-12 -->\n      <defs>\n       <path d=\"M 19.1875 8.296875 \nL 53.609375 8.296875 \nL 53.609375 0 \nL 7.328125 0 \nL 7.328125 8.296875 \nQ 12.9375 14.109375 22.625 23.890625 \nQ 32.328125 33.6875 34.8125 36.53125 \nQ 39.546875 41.84375 41.421875 45.53125 \nQ 43.3125 49.21875 43.3125 52.78125 \nQ 43.3125 58.59375 39.234375 62.25 \nQ 35.15625 65.921875 28.609375 65.921875 \nQ 23.96875 65.921875 18.8125 64.3125 \nQ 13.671875 62.703125 7.8125 59.421875 \nL 7.8125 69.390625 \nQ 13.765625 71.78125 18.9375 73 \nQ 24.125 74.21875 28.421875 74.21875 \nQ 39.75 74.21875 46.484375 68.546875 \nQ 53.21875 62.890625 53.21875 53.421875 \nQ 53.21875 48.921875 51.53125 44.890625 \nQ 49.859375 40.875 45.40625 35.40625 \nQ 44.1875 33.984375 37.640625 27.21875 \nQ 31.109375 20.453125 19.1875 8.296875 \nz\n\" id=\"DejaVuSans-50\"/>\n       <path d=\"M 31.78125 66.40625 \nQ 24.171875 66.40625 20.328125 58.90625 \nQ 16.5 51.421875 16.5 36.375 \nQ 16.5 21.390625 20.328125 13.890625 \nQ 24.171875 6.390625 31.78125 6.390625 \nQ 39.453125 6.390625 43.28125 13.890625 \nQ 47.125 21.390625 47.125 36.375 \nQ 47.125 51.421875 43.28125 58.90625 \nQ 39.453125 66.40625 31.78125 66.40625 \nz\nM 31.78125 74.21875 \nQ 44.046875 74.21875 50.515625 64.515625 \nQ 56.984375 54.828125 56.984375 36.375 \nQ 56.984375 17.96875 50.515625 8.265625 \nQ 44.046875 -1.421875 31.78125 -1.421875 \nQ 19.53125 -1.421875 13.0625 8.265625 \nQ 6.59375 17.96875 6.59375 36.375 \nQ 6.59375 54.828125 13.0625 64.515625 \nQ 19.53125 74.21875 31.78125 74.21875 \nz\n\" id=\"DejaVuSans-48\"/>\n       <path d=\"M 4.890625 31.390625 \nL 31.203125 31.390625 \nL 31.203125 23.390625 \nL 4.890625 23.390625 \nz\n\" id=\"DejaVuSans-45\"/>\n       <path d=\"M 33.015625 40.375 \nQ 26.375 40.375 22.484375 35.828125 \nQ 18.609375 31.296875 18.609375 23.390625 \nQ 18.609375 15.53125 22.484375 10.953125 \nQ 26.375 6.390625 33.015625 6.390625 \nQ 39.65625 6.390625 43.53125 10.953125 \nQ 47.40625 15.53125 47.40625 23.390625 \nQ 47.40625 31.296875 43.53125 35.828125 \nQ 39.65625 40.375 33.015625 40.375 \nz\nM 52.59375 71.296875 \nL 52.59375 62.3125 \nQ 48.875 64.0625 45.09375 64.984375 \nQ 41.3125 65.921875 37.59375 65.921875 \nQ 27.828125 65.921875 22.671875 59.328125 \nQ 17.53125 52.734375 16.796875 39.40625 \nQ 19.671875 43.65625 24.015625 45.921875 \nQ 28.375 48.1875 33.59375 48.1875 \nQ 44.578125 48.1875 50.953125 41.515625 \nQ 57.328125 34.859375 57.328125 23.390625 \nQ 57.328125 12.15625 50.6875 5.359375 \nQ 44.046875 -1.421875 33.015625 -1.421875 \nQ 20.359375 -1.421875 13.671875 8.265625 \nQ 6.984375 17.96875 6.984375 36.375 \nQ 6.984375 53.65625 15.1875 63.9375 \nQ 23.390625 74.21875 37.203125 74.21875 \nQ 40.921875 74.21875 44.703125 73.484375 \nQ 48.484375 72.75 52.59375 71.296875 \nz\n\" id=\"DejaVuSans-54\"/>\n       <path d=\"M 12.40625 8.296875 \nL 28.515625 8.296875 \nL 28.515625 63.921875 \nL 10.984375 60.40625 \nL 10.984375 69.390625 \nL 28.421875 72.90625 \nL 38.28125 72.90625 \nL 38.28125 8.296875 \nL 54.390625 8.296875 \nL 54.390625 0 \nL 12.40625 0 \nz\n\" id=\"DejaVuSans-49\"/>\n      </defs>\n      <g transform=\"translate(89.952273 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_2\">\n     <g id=\"line2d_3\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 130.673417 279 \nL 130.673417 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_4\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"130.673417\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_2\">\n      <!-- 2020-06-15 -->\n      <defs>\n       <path d=\"M 10.796875 72.90625 \nL 49.515625 72.90625 \nL 49.515625 64.59375 \nL 19.828125 64.59375 \nL 19.828125 46.734375 \nQ 21.96875 47.46875 24.109375 47.828125 \nQ 26.265625 48.1875 28.421875 48.1875 \nQ 40.625 48.1875 47.75 41.5 \nQ 54.890625 34.8125 54.890625 23.390625 \nQ 54.890625 11.625 47.5625 5.09375 \nQ 40.234375 -1.421875 26.90625 -1.421875 \nQ 22.3125 -1.421875 17.546875 -0.640625 \nQ 12.796875 0.140625 7.71875 1.703125 \nL 7.71875 11.625 \nQ 12.109375 9.234375 16.796875 8.0625 \nQ 21.484375 6.890625 26.703125 6.890625 \nQ 35.15625 6.890625 40.078125 11.328125 \nQ 45.015625 15.765625 45.015625 23.390625 \nQ 45.015625 31 40.078125 35.4375 \nQ 35.15625 39.890625 26.703125 39.890625 \nQ 22.75 39.890625 18.8125 39.015625 \nQ 14.890625 38.140625 10.796875 36.28125 \nz\n\" id=\"DejaVuSans-53\"/>\n      </defs>\n      <g transform=\"translate(133.432792 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-53\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_3\">\n     <g id=\"line2d_5\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 145.166924 279 \nL 145.166924 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_6\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"145.166924\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_3\">\n      <!-- 2020-06-16 -->\n      <g transform=\"translate(147.926299 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-54\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_4\">\n     <g id=\"line2d_7\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 159.66043 279 \nL 159.66043 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_8\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"159.66043\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_4\">\n      <!-- 2020-06-17 -->\n      <defs>\n       <path d=\"M 8.203125 72.90625 \nL 55.078125 72.90625 \nL 55.078125 68.703125 \nL 28.609375 0 \nL 18.3125 0 \nL 43.21875 64.59375 \nL 8.203125 64.59375 \nz\n\" id=\"DejaVuSans-55\"/>\n      </defs>\n      <g transform=\"translate(162.419805 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-55\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_5\">\n     <g id=\"line2d_9\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 174.153937 279 \nL 174.153937 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_10\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"174.153937\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_5\">\n      <!-- 2020-06-18 -->\n      <defs>\n       <path d=\"M 31.78125 34.625 \nQ 24.75 34.625 20.71875 30.859375 \nQ 16.703125 27.09375 16.703125 20.515625 \nQ 16.703125 13.921875 20.71875 10.15625 \nQ 24.75 6.390625 31.78125 6.390625 \nQ 38.8125 6.390625 42.859375 10.171875 \nQ 46.921875 13.96875 46.921875 20.515625 \nQ 46.921875 27.09375 42.890625 30.859375 \nQ 38.875 34.625 31.78125 34.625 \nz\nM 21.921875 38.8125 \nQ 15.578125 40.375 12.03125 44.71875 \nQ 8.5 49.078125 8.5 55.328125 \nQ 8.5 64.0625 14.71875 69.140625 \nQ 20.953125 74.21875 31.78125 74.21875 \nQ 42.671875 74.21875 48.875 69.140625 \nQ 55.078125 64.0625 55.078125 55.328125 \nQ 55.078125 49.078125 51.53125 44.71875 \nQ 48 40.375 41.703125 38.8125 \nQ 48.828125 37.15625 52.796875 32.3125 \nQ 56.78125 27.484375 56.78125 20.515625 \nQ 56.78125 9.90625 50.3125 4.234375 \nQ 43.84375 -1.421875 31.78125 -1.421875 \nQ 19.734375 -1.421875 13.25 4.234375 \nQ 6.78125 9.90625 6.78125 20.515625 \nQ 6.78125 27.484375 10.78125 32.3125 \nQ 14.796875 37.15625 21.921875 38.8125 \nz\nM 18.3125 54.390625 \nQ 18.3125 48.734375 21.84375 45.5625 \nQ 25.390625 42.390625 31.78125 42.390625 \nQ 38.140625 42.390625 41.71875 45.5625 \nQ 45.3125 48.734375 45.3125 54.390625 \nQ 45.3125 60.0625 41.71875 63.234375 \nQ 38.140625 66.40625 31.78125 66.40625 \nQ 25.390625 66.40625 21.84375 63.234375 \nQ 18.3125 60.0625 18.3125 54.390625 \nz\n\" id=\"DejaVuSans-56\"/>\n      </defs>\n      <g transform=\"translate(176.913312 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-56\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_6\">\n     <g id=\"line2d_11\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 188.647443 279 \nL 188.647443 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_12\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"188.647443\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_6\">\n      <!-- 2020-06-19 -->\n      <defs>\n       <path d=\"M 10.984375 1.515625 \nL 10.984375 10.5 \nQ 14.703125 8.734375 18.5 7.8125 \nQ 22.3125 6.890625 25.984375 6.890625 \nQ 35.75 6.890625 40.890625 13.453125 \nQ 46.046875 20.015625 46.78125 33.40625 \nQ 43.953125 29.203125 39.59375 26.953125 \nQ 35.25 24.703125 29.984375 24.703125 \nQ 19.046875 24.703125 12.671875 31.3125 \nQ 6.296875 37.9375 6.296875 49.421875 \nQ 6.296875 60.640625 12.9375 67.421875 \nQ 19.578125 74.21875 30.609375 74.21875 \nQ 43.265625 74.21875 49.921875 64.515625 \nQ 56.59375 54.828125 56.59375 36.375 \nQ 56.59375 19.140625 48.40625 8.859375 \nQ 40.234375 -1.421875 26.421875 -1.421875 \nQ 22.703125 -1.421875 18.890625 -0.6875 \nQ 15.09375 0.046875 10.984375 1.515625 \nz\nM 30.609375 32.421875 \nQ 37.25 32.421875 41.125 36.953125 \nQ 45.015625 41.5 45.015625 49.421875 \nQ 45.015625 57.28125 41.125 61.84375 \nQ 37.25 66.40625 30.609375 66.40625 \nQ 23.96875 66.40625 20.09375 61.84375 \nQ 16.21875 57.28125 16.21875 49.421875 \nQ 16.21875 41.5 20.09375 36.953125 \nQ 23.96875 32.421875 30.609375 32.421875 \nz\n\" id=\"DejaVuSans-57\"/>\n      </defs>\n      <g transform=\"translate(191.406818 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-57\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_7\">\n     <g id=\"line2d_13\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 232.127963 279 \nL 232.127963 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_14\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"232.127963\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_7\">\n      <!-- 2020-06-22 -->\n      <g transform=\"translate(234.887338 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_8\">\n     <g id=\"line2d_15\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 246.621469 279 \nL 246.621469 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_16\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"246.621469\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_8\">\n      <!-- 2020-06-23 -->\n      <defs>\n       <path d=\"M 40.578125 39.3125 \nQ 47.65625 37.796875 51.625 33 \nQ 55.609375 28.21875 55.609375 21.1875 \nQ 55.609375 10.40625 48.1875 4.484375 \nQ 40.765625 -1.421875 27.09375 -1.421875 \nQ 22.515625 -1.421875 17.65625 -0.515625 \nQ 12.796875 0.390625 7.625 2.203125 \nL 7.625 11.71875 \nQ 11.71875 9.328125 16.59375 8.109375 \nQ 21.484375 6.890625 26.8125 6.890625 \nQ 36.078125 6.890625 40.9375 10.546875 \nQ 45.796875 14.203125 45.796875 21.1875 \nQ 45.796875 27.640625 41.28125 31.265625 \nQ 36.765625 34.90625 28.71875 34.90625 \nL 20.21875 34.90625 \nL 20.21875 43.015625 \nL 29.109375 43.015625 \nQ 36.375 43.015625 40.234375 45.921875 \nQ 44.09375 48.828125 44.09375 54.296875 \nQ 44.09375 59.90625 40.109375 62.90625 \nQ 36.140625 65.921875 28.71875 65.921875 \nQ 24.65625 65.921875 20.015625 65.03125 \nQ 15.375 64.15625 9.8125 62.3125 \nL 9.8125 71.09375 \nQ 15.4375 72.65625 20.34375 73.4375 \nQ 25.25 74.21875 29.59375 74.21875 \nQ 40.828125 74.21875 47.359375 69.109375 \nQ 53.90625 64.015625 53.90625 55.328125 \nQ 53.90625 49.265625 50.4375 45.09375 \nQ 46.96875 40.921875 40.578125 39.3125 \nz\n\" id=\"DejaVuSans-51\"/>\n      </defs>\n      <g transform=\"translate(249.380844 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-51\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_9\">\n     <g id=\"line2d_17\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 261.114976 279 \nL 261.114976 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_18\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"261.114976\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_9\">\n      <!-- 2020-06-24 -->\n      <defs>\n       <path d=\"M 37.796875 64.3125 \nL 12.890625 25.390625 \nL 37.796875 25.390625 \nz\nM 35.203125 72.90625 \nL 47.609375 72.90625 \nL 47.609375 25.390625 \nL 58.015625 25.390625 \nL 58.015625 17.1875 \nL 47.609375 17.1875 \nL 47.609375 0 \nL 37.796875 0 \nL 37.796875 17.1875 \nL 4.890625 17.1875 \nL 4.890625 26.703125 \nz\n\" id=\"DejaVuSans-52\"/>\n      </defs>\n      <g transform=\"translate(263.874351 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-52\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_10\">\n     <g id=\"line2d_19\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 275.608482 279 \nL 275.608482 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_20\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"275.608482\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_10\">\n      <!-- 2020-06-25 -->\n      <g transform=\"translate(278.367857 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-53\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_11\">\n     <g id=\"line2d_21\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 290.101989 279 \nL 290.101989 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_22\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"290.101989\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_11\">\n      <!-- 2020-06-26 -->\n      <g transform=\"translate(292.861364 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-54\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_12\">\n     <g id=\"line2d_23\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 333.582508 279 \nL 333.582508 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_24\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"333.582508\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_12\">\n      <!-- 2020-06-29 -->\n      <g transform=\"translate(336.341883 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-57\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_13\">\n     <g id=\"line2d_25\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 348.076015 279 \nL 348.076015 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_26\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"348.076015\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_13\">\n      <!-- 2020-06-30 -->\n      <g transform=\"translate(350.83539 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-51\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_14\">\n     <g id=\"line2d_27\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 362.569521 279 \nL 362.569521 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_28\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"362.569521\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_14\">\n      <!-- 2020-07-01 -->\n      <g transform=\"translate(365.328896 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_15\">\n     <g id=\"line2d_29\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 377.063028 279 \nL 377.063028 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_30\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"377.063028\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_15\">\n      <!-- 2020-07-02 -->\n      <g transform=\"translate(379.822403 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_16\">\n     <g id=\"line2d_31\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 435.037054 279 \nL 435.037054 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_32\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"435.037054\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_16\">\n      <!-- 2020-07-06 -->\n      <g transform=\"translate(437.796429 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-54\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_17\">\n     <g id=\"line2d_33\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 449.53056 279 \nL 449.53056 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_34\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"449.53056\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_17\">\n      <!-- 2020-07-07 -->\n      <g transform=\"translate(452.289935 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-55\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_18\">\n     <g id=\"line2d_35\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 464.024067 279 \nL 464.024067 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_36\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"464.024067\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_18\">\n      <!-- 2020-07-08 -->\n      <g transform=\"translate(466.783442 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-56\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_19\">\n     <g id=\"line2d_37\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 478.517573 279 \nL 478.517573 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_38\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"478.517573\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_19\">\n      <!-- 2020-07-09 -->\n      <g transform=\"translate(481.276948 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-57\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_20\">\n     <g id=\"line2d_39\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 493.01108 279 \nL 493.01108 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_40\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"493.01108\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_20\">\n      <!-- 2020-07-10 -->\n      <g transform=\"translate(495.770455 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_21\">\n     <g id=\"line2d_41\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 536.491599 279 \nL 536.491599 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_42\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"536.491599\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_21\">\n      <!-- 2020-07-13 -->\n      <g transform=\"translate(539.250974 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-51\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_22\">\n     <g id=\"line2d_43\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 550.985106 279 \nL 550.985106 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_44\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"550.985106\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_22\">\n      <!-- 2020-07-14 -->\n      <g transform=\"translate(553.744481 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-52\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_23\">\n     <g id=\"line2d_45\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 565.478612 279 \nL 565.478612 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_46\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"565.478612\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_23\">\n      <!-- 2020-07-15 -->\n      <g transform=\"translate(568.237987 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-53\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_24\">\n     <g id=\"line2d_47\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 579.972119 279 \nL 579.972119 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_48\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"579.972119\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_24\">\n      <!-- 2020-07-16 -->\n      <g transform=\"translate(582.731494 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-54\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_25\">\n     <g id=\"line2d_49\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 594.465625 279 \nL 594.465625 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_50\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"594.465625\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_25\">\n      <!-- 2020-07-17 -->\n      <g transform=\"translate(597.225 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-55\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_26\">\n     <g id=\"line2d_51\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 637.946144 279 \nL 637.946144 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_52\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"637.946144\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_26\">\n      <!-- 2020-07-20 -->\n      <g transform=\"translate(640.705519 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_27\">\n     <g id=\"line2d_53\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 652.439651 279 \nL 652.439651 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_54\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"652.439651\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_27\">\n      <!-- 2020-07-21 -->\n      <g transform=\"translate(655.199026 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_28\">\n     <g id=\"line2d_55\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 666.933157 279 \nL 666.933157 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_56\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"666.933157\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_28\">\n      <!-- 2020-07-22 -->\n      <g transform=\"translate(669.692532 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_29\">\n     <g id=\"line2d_57\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 681.426664 279 \nL 681.426664 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_58\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"681.426664\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_29\">\n      <!-- 2020-07-23 -->\n      <g transform=\"translate(684.186039 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-51\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_30\">\n     <g id=\"line2d_59\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 695.92017 279 \nL 695.92017 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_60\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"695.92017\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_30\">\n      <!-- 2020-07-24 -->\n      <g transform=\"translate(698.679545 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-52\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_31\">\n     <g id=\"line2d_61\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 739.40069 279 \nL 739.40069 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_62\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"739.40069\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_31\">\n      <!-- 2020-07-27 -->\n      <g transform=\"translate(742.160065 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-55\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_32\">\n     <g id=\"line2d_63\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 753.894196 279 \nL 753.894196 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_64\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"753.894196\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_32\">\n      <!-- 2020-07-28 -->\n      <g transform=\"translate(756.653571 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-56\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_33\">\n     <g id=\"line2d_65\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 768.387703 279 \nL 768.387703 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_66\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"768.387703\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_33\">\n      <!-- 2020-07-29 -->\n      <g transform=\"translate(771.147078 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-57\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_34\">\n     <g id=\"line2d_67\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 782.881209 279 \nL 782.881209 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_68\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"782.881209\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_34\">\n      <!-- 2020-07-30 -->\n      <g transform=\"translate(785.640584 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-51\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_35\">\n     <g id=\"line2d_69\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 797.374716 279 \nL 797.374716 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_70\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"797.374716\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_35\">\n      <!-- 2020-07-31 -->\n      <g transform=\"translate(800.134091 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-51\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_36\">\n     <g id=\"line2d_71\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 840.855235 279 \nL 840.855235 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_72\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"840.855235\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_36\">\n      <!-- 2020-08-03 -->\n      <g transform=\"translate(843.61461 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-51\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_37\">\n     <g id=\"line2d_73\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 855.348742 279 \nL 855.348742 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_74\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"855.348742\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_37\">\n      <!-- 2020-08-04 -->\n      <g transform=\"translate(858.108117 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-52\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_38\">\n     <g id=\"line2d_75\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 869.842248 279 \nL 869.842248 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_76\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"869.842248\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_38\">\n      <!-- 2020-08-05 -->\n      <g transform=\"translate(872.601623 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-53\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_39\">\n     <g id=\"line2d_77\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 884.335755 279 \nL 884.335755 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_78\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"884.335755\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_39\">\n      <!-- 2020-08-06 -->\n      <g transform=\"translate(887.09513 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-54\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_40\">\n     <g id=\"line2d_79\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 898.829261 279 \nL 898.829261 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_80\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"898.829261\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_40\">\n      <!-- 2020-08-07 -->\n      <g transform=\"translate(901.588636 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-55\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_41\">\n     <g id=\"line2d_81\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 942.309781 279 \nL 942.309781 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_82\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"942.309781\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_41\">\n      <!-- 2020-08-10 -->\n      <g transform=\"translate(945.069156 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_42\">\n     <g id=\"line2d_83\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 956.803287 279 \nL 956.803287 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_84\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"956.803287\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_42\">\n      <!-- 2020-08-11 -->\n      <g transform=\"translate(959.562662 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_43\">\n     <g id=\"line2d_85\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 971.296794 279 \nL 971.296794 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_86\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"971.296794\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_43\">\n      <!-- 2020-08-12 -->\n      <g transform=\"translate(974.056169 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_44\">\n     <g id=\"line2d_87\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 985.7903 279 \nL 985.7903 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_88\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"985.7903\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_44\">\n      <!-- 2020-08-13 -->\n      <g transform=\"translate(988.549675 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-51\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_45\">\n     <g id=\"line2d_89\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 1000.283807 279 \nL 1000.283807 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_90\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"1000.283807\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_45\">\n      <!-- 2020-08-14 -->\n      <g transform=\"translate(1003.043182 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-52\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_46\">\n     <g id=\"line2d_91\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 1043.764326 279 \nL 1043.764326 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_92\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"1043.764326\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_46\">\n      <!-- 2020-08-17 -->\n      <g transform=\"translate(1046.523701 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-55\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_47\">\n     <g id=\"line2d_93\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 1058.257833 279 \nL 1058.257833 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_94\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"1058.257833\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_47\">\n      <!-- 2020-08-18 -->\n      <g transform=\"translate(1061.017208 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-56\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_48\">\n     <g id=\"line2d_95\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 1072.751339 279 \nL 1072.751339 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_96\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"1072.751339\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_48\">\n      <!-- 2020-08-19 -->\n      <g transform=\"translate(1075.510714 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-57\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_49\">\n     <g id=\"line2d_97\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 1087.244846 279 \nL 1087.244846 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_98\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"1087.244846\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_49\">\n      <!-- 2020-08-20 -->\n      <g transform=\"translate(1090.004221 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_50\">\n     <g id=\"line2d_99\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 1101.738352 279 \nL 1101.738352 7.2 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_100\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"1101.738352\" xlink:href=\"#m948fc8169c\" y=\"279\"/>\n      </g>\n     </g>\n     <g id=\"text_50\">\n      <!-- 2020-08-21 -->\n      <g transform=\"translate(1104.497727 344.115625)rotate(-90)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n   </g>\n   <g id=\"matplotlib.axis_2\">\n    <g id=\"ytick_1\">\n     <g id=\"line2d_101\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 36.465625 235.140713 \nL 1152.465625 235.140713 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_102\">\n      <defs>\n       <path d=\"M 0 0 \nL -3.5 0 \n\" id=\"m2a2f1d5291\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"36.465625\" xlink:href=\"#m2a2f1d5291\" y=\"235.140713\"/>\n      </g>\n     </g>\n     <g id=\"text_51\">\n      <!-- 1.24 -->\n      <defs>\n       <path d=\"M 10.6875 12.40625 \nL 21 12.40625 \nL 21 0 \nL 10.6875 0 \nz\n\" id=\"DejaVuSans-46\"/>\n      </defs>\n      <g transform=\"translate(7.2 238.939931)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-52\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_2\">\n     <g id=\"line2d_103\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 36.465625 183.066759 \nL 1152.465625 183.066759 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_104\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"36.465625\" xlink:href=\"#m2a2f1d5291\" y=\"183.066759\"/>\n      </g>\n     </g>\n     <g id=\"text_52\">\n      <!-- 1.26 -->\n      <g transform=\"translate(7.2 186.865978)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-54\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_3\">\n     <g id=\"line2d_105\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 36.465625 130.992806 \nL 1152.465625 130.992806 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_106\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"36.465625\" xlink:href=\"#m2a2f1d5291\" y=\"130.992806\"/>\n      </g>\n     </g>\n     <g id=\"text_53\">\n      <!-- 1.28 -->\n      <g transform=\"translate(7.2 134.792025)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-56\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_4\">\n     <g id=\"line2d_107\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 36.465625 78.918852 \nL 1152.465625 78.918852 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_108\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"36.465625\" xlink:href=\"#m2a2f1d5291\" y=\"78.918852\"/>\n      </g>\n     </g>\n     <g id=\"text_54\">\n      <!-- 1.30 -->\n      <g transform=\"translate(7.2 82.718071)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-51\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_5\">\n     <g id=\"line2d_109\">\n      <path clip-path=\"url(#pd0d284ed72)\" d=\"M 36.465625 26.844899 \nL 1152.465625 26.844899 \n\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n     </g>\n     <g id=\"line2d_110\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"36.465625\" xlink:href=\"#m2a2f1d5291\" y=\"26.844899\"/>\n      </g>\n     </g>\n     <g id=\"text_55\">\n      <!-- 1.32 -->\n      <g transform=\"translate(7.2 30.644118)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-51\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n   </g>\n   <g id=\"line2d_111\">\n    <path clip-path=\"url(#pd0d284ed72)\" d=\"M 87.192898 202.073752 \nL 130.673417 197.907836 \nL 145.166924 186.711936 \nL 159.66043 202.334122 \nL 174.153937 227.32962 \nL 188.647443 245.034764 \nL 232.127963 222.903334 \nL 246.621469 201.032273 \nL 261.114976 226.80888 \nL 275.608482 233.578494 \nL 290.101989 251.544008 \nL 333.582508 266.645455 \nL 348.076015 243.212175 \nL 362.569521 215.87335 \nL 377.063028 217.175199 \nL 435.037054 213.790392 \nL 449.53056 190.357113 \nL 464.024067 184.889348 \nL 478.517573 179.421583 \nL 493.01108 169.006792 \nL 536.491599 179.421583 \nL 550.985106 197.126727 \nL 565.478612 186.711936 \nL 579.972119 177.598994 \nL 594.465625 196.085248 \nL 637.946144 167.965313 \nL 652.439651 147.656471 \nL 666.933157 149.479059 \nL 681.426664 141.667966 \nL 695.92017 133.336134 \nL 739.40069 108.340636 \nL 753.894196 91.937341 \nL 768.387703 85.688466 \nL 782.881209 69.805911 \nL 797.374716 44.289673 \nL 840.855235 65.119255 \nL 855.348742 63.557036 \nL 869.842248 42.206715 \nL 884.335755 40.644497 \nL 898.829261 67.722952 \nL 942.309781 57.568531 \nL 956.803287 58.87038 \nL 971.296794 66.681473 \nL 985.7903 57.568531 \nL 1000.283807 50.798918 \nL 1043.764326 51.580027 \nL 1058.257833 19.554545 \nL 1072.751339 29.188227 \nL 1087.244846 29.448597 \nL 1101.738352 53.402615 \n\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;stroke-width:1.5;\"/>\n   </g>\n   <g id=\"line2d_112\">\n    <path clip-path=\"url(#pd0d284ed72)\" d=\"M 87.192898 244.15706 \nL 130.673417 242.581236 \nL 145.166924 243.116029 \nL 159.66043 238.154215 \nL 174.153937 234.704597 \nL 188.647443 232.00611 \nL 232.127963 230.324755 \nL 246.621469 228.835218 \nL 261.114976 225.173606 \nL 275.608482 222.373313 \nL 290.101989 223.078508 \nL 333.582508 221.609146 \nL 348.076015 222.738947 \nL 362.569521 227.524462 \nL 377.063028 228.170994 \nL 435.037054 222.391005 \nL 449.53056 226.904313 \nL 464.024067 231.511357 \nL 478.517573 231.021569 \nL 493.01108 229.905425 \nL 536.491599 227.948758 \nL 550.985106 225.641977 \nL 565.478612 224.194652 \nL 579.972119 227.722798 \nL 594.465625 226.658798 \nL 637.946144 228.870602 \nL 652.439651 223.913443 \nL 666.933157 225.877249 \nL 681.426664 217.642116 \nL 695.92017 219.198075 \nL 739.40069 210.837857 \nL 753.894196 205.919807 \nL 768.387703 200.448652 \nL 782.881209 197.243306 \nL 797.374716 193.276897 \nL 840.855235 188.304841 \nL 855.348742 184.734482 \nL 869.842248 182.609277 \nL 884.335755 181.948157 \nL 898.829261 170.666905 \nL 942.309781 163.257396 \nL 956.803287 160.183343 \nL 971.296794 156.751727 \nL 985.7903 150.701083 \nL 1000.283807 147.461906 \nL 1043.764326 143.042645 \nL 1058.257833 134.272408 \nL 1072.751339 131.740908 \nL 1087.244846 132.168308 \nL 1101.738352 130.617004 \n\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;stroke-width:1.5;\"/>\n   </g>\n   <g id=\"patch_3\">\n    <path d=\"M 36.465625 279 \nL 36.465625 7.2 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_4\">\n    <path d=\"M 1152.465625 279 \nL 1152.465625 7.2 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_5\">\n    <path d=\"M 36.465625 279 \nL 1152.465625 279 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_6\">\n    <path d=\"M 36.465625 7.2 \nL 1152.465625 7.2 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"legend_1\">\n    <g id=\"patch_7\">\n     <path d=\"M 43.465625 44.55625 \nL 154.165625 44.55625 \nQ 156.165625 44.55625 156.165625 42.55625 \nL 156.165625 14.2 \nQ 156.165625 12.2 154.165625 12.2 \nL 43.465625 12.2 \nQ 41.465625 12.2 41.465625 14.2 \nL 41.465625 42.55625 \nQ 41.465625 44.55625 43.465625 44.55625 \nz\n\" style=\"fill:#ffffff;opacity:0.8;stroke:#cccccc;stroke-linejoin:miter;\"/>\n    </g>\n    <g id=\"line2d_113\">\n     <path d=\"M 45.465625 20.298438 \nL 65.465625 20.298438 \n\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;stroke-width:1.5;\"/>\n    </g>\n    <g id=\"line2d_114\"/>\n    <g id=\"text_56\">\n     <!-- GBP Actual -->\n     <defs>\n      <path d=\"M 59.515625 10.40625 \nL 59.515625 29.984375 \nL 43.40625 29.984375 \nL 43.40625 38.09375 \nL 69.28125 38.09375 \nL 69.28125 6.78125 \nQ 63.578125 2.734375 56.6875 0.65625 \nQ 49.8125 -1.421875 42 -1.421875 \nQ 24.90625 -1.421875 15.25 8.5625 \nQ 5.609375 18.5625 5.609375 36.375 \nQ 5.609375 54.25 15.25 64.234375 \nQ 24.90625 74.21875 42 74.21875 \nQ 49.125 74.21875 55.546875 72.453125 \nQ 61.96875 70.703125 67.390625 67.28125 \nL 67.390625 56.78125 \nQ 61.921875 61.421875 55.765625 63.765625 \nQ 49.609375 66.109375 42.828125 66.109375 \nQ 29.4375 66.109375 22.71875 58.640625 \nQ 16.015625 51.171875 16.015625 36.375 \nQ 16.015625 21.625 22.71875 14.15625 \nQ 29.4375 6.6875 42.828125 6.6875 \nQ 48.046875 6.6875 52.140625 7.59375 \nQ 56.25 8.5 59.515625 10.40625 \nz\n\" id=\"DejaVuSans-71\"/>\n      <path d=\"M 19.671875 34.8125 \nL 19.671875 8.109375 \nL 35.5 8.109375 \nQ 43.453125 8.109375 47.28125 11.40625 \nQ 51.125 14.703125 51.125 21.484375 \nQ 51.125 28.328125 47.28125 31.5625 \nQ 43.453125 34.8125 35.5 34.8125 \nz\nM 19.671875 64.796875 \nL 19.671875 42.828125 \nL 34.28125 42.828125 \nQ 41.5 42.828125 45.03125 45.53125 \nQ 48.578125 48.25 48.578125 53.8125 \nQ 48.578125 59.328125 45.03125 62.0625 \nQ 41.5 64.796875 34.28125 64.796875 \nz\nM 9.8125 72.90625 \nL 35.015625 72.90625 \nQ 46.296875 72.90625 52.390625 68.21875 \nQ 58.5 63.53125 58.5 54.890625 \nQ 58.5 48.1875 55.375 44.234375 \nQ 52.25 40.28125 46.1875 39.3125 \nQ 53.46875 37.75 57.5 32.78125 \nQ 61.53125 27.828125 61.53125 20.40625 \nQ 61.53125 10.640625 54.890625 5.3125 \nQ 48.25 0 35.984375 0 \nL 9.8125 0 \nz\n\" id=\"DejaVuSans-66\"/>\n      <path d=\"M 19.671875 64.796875 \nL 19.671875 37.40625 \nL 32.078125 37.40625 \nQ 38.96875 37.40625 42.71875 40.96875 \nQ 46.484375 44.53125 46.484375 51.125 \nQ 46.484375 57.671875 42.71875 61.234375 \nQ 38.96875 64.796875 32.078125 64.796875 \nz\nM 9.8125 72.90625 \nL 32.078125 72.90625 \nQ 44.34375 72.90625 50.609375 67.359375 \nQ 56.890625 61.8125 56.890625 51.125 \nQ 56.890625 40.328125 50.609375 34.8125 \nQ 44.34375 29.296875 32.078125 29.296875 \nL 19.671875 29.296875 \nL 19.671875 0 \nL 9.8125 0 \nz\n\" id=\"DejaVuSans-80\"/>\n      <path id=\"DejaVuSans-32\"/>\n      <path d=\"M 34.1875 63.1875 \nL 20.796875 26.90625 \nL 47.609375 26.90625 \nz\nM 28.609375 72.90625 \nL 39.796875 72.90625 \nL 67.578125 0 \nL 57.328125 0 \nL 50.6875 18.703125 \nL 17.828125 18.703125 \nL 11.1875 0 \nL 0.78125 0 \nz\n\" id=\"DejaVuSans-65\"/>\n      <path d=\"M 48.78125 52.59375 \nL 48.78125 44.1875 \nQ 44.96875 46.296875 41.140625 47.34375 \nQ 37.3125 48.390625 33.40625 48.390625 \nQ 24.65625 48.390625 19.8125 42.84375 \nQ 14.984375 37.3125 14.984375 27.296875 \nQ 14.984375 17.28125 19.8125 11.734375 \nQ 24.65625 6.203125 33.40625 6.203125 \nQ 37.3125 6.203125 41.140625 7.25 \nQ 44.96875 8.296875 48.78125 10.40625 \nL 48.78125 2.09375 \nQ 45.015625 0.34375 40.984375 -0.53125 \nQ 36.96875 -1.421875 32.421875 -1.421875 \nQ 20.0625 -1.421875 12.78125 6.34375 \nQ 5.515625 14.109375 5.515625 27.296875 \nQ 5.515625 40.671875 12.859375 48.328125 \nQ 20.21875 56 33.015625 56 \nQ 37.15625 56 41.109375 55.140625 \nQ 45.0625 54.296875 48.78125 52.59375 \nz\n\" id=\"DejaVuSans-99\"/>\n      <path d=\"M 18.3125 70.21875 \nL 18.3125 54.6875 \nL 36.8125 54.6875 \nL 36.8125 47.703125 \nL 18.3125 47.703125 \nL 18.3125 18.015625 \nQ 18.3125 11.328125 20.140625 9.421875 \nQ 21.96875 7.515625 27.59375 7.515625 \nL 36.8125 7.515625 \nL 36.8125 0 \nL 27.59375 0 \nQ 17.1875 0 13.234375 3.875 \nQ 9.28125 7.765625 9.28125 18.015625 \nL 9.28125 47.703125 \nL 2.6875 47.703125 \nL 2.6875 54.6875 \nL 9.28125 54.6875 \nL 9.28125 70.21875 \nz\n\" id=\"DejaVuSans-116\"/>\n      <path d=\"M 8.5 21.578125 \nL 8.5 54.6875 \nL 17.484375 54.6875 \nL 17.484375 21.921875 \nQ 17.484375 14.15625 20.5 10.265625 \nQ 23.53125 6.390625 29.59375 6.390625 \nQ 36.859375 6.390625 41.078125 11.03125 \nQ 45.3125 15.671875 45.3125 23.6875 \nL 45.3125 54.6875 \nL 54.296875 54.6875 \nL 54.296875 0 \nL 45.3125 0 \nL 45.3125 8.40625 \nQ 42.046875 3.421875 37.71875 1 \nQ 33.40625 -1.421875 27.6875 -1.421875 \nQ 18.265625 -1.421875 13.375 4.4375 \nQ 8.5 10.296875 8.5 21.578125 \nz\nM 31.109375 56 \nz\n\" id=\"DejaVuSans-117\"/>\n      <path d=\"M 34.28125 27.484375 \nQ 23.390625 27.484375 19.1875 25 \nQ 14.984375 22.515625 14.984375 16.5 \nQ 14.984375 11.71875 18.140625 8.90625 \nQ 21.296875 6.109375 26.703125 6.109375 \nQ 34.1875 6.109375 38.703125 11.40625 \nQ 43.21875 16.703125 43.21875 25.484375 \nL 43.21875 27.484375 \nz\nM 52.203125 31.203125 \nL 52.203125 0 \nL 43.21875 0 \nL 43.21875 8.296875 \nQ 40.140625 3.328125 35.546875 0.953125 \nQ 30.953125 -1.421875 24.3125 -1.421875 \nQ 15.921875 -1.421875 10.953125 3.296875 \nQ 6 8.015625 6 15.921875 \nQ 6 25.140625 12.171875 29.828125 \nQ 18.359375 34.515625 30.609375 34.515625 \nL 43.21875 34.515625 \nL 43.21875 35.40625 \nQ 43.21875 41.609375 39.140625 45 \nQ 35.0625 48.390625 27.6875 48.390625 \nQ 23 48.390625 18.546875 47.265625 \nQ 14.109375 46.140625 10.015625 43.890625 \nL 10.015625 52.203125 \nQ 14.9375 54.109375 19.578125 55.046875 \nQ 24.21875 56 28.609375 56 \nQ 40.484375 56 46.34375 49.84375 \nQ 52.203125 43.703125 52.203125 31.203125 \nz\n\" id=\"DejaVuSans-97\"/>\n      <path d=\"M 9.421875 75.984375 \nL 18.40625 75.984375 \nL 18.40625 0 \nL 9.421875 0 \nz\n\" id=\"DejaVuSans-108\"/>\n     </defs>\n     <g transform=\"translate(73.465625 23.798438)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-71\"/>\n      <use x=\"77.490234\" xlink:href=\"#DejaVuSans-66\"/>\n      <use x=\"146.09375\" xlink:href=\"#DejaVuSans-80\"/>\n      <use x=\"206.396484\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"238.183594\" xlink:href=\"#DejaVuSans-65\"/>\n      <use x=\"304.841797\" xlink:href=\"#DejaVuSans-99\"/>\n      <use x=\"359.822266\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"399.03125\" xlink:href=\"#DejaVuSans-117\"/>\n      <use x=\"462.410156\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"523.689453\" xlink:href=\"#DejaVuSans-108\"/>\n     </g>\n    </g>\n    <g id=\"line2d_115\">\n     <path d=\"M 45.465625 34.976563 \nL 65.465625 34.976563 \n\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;stroke-width:1.5;\"/>\n    </g>\n    <g id=\"line2d_116\"/>\n    <g id=\"text_57\">\n     <!-- GBP Predictions -->\n     <defs>\n      <path d=\"M 41.109375 46.296875 \nQ 39.59375 47.171875 37.8125 47.578125 \nQ 36.03125 48 33.890625 48 \nQ 26.265625 48 22.1875 43.046875 \nQ 18.109375 38.09375 18.109375 28.8125 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 20.953125 51.171875 25.484375 53.578125 \nQ 30.03125 56 36.53125 56 \nQ 37.453125 56 38.578125 55.875 \nQ 39.703125 55.765625 41.0625 55.515625 \nz\n\" id=\"DejaVuSans-114\"/>\n      <path d=\"M 56.203125 29.59375 \nL 56.203125 25.203125 \nL 14.890625 25.203125 \nQ 15.484375 15.921875 20.484375 11.0625 \nQ 25.484375 6.203125 34.421875 6.203125 \nQ 39.59375 6.203125 44.453125 7.46875 \nQ 49.3125 8.734375 54.109375 11.28125 \nL 54.109375 2.78125 \nQ 49.265625 0.734375 44.1875 -0.34375 \nQ 39.109375 -1.421875 33.890625 -1.421875 \nQ 20.796875 -1.421875 13.15625 6.1875 \nQ 5.515625 13.8125 5.515625 26.8125 \nQ 5.515625 40.234375 12.765625 48.109375 \nQ 20.015625 56 32.328125 56 \nQ 43.359375 56 49.78125 48.890625 \nQ 56.203125 41.796875 56.203125 29.59375 \nz\nM 47.21875 32.234375 \nQ 47.125 39.59375 43.09375 43.984375 \nQ 39.0625 48.390625 32.421875 48.390625 \nQ 24.90625 48.390625 20.390625 44.140625 \nQ 15.875 39.890625 15.1875 32.171875 \nz\n\" id=\"DejaVuSans-101\"/>\n      <path d=\"M 45.40625 46.390625 \nL 45.40625 75.984375 \nL 54.390625 75.984375 \nL 54.390625 0 \nL 45.40625 0 \nL 45.40625 8.203125 \nQ 42.578125 3.328125 38.25 0.953125 \nQ 33.9375 -1.421875 27.875 -1.421875 \nQ 17.96875 -1.421875 11.734375 6.484375 \nQ 5.515625 14.40625 5.515625 27.296875 \nQ 5.515625 40.1875 11.734375 48.09375 \nQ 17.96875 56 27.875 56 \nQ 33.9375 56 38.25 53.625 \nQ 42.578125 51.265625 45.40625 46.390625 \nz\nM 14.796875 27.296875 \nQ 14.796875 17.390625 18.875 11.75 \nQ 22.953125 6.109375 30.078125 6.109375 \nQ 37.203125 6.109375 41.296875 11.75 \nQ 45.40625 17.390625 45.40625 27.296875 \nQ 45.40625 37.203125 41.296875 42.84375 \nQ 37.203125 48.484375 30.078125 48.484375 \nQ 22.953125 48.484375 18.875 42.84375 \nQ 14.796875 37.203125 14.796875 27.296875 \nz\n\" id=\"DejaVuSans-100\"/>\n      <path d=\"M 9.421875 54.6875 \nL 18.40625 54.6875 \nL 18.40625 0 \nL 9.421875 0 \nz\nM 9.421875 75.984375 \nL 18.40625 75.984375 \nL 18.40625 64.59375 \nL 9.421875 64.59375 \nz\n\" id=\"DejaVuSans-105\"/>\n      <path d=\"M 30.609375 48.390625 \nQ 23.390625 48.390625 19.1875 42.75 \nQ 14.984375 37.109375 14.984375 27.296875 \nQ 14.984375 17.484375 19.15625 11.84375 \nQ 23.34375 6.203125 30.609375 6.203125 \nQ 37.796875 6.203125 41.984375 11.859375 \nQ 46.1875 17.53125 46.1875 27.296875 \nQ 46.1875 37.015625 41.984375 42.703125 \nQ 37.796875 48.390625 30.609375 48.390625 \nz\nM 30.609375 56 \nQ 42.328125 56 49.015625 48.375 \nQ 55.71875 40.765625 55.71875 27.296875 \nQ 55.71875 13.875 49.015625 6.21875 \nQ 42.328125 -1.421875 30.609375 -1.421875 \nQ 18.84375 -1.421875 12.171875 6.21875 \nQ 5.515625 13.875 5.515625 27.296875 \nQ 5.515625 40.765625 12.171875 48.375 \nQ 18.84375 56 30.609375 56 \nz\n\" id=\"DejaVuSans-111\"/>\n      <path d=\"M 54.890625 33.015625 \nL 54.890625 0 \nL 45.90625 0 \nL 45.90625 32.71875 \nQ 45.90625 40.484375 42.875 44.328125 \nQ 39.84375 48.1875 33.796875 48.1875 \nQ 26.515625 48.1875 22.3125 43.546875 \nQ 18.109375 38.921875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 21.34375 51.125 25.703125 53.5625 \nQ 30.078125 56 35.796875 56 \nQ 45.21875 56 50.046875 50.171875 \nQ 54.890625 44.34375 54.890625 33.015625 \nz\n\" id=\"DejaVuSans-110\"/>\n      <path d=\"M 44.28125 53.078125 \nL 44.28125 44.578125 \nQ 40.484375 46.53125 36.375 47.5 \nQ 32.28125 48.484375 27.875 48.484375 \nQ 21.1875 48.484375 17.84375 46.4375 \nQ 14.5 44.390625 14.5 40.28125 \nQ 14.5 37.15625 16.890625 35.375 \nQ 19.28125 33.59375 26.515625 31.984375 \nL 29.59375 31.296875 \nQ 39.15625 29.25 43.1875 25.515625 \nQ 47.21875 21.78125 47.21875 15.09375 \nQ 47.21875 7.46875 41.1875 3.015625 \nQ 35.15625 -1.421875 24.609375 -1.421875 \nQ 20.21875 -1.421875 15.453125 -0.5625 \nQ 10.6875 0.296875 5.421875 2 \nL 5.421875 11.28125 \nQ 10.40625 8.6875 15.234375 7.390625 \nQ 20.0625 6.109375 24.8125 6.109375 \nQ 31.15625 6.109375 34.5625 8.28125 \nQ 37.984375 10.453125 37.984375 14.40625 \nQ 37.984375 18.0625 35.515625 20.015625 \nQ 33.0625 21.96875 24.703125 23.78125 \nL 21.578125 24.515625 \nQ 13.234375 26.265625 9.515625 29.90625 \nQ 5.8125 33.546875 5.8125 39.890625 \nQ 5.8125 47.609375 11.28125 51.796875 \nQ 16.75 56 26.8125 56 \nQ 31.78125 56 36.171875 55.265625 \nQ 40.578125 54.546875 44.28125 53.078125 \nz\n\" id=\"DejaVuSans-115\"/>\n     </defs>\n     <g transform=\"translate(73.465625 38.476563)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-71\"/>\n      <use x=\"77.490234\" xlink:href=\"#DejaVuSans-66\"/>\n      <use x=\"146.09375\" xlink:href=\"#DejaVuSans-80\"/>\n      <use x=\"206.396484\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"238.183594\" xlink:href=\"#DejaVuSans-80\"/>\n      <use x=\"296.736328\" xlink:href=\"#DejaVuSans-114\"/>\n      <use x=\"335.599609\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"397.123047\" xlink:href=\"#DejaVuSans-100\"/>\n      <use x=\"460.599609\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"488.382812\" xlink:href=\"#DejaVuSans-99\"/>\n      <use x=\"543.363281\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"582.572266\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"610.355469\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"671.537109\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"734.916016\" xlink:href=\"#DejaVuSans-115\"/>\n     </g>\n    </g>\n   </g>\n  </g>\n </g>\n <defs>\n  <clipPath id=\"pd0d284ed72\">\n   <rect height=\"271.8\" width=\"1116\" x=\"36.465625\" y=\"7.2\"/>\n  </clipPath>\n </defs>\n</svg>\n", "image/png": "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\n"}, "metadata": {"needs_background": "light"}}, {"output_type": "stream", "name": "stdout", "text": ["Test RMSE: 0.03028\n"]}], "source": ["# evaluate forecasts and plot them together\n", "plt.figure(figsize=(20,5))\n", "plt.plot(actuals.index, actuals, color='blue')\n", "plt.plot(actuals.index, predictions, color='red')\n", "plt.legend(('GBP Actual', 'GBP Predictions'))\n", "plt.xticks(actuals.index, rotation=90)\n", "plt.grid(True)\n", "plt.show()\n", "\n", "rmse = sqrt(mean_squared_error(actuals, predictions))\n", "print('Test RMSE: %.5f' % rmse)"]}]}