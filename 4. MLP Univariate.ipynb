{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MLP Univariate"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Using TensorFlow backend.\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import tensorflow as tf\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import mean_squared_error\n", "import tensorflow as tf\n", "import keras\n", "from keras.backend import sigmoid\n", "from keras.models import Sequential\n", "from keras import models, layers, backend, optimizers\n", "from keras.layers import Dropout, BatchNormalization\n", "from keras.callbacks import EarlyStopping\n", "import time\n", "from math import sqrt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>AUD</th>\n", "      <th>EUR</th>\n", "      <th>NZD</th>\n", "      <th>GBP</th>\n", "      <th>BRL</th>\n", "      <th>CAD</th>\n", "      <th>CNY</th>\n", "      <th>DKK</th>\n", "      <th>HKD</th>\n", "      <th>INR</th>\n", "      <th>...</th>\n", "      <th>CHF</th>\n", "      <th>TWD</th>\n", "      <th>THB</th>\n", "      <th>VEB</th>\n", "      <th>gdpGBP</th>\n", "      <th>gdpUSD</th>\n", "      <th>GBR_Value</th>\n", "      <th>USA_Value</th>\n", "      <th>liborGBP</th>\n", "      <th>liborUSD</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2001-01-02</th>\n", "      <td>0.5592</td>\n", "      <td>0.9465</td>\n", "      <td>0.4432</td>\n", "      <td>1.4977</td>\n", "      <td>1.938</td>\n", "      <td>1.4963</td>\n", "      <td>8.2779</td>\n", "      <td>7.8845</td>\n", "      <td>7.8000</td>\n", "      <td>46.74</td>\n", "      <td>...</td>\n", "      <td>1.6075</td>\n", "      <td>33.000</td>\n", "      <td>43.79</td>\n", "      <td>0.7008</td>\n", "      <td>99.977643</td>\n", "      <td>100.815277</td>\n", "      <td>-1.815904</td>\n", "      <td>-4.084297</td>\n", "      <td>5.81094</td>\n", "      <td>6.65125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2001-01-03</th>\n", "      <td>0.5635</td>\n", "      <td>0.9473</td>\n", "      <td>0.4463</td>\n", "      <td>1.5045</td>\n", "      <td>1.946</td>\n", "      <td>1.4982</td>\n", "      <td>8.2773</td>\n", "      <td>7.8750</td>\n", "      <td>7.8000</td>\n", "      <td>46.75</td>\n", "      <td>...</td>\n", "      <td>1.6025</td>\n", "      <td>33.078</td>\n", "      <td>43.70</td>\n", "      <td>0.7002</td>\n", "      <td>99.980866</td>\n", "      <td>100.807191</td>\n", "      <td>-1.824859</td>\n", "      <td>-4.076654</td>\n", "      <td>6.09750</td>\n", "      <td>6.65375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2001-01-04</th>\n", "      <td>0.5655</td>\n", "      <td>0.9448</td>\n", "      <td>0.4457</td>\n", "      <td>1.4930</td>\n", "      <td>1.938</td>\n", "      <td>1.4985</td>\n", "      <td>8.2781</td>\n", "      <td>7.8991</td>\n", "      <td>7.7998</td>\n", "      <td>46.78</td>\n", "      <td>...</td>\n", "      <td>1.6115</td>\n", "      <td>33.000</td>\n", "      <td>43.53</td>\n", "      <td>0.6994</td>\n", "      <td>99.984089</td>\n", "      <td>100.799105</td>\n", "      <td>-1.833815</td>\n", "      <td>-4.069010</td>\n", "      <td>5.57125</td>\n", "      <td>6.09625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2001-01-05</th>\n", "      <td>0.5712</td>\n", "      <td>0.9535</td>\n", "      <td>0.4518</td>\n", "      <td>1.4990</td>\n", "      <td>1.953</td>\n", "      <td>1.5003</td>\n", "      <td>8.2775</td>\n", "      <td>7.8260</td>\n", "      <td>7.7993</td>\n", "      <td>46.76</td>\n", "      <td>...</td>\n", "      <td>1.6025</td>\n", "      <td>32.927</td>\n", "      <td>43.26</td>\n", "      <td>0.6988</td>\n", "      <td>99.987312</td>\n", "      <td>100.791019</td>\n", "      <td>-1.842770</td>\n", "      <td>-4.061367</td>\n", "      <td>5.37813</td>\n", "      <td>6.01625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2001-01-08</th>\n", "      <td>0.5660</td>\n", "      <td>0.9486</td>\n", "      <td>0.4505</td>\n", "      <td>1.4969</td>\n", "      <td>1.954</td>\n", "      <td>1.4944</td>\n", "      <td>8.2778</td>\n", "      <td>7.8705</td>\n", "      <td>7.7998</td>\n", "      <td>46.73</td>\n", "      <td>...</td>\n", "      <td>1.6076</td>\n", "      <td>32.850</td>\n", "      <td>42.95</td>\n", "      <td>0.6990</td>\n", "      <td>99.990535</td>\n", "      <td>100.782933</td>\n", "      <td>-1.851726</td>\n", "      <td>-4.053723</td>\n", "      <td>5.50000</td>\n", "      <td>6.01500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 29 columns</p>\n", "</div>"], "text/plain": ["               AUD     EUR     NZD     GBP    BRL     CAD     CNY     DKK  \\\n", "Date                                                                        \n", "2001-01-02  0.5592  0.9465  0.4432  1.4977  1.938  1.4963  8.2779  7.8845   \n", "2001-01-03  0.5635  0.9473  0.4463  1.5045  1.946  1.4982  8.2773  7.8750   \n", "2001-01-04  0.5655  0.9448  0.4457  1.4930  1.938  1.4985  8.2781  7.8991   \n", "2001-01-05  0.5712  0.9535  0.4518  1.4990  1.953  1.5003  8.2775  7.8260   \n", "2001-01-08  0.5660  0.9486  0.4505  1.4969  1.954  1.4944  8.2778  7.8705   \n", "\n", "               HKD    INR  ...     CHF     TWD    THB     VEB     gdpGBP  \\\n", "Date                       ...                                             \n", "2001-01-02  7.8000  46.74  ...  1.6075  33.000  43.79  0.7008  99.977643   \n", "2001-01-03  7.8000  46.75  ...  1.6025  33.078  43.70  0.7002  99.980866   \n", "2001-01-04  7.7998  46.78  ...  1.6115  33.000  43.53  0.6994  99.984089   \n", "2001-01-05  7.7993  46.76  ...  1.6025  32.927  43.26  0.6988  99.987312   \n", "2001-01-08  7.7998  46.73  ...  1.6076  32.850  42.95  0.6990  99.990535   \n", "\n", "                gdpUSD  GBR_Value  USA_Value  liborGBP  liborUSD  \n", "Date                                                              \n", "2001-01-02  100.815277  -1.815904  -4.084297   5.81094   6.65125  \n", "2001-01-03  100.807191  -1.824859  -4.076654   6.09750   6.65375  \n", "2001-01-04  100.799105  -1.833815  -4.069010   5.57125   6.09625  \n", "2001-01-05  100.791019  -1.842770  -4.061367   5.37813   6.01625  \n", "2001-01-08  100.782933  -1.851726  -4.053723   5.50000   6.01500  \n", "\n", "[5 rows x 29 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# we read our dataframe and assign the time column as the index values of the dataframe\n", "file = \"./DATA/Merged.csv\"\n", "df = pd.read_csv(file, index_col='Date', parse_dates=True)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def CreateLaggedSequence(data, lag):\n", "    # make two lists for PredictorSequences and ResponseVariables\n", "    PredictorSequences, ResponseVariables = list(), list()\n", "    for i in range(len(data)):\n", "        # mark the range of the sequence\n", "        end_i = i + lag\n", "        # check when the data ends\n", "        if end_i+1 > len(data):\n", "            # stop sequence creation\n", "            break\n", "        # get the predictors and responses\n", "        PredictorSequence = data[i:end_i]\n", "        ResponseVariable = data[end_i]\n", "        # append them to the lists\n", "        PredictorSequences.append(PredictorSequence)\n", "        ResponseVariables.append(ResponseVariable)\n", "        # print(end_i)\n", "    return np.array(PredictorSequences), np.array(ResponseVariables)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["((4870, 50), (4870,), 4870, 4870)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the univariate series\n", "X = df['GBP']\n", "np_X = np.array(X)\n", "# Create the lagged values for the series (50 lags)\n", "X, y = CreateLaggedSequence(np_X, 50)\n", "lag = X.shape[1]\n", "# Reshape it for the process\n", "X = X.reshape(X.shape[0], X.shape[1]) \n", "X.shape, y.shape, len(X), len(y)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["((4820, 50), (50, 50), (4820,), (50,))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# split the train and test sets (last 50 observation spared for the test)\n", "x_train, x_test = X[:-50], X[-50:]\n", "y_train, y_test = y[:-50], y[-50:]\n", "x_train.shape, x_test.shape, y_train.shape, y_test.shape"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Define a new activation function\n", "# <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2018, August 19). Swish Activation Function by Google. Retrieved from https://medium.com/@neuralnets/swish-activation-function-by-google-53e1ea86f820\n", "\n", "def swish(x, beta = 1):\n", "    return (x * sigmoid(beta * x))\n", "\n", "from keras.utils.generic_utils import get_custom_objects\n", "from keras.layers import Activation\n", "get_custom_objects().update({'swish': Activation(swish)})"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"tags": ["outputPrepend"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 6.0431e-04 - val_mae: 0.0206\n", "Epoch 890/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.8267e-04 - mae: 0.0216 - val_loss: 5.9374e-04 - val_mae: 0.0203\n", "Epoch 891/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.8021e-04 - mae: 0.0216 - val_loss: 5.8376e-04 - val_mae: 0.0201\n", "Epoch 892/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 8.7775e-04 - mae: 0.0216 - val_loss: 5.7372e-04 - val_mae: 0.0199\n", "Epoch 893/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 8.7531e-04 - mae: 0.0215 - val_loss: 5.6370e-04 - val_mae: 0.0197\n", "Epoch 894/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 8.7287e-04 - mae: 0.0215 - val_loss: 5.5426e-04 - val_mae: 0.0195\n", "Epoch 895/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.7043e-04 - mae: 0.0215 - val_loss: 5.4446e-04 - val_mae: 0.0193\n", "Epoch 896/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.6800e-04 - mae: 0.0214 - val_loss: 5.3522e-04 - val_mae: 0.0191\n", "Epoch 897/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.6558e-04 - mae: 0.0214 - val_loss: 5.2598e-04 - val_mae: 0.0189\n", "Epoch 898/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.6316e-04 - mae: 0.0214 - val_loss: 5.1682e-04 - val_mae: 0.0187\n", "Epoch 899/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 8.6076e-04 - mae: 0.0213 - val_loss: 5.0807e-04 - val_mae: 0.0185\n", "Epoch 900/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.5836e-04 - mae: 0.0213 - val_loss: 4.9907e-04 - val_mae: 0.0183\n", "Epoch 901/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.5596e-04 - mae: 0.0213 - val_loss: 4.9057e-04 - val_mae: 0.0181\n", "Epoch 902/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.5357e-04 - mae: 0.0212 - val_loss: 4.8209e-04 - val_mae: 0.0179\n", "Epoch 903/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 8.5119e-04 - mae: 0.0212 - val_loss: 4.7374e-04 - val_mae: 0.0177\n", "Epoch 904/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.4882e-04 - mae: 0.0212 - val_loss: 4.6558e-04 - val_mae: 0.0175\n", "Epoch 905/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 8.4645e-04 - mae: 0.0211 - val_loss: 4.5758e-04 - val_mae: 0.0174\n", "Epoch 906/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.4409e-04 - mae: 0.0211 - val_loss: 4.4970e-04 - val_mae: 0.0172\n", "Epoch 907/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.4173e-04 - mae: 0.0211 - val_loss: 4.4192e-04 - val_mae: 0.0170\n", "Epoch 908/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.3939e-04 - mae: 0.0210 - val_loss: 4.3443e-04 - val_mae: 0.0168\n", "Epoch 909/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.3704e-04 - mae: 0.0210 - val_loss: 4.2690e-04 - val_mae: 0.0166\n", "Epoch 910/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.3471e-04 - mae: 0.0210 - val_loss: 4.1973e-04 - val_mae: 0.0165\n", "Epoch 911/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.3238e-04 - mae: 0.0209 - val_loss: 4.1246e-04 - val_mae: 0.0163\n", "Epoch 912/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.3006e-04 - mae: 0.0209 - val_loss: 4.0564e-04 - val_mae: 0.0161\n", "Epoch 913/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.2775e-04 - mae: 0.0209 - val_loss: 3.9859e-04 - val_mae: 0.0159\n", "Epoch 914/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.2544e-04 - mae: 0.0208 - val_loss: 3.9208e-04 - val_mae: 0.0158\n", "Epoch 915/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.2314e-04 - mae: 0.0208 - val_loss: 3.8536e-04 - val_mae: 0.0156\n", "Epoch 916/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.2085e-04 - mae: 0.0208 - val_loss: 3.7902e-04 - val_mae: 0.0155\n", "Epoch 917/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 8.1855e-04 - mae: 0.0207 - val_loss: 3.7262e-04 - val_mae: 0.0153\n", "Epoch 918/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.1627e-04 - mae: 0.0207 - val_loss: 3.6664e-04 - val_mae: 0.0151\n", "Epoch 919/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.1400e-04 - mae: 0.0207 - val_loss: 3.6039e-04 - val_mae: 0.0150\n", "Epoch 920/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.1172e-04 - mae: 0.0206 - val_loss: 3.5477e-04 - val_mae: 0.0148\n", "Epoch 921/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.0946e-04 - mae: 0.0206 - val_loss: 3.4874e-04 - val_mae: 0.0147\n", "Epoch 922/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 8.0721e-04 - mae: 0.0206 - val_loss: 3.4344e-04 - val_mae: 0.0146\n", "Epoch 923/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.0496e-04 - mae: 0.0205 - val_loss: 3.3769e-04 - val_mae: 0.0144\n", "Epoch 924/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.0272e-04 - mae: 0.0205 - val_loss: 3.3257e-04 - val_mae: 0.0143\n", "Epoch 925/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.0048e-04 - mae: 0.0205 - val_loss: 3.2723e-04 - val_mae: 0.0141\n", "Epoch 926/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.9826e-04 - mae: 0.0204 - val_loss: 3.2223e-04 - val_mae: 0.0140\n", "Epoch 927/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.9604e-04 - mae: 0.0204 - val_loss: 3.1732e-04 - val_mae: 0.0139\n", "Epoch 928/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.9382e-04 - mae: 0.0204 - val_loss: 3.1242e-04 - val_mae: 0.0137\n", "Epoch 929/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.9162e-04 - mae: 0.0204 - val_loss: 3.0791e-04 - val_mae: 0.0136\n", "Epoch 930/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.8941e-04 - mae: 0.0203 - val_loss: 3.0320e-04 - val_mae: 0.0135\n", "Epoch 931/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.8723e-04 - mae: 0.0203 - val_loss: 2.9900e-04 - val_mae: 0.0134\n", "Epoch 932/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.8503e-04 - mae: 0.0203 - val_loss: 2.9447e-04 - val_mae: 0.0132\n", "Epoch 933/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 7.8285e-04 - mae: 0.0202 - val_loss: 2.9063e-04 - val_mae: 0.0131\n", "Epoch 934/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 7.8068e-04 - mae: 0.0202 - val_loss: 2.8622e-04 - val_mae: 0.0130\n", "Epoch 935/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.7851e-04 - mae: 0.0202 - val_loss: 2.8280e-04 - val_mae: 0.0129\n", "Epoch 936/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 7.7635e-04 - mae: 0.0201 - val_loss: 2.7852e-04 - val_mae: 0.0128\n", "Epoch 937/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.7420e-04 - mae: 0.0201 - val_loss: 2.7547e-04 - val_mae: 0.0127\n", "Epoch 938/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.7205e-04 - mae: 0.0201 - val_loss: 2.7124e-04 - val_mae: 0.0126\n", "Epoch 939/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.6991e-04 - mae: 0.0200 - val_loss: 2.6872e-04 - val_mae: 0.0125\n", "Epoch 940/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.6778e-04 - mae: 0.0200 - val_loss: 2.6440e-04 - val_mae: 0.0124\n", "Epoch 941/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.6565e-04 - mae: 0.0200 - val_loss: 2.6262e-04 - val_mae: 0.0123\n", "Epoch 942/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.6353e-04 - mae: 0.0199 - val_loss: 2.5783e-04 - val_mae: 0.0122\n", "Epoch 943/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 7.6143e-04 - mae: 0.0199 - val_loss: 2.5735e-04 - val_mae: 0.0122\n", "Epoch 944/1024\n", "3856/3856 [==============================] - 0s 23us/step - loss: 7.5933e-04 - mae: 0.0199 - val_loss: 2.5129e-04 - val_mae: 0.0120\n", "Epoch 945/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.5726e-04 - mae: 0.0199 - val_loss: 2.5333e-04 - val_mae: 0.0121\n", "Epoch 946/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.5522e-04 - mae: 0.0198 - val_loss: 2.4426e-04 - val_mae: 0.0118\n", "Epoch 947/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.5327e-04 - mae: 0.0198 - val_loss: 2.5174e-04 - val_mae: 0.0120\n", "Epoch 948/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.5151e-04 - mae: 0.0198 - val_loss: 2.3591e-04 - val_mae: 0.0115\n", "Epoch 949/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 7.5026e-04 - mae: 0.0198 - val_loss: 2.5646e-04 - val_mae: 0.0122\n", "Epoch 950/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.5024e-04 - mae: 0.0197 - val_loss: 2.2697e-04 - val_mae: 0.0112\n", "Epoch 951/1024\n", "3856/3856 [==============================] - 0s 23us/step - loss: 7.5380e-04 - mae: 0.0200 - val_loss: 2.8394e-04 - val_mae: 0.0130\n", "Epoch 952/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.6560e-04 - mae: 0.0199 - val_loss: 2.4138e-04 - val_mae: 0.0117\n", "Epoch 953/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.0463e-04 - mae: 0.0212 - val_loss: 4.1210e-04 - val_mae: 0.0165\n", "Epoch 954/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 8.8501e-04 - mae: 0.0221 - val_loss: 4.5124e-04 - val_mae: 0.0173\n", "Epoch 955/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 0.0011 - mae: 0.0276 - val_loss: 7.4669e-04 - val_mae: 0.0239\n", "Epoch 956/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 0.0013 - mae: 0.0285 - val_loss: 8.4977e-04 - val_mae: 0.0256\n", "Epoch 957/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 0.0017 - mae: 0.0357 - val_loss: 5.9018e-04 - val_mae: 0.0207\n", "Epoch 958/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 0.0011 - mae: 0.0255 - val_loss: 2.3401e-04 - val_mae: 0.0115\n", "Epoch 959/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.7967e-04 - mae: 0.0207 - val_loss: 2.2620e-04 - val_mae: 0.0112\n", "Epoch 960/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 7.6041e-04 - mae: 0.0203 - val_loss: 4.8329e-04 - val_mae: 0.0183\n", "Epoch 961/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 9.5400e-04 - mae: 0.0234 - val_loss: 4.8002e-04 - val_mae: 0.0180\n", "Epoch 962/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 0.0012 - mae: 0.0283 - val_loss: 4.0406e-04 - val_mae: 0.0164\n", "Epoch 963/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.7421e-04 - mae: 0.0220 - val_loss: 2.2482e-04 - val_mae: 0.0112\n", "Epoch 964/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.2651e-04 - mae: 0.0194 - val_loss: 2.5543e-04 - val_mae: 0.0121\n", "Epoch 965/1024\n", "3856/3856 [==============================] - 0s 24us/step - loss: 8.1475e-04 - mae: 0.0215 - val_loss: 4.1739e-04 - val_mae: 0.0167\n", "Epoch 966/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 8.9976e-04 - mae: 0.0225 - val_loss: 3.0286e-04 - val_mae: 0.0134\n", "Epoch 967/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.8155e-04 - mae: 0.0229 - val_loss: 2.4664e-04 - val_mae: 0.0120\n", "Epoch 968/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 7.3561e-04 - mae: 0.0195 - val_loss: 2.7157e-04 - val_mae: 0.0127\n", "Epoch 969/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 7.6021e-04 - mae: 0.0199 - val_loss: 3.1063e-04 - val_mae: 0.0137\n", "Epoch 970/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.7121e-04 - mae: 0.0227 - val_loss: 3.0516e-04 - val_mae: 0.0137\n", "Epoch 971/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.0323e-04 - mae: 0.0207 - val_loss: 2.2108e-04 - val_mae: 0.0110\n", "Epoch 972/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.2356e-04 - mae: 0.0194 - val_loss: 2.3161e-04 - val_mae: 0.0113\n", "Epoch 973/1024\n", "3856/3856 [==============================] - 0s 20us/step - loss: 7.3828e-04 - mae: 0.0198 - val_loss: 2.8256e-04 - val_mae: 0.0130\n", "Epoch 974/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.8771e-04 - mae: 0.0205 - val_loss: 2.7015e-04 - val_mae: 0.0125\n", "Epoch 975/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.8263e-04 - mae: 0.0208 - val_loss: 2.2519e-04 - val_mae: 0.0113\n", "Epoch 976/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 7.1892e-04 - mae: 0.0192 - val_loss: 2.2905e-04 - val_mae: 0.0114\n", "Epoch 977/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 7.2383e-04 - mae: 0.0193 - val_loss: 2.6994e-04 - val_mae: 0.0125\n", "Epoch 978/1024\n", "3856/3856 [==============================] - 0s 20us/step - loss: 7.6837e-04 - mae: 0.0205 - val_loss: 2.4653e-04 - val_mae: 0.0120\n", "Epoch 979/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.4886e-04 - mae: 0.0198 - val_loss: 2.2691e-04 - val_mae: 0.0112\n", "Epoch 980/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.1121e-04 - mae: 0.0192 - val_loss: 2.2617e-04 - val_mae: 0.0112\n", "Epoch 981/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.0805e-04 - mae: 0.0192 - val_loss: 2.3487e-04 - val_mae: 0.0116\n", "Epoch 982/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.3191e-04 - mae: 0.0195 - val_loss: 2.5982e-04 - val_mae: 0.0122\n", "Epoch 983/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.3782e-04 - mae: 0.0199 - val_loss: 2.2206e-04 - val_mae: 0.0112\n", "Epoch 984/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.0863e-04 - mae: 0.0191 - val_loss: 2.1903e-04 - val_mae: 0.0110\n", "Epoch 985/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 6.9593e-04 - mae: 0.0189 - val_loss: 2.4173e-04 - val_mae: 0.0116\n", "Epoch 986/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.0928e-04 - mae: 0.0193 - val_loss: 2.2698e-04 - val_mae: 0.0113\n", "Epoch 987/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 7.1728e-04 - mae: 0.0193 - val_loss: 2.4771e-04 - val_mae: 0.0118\n", "Epoch 988/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.0824e-04 - mae: 0.0193 - val_loss: 2.1890e-04 - val_mae: 0.0110\n", "Epoch 989/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 6.9087e-04 - mae: 0.0188 - val_loss: 2.1947e-04 - val_mae: 0.0110\n", "Epoch 990/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 6.8837e-04 - mae: 0.0188 - val_loss: 2.4754e-04 - val_mae: 0.0118\n", "Epoch 991/1024\n", "3856/3856 [==============================] - 0s 20us/step - loss: 6.9761e-04 - mae: 0.0192 - val_loss: 2.2048e-04 - val_mae: 0.0111\n", "Epoch 992/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.0028e-04 - mae: 0.0190 - val_loss: 2.5164e-04 - val_mae: 0.0120\n", "Epoch 993/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 6.9350e-04 - mae: 0.0191 - val_loss: 2.2141e-04 - val_mae: 0.0111\n", "Epoch 994/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 6.8213e-04 - mae: 0.0187 - val_loss: 2.2654e-04 - val_mae: 0.0112\n", "Epoch 995/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 6.7797e-04 - mae: 0.0187 - val_loss: 2.4819e-04 - val_mae: 0.0119\n", "Epoch 996/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 6.8135e-04 - mae: 0.0188 - val_loss: 2.2051e-04 - val_mae: 0.0111\n", "Epoch 997/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 6.8492e-04 - mae: 0.0188 - val_loss: 2.6605e-04 - val_mae: 0.0124\n", "Epoch 998/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 6.8441e-04 - mae: 0.0190 - val_loss: 2.2349e-04 - val_mae: 0.0112\n", "Epoch 999/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 6.7790e-04 - mae: 0.0187 - val_loss: 2.5075e-04 - val_mae: 0.0120\n", "Epoch 1000/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 6.7136e-04 - mae: 0.0187 - val_loss: 2.4077e-04 - val_mae: 0.0117\n", "Epoch 1001/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 6.6771e-04 - mae: 0.0185 - val_loss: 2.3419e-04 - val_mae: 0.0115\n", "Epoch 1002/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 6.6766e-04 - mae: 0.0185 - val_loss: 2.6786e-04 - val_mae: 0.0125\n", "Epoch 1003/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 6.6937e-04 - mae: 0.0187 - val_loss: 2.2945e-04 - val_mae: 0.0114\n", "Epoch 1004/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 6.7008e-04 - mae: 0.0186 - val_loss: 2.8209e-04 - val_mae: 0.0129\n", "Epoch 1005/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 6.6935e-04 - mae: 0.0187 - val_loss: 2.3309e-04 - val_mae: 0.0115\n", "Epoch 1006/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 6.6614e-04 - mae: 0.0185 - val_loss: 2.7823e-04 - val_mae: 0.0128\n", "Epoch 1007/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 6.6245e-04 - mae: 0.0186 - val_loss: 2.4411e-04 - val_mae: 0.0118\n", "Epoch 1008/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 6.5859e-04 - mae: 0.0184 - val_loss: 2.6759e-04 - val_mae: 0.0125\n", "Epoch 1009/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 6.5556e-04 - mae: 0.0184 - val_loss: 2.6076e-04 - val_mae: 0.0123\n", "Epoch 1010/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 6.5344e-04 - mae: 0.0183 - val_loss: 2.5888e-04 - val_mae: 0.0123\n", "Epoch 1011/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 6.5214e-04 - mae: 0.0183 - val_loss: 2.7926e-04 - val_mae: 0.0129\n", "Epoch 1012/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 6.5140e-04 - mae: 0.0184 - val_loss: 2.5387e-04 - val_mae: 0.0121\n", "Epoch 1013/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 6.5099e-04 - mae: 0.0183 - val_loss: 2.9766e-04 - val_mae: 0.0134\n", "Epoch 1014/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 6.5092e-04 - mae: 0.0184 - val_loss: 2.5099e-04 - val_mae: 0.0120\n", "Epoch 1015/1024\n", "3856/3856 [==============================] - 0s 16us/step - loss: 6.5092e-04 - mae: 0.0183 - val_loss: 3.1762e-04 - val_mae: 0.0139\n", "Epoch 1016/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 6.5167e-04 - mae: 0.0185 - val_loss: 2.4775e-04 - val_mae: 0.0119\n", "Epoch 1017/1024\n", "3856/3856 [==============================] - 0s 19us/step - loss: 6.5263e-04 - mae: 0.0183 - val_loss: 3.4485e-04 - val_mae: 0.0147\n", "Epoch 1018/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 6.5577e-04 - mae: 0.0186 - val_loss: 2.4160e-04 - val_mae: 0.0118\n", "Epoch 1019/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 6.5971e-04 - mae: 0.0185 - val_loss: 3.9139e-04 - val_mae: 0.0159\n", "Epoch 1020/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 6.7015e-04 - mae: 0.0190 - val_loss: 2.3170e-04 - val_mae: 0.0115\n", "Epoch 1021/1024\n", "3856/3856 [==============================] - 0s 20us/step - loss: 6.8260e-04 - mae: 0.0189 - val_loss: 4.8420e-04 - val_mae: 0.0182\n", "Epoch 1022/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 7.1587e-04 - mae: 0.0201 - val_loss: 2.2603e-04 - val_mae: 0.0113\n", "Epoch 1023/1024\n", "3856/3856 [==============================] - 0s 17us/step - loss: 7.4982e-04 - mae: 0.0202 - val_loss: 6.8361e-04 - val_mae: 0.0225\n", "Epoch 1024/1024\n", "3856/3856 [==============================] - 0s 18us/step - loss: 8.5266e-04 - mae: 0.0232 - val_loss: 2.5401e-04 - val_mae: 0.0121\n", "50/50 [==============================] - 0s 62us/step\n", "test_acc: 0.01069902628660202\n", "Total time: 74.502610206604 seconds\n"]}], "source": ["# create a start point for timer\n", "start = time.time()\n", "\n", "# design the model\n", "backend.clear_session()\n", "model = models.Sequential()\n", "\n", "model.add(layers.Dense(3*2**8, activation = 'swish', kernel_initializer='he_uniform', input_shape = (x_train.shape[1],)))\n", "model.add(layers.Dense(3*2**6, activation='swish', kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**7, activation='swish', kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**5, activation='swish', kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**6, activation='swish', kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**4, activation='swish', kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**5, activation='swish', kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**3, activation='swish', kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**4, activation='swish', kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**2, activation='swish', kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**3, activation='swish', kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**1, activation='swish', kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**2, activation='swish', kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**0, activation=tf.keras.backend.sin, kernel_initializer='he_uniform'))\n", "model.add(layers.Dense(3*2**1, activation='swish', kernel_initializer='he_uniform'))\n", "\n", "model.add(layers.Dense(1, activation='swish'))\n", "# compile the model\n", "model.compile(optimizer = optimizers.<PERSON>(lr=0.00011), loss = 'mse', metrics = ['mae'])\n", "#get the summary of the model\n", "model.summary()\n", "\n", "################################################################################\n", "\n", "# fit the model\n", "history = model.fit(x_train, y_train, \n", "            epochs = 2**10, \n", "            batch_size = 3856, \n", "            validation_split = 0.20, # sparing validation data from the training data\n", "            verbose = 1, \n", "            callbacks=[EarlyStopping(monitor='val_mae', patience=2**9,)])\n", "\n", "#evaluate the model\n", "test_loss, test_acc = model.evaluate(x_test, y_test)\n", "print('test_acc:', test_acc)\n", "\n", "# end the timer and print the total time passed\n", "end = time.time()\n", "print(\"Total time:\", end-start, \"seconds\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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", "image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Created with matplotlib (https://matplotlib.org/) -->\n", "<svg height=\"277.314375pt\" version=\"1.1\" viewBox=\"0 0 398.50625 277.314375\" width=\"398.50625pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", " <defs>\n", "  <style type=\"text/css\">\n", "*{stroke-linecap:butt;stroke-linejoin:round;}\n", "  </style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 277.314375 \n", "L 398.50625 277.314375 \n", "L 398.50625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill:none;\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 56.50625 239.758125 \n", "L 391.30625 239.758125 \n", "L 391.30625 22.318125 \n", "L 56.50625 22.318125 \n", "z\n", "\" style=\"fill:#ffffff;\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 0 3.5 \n", "\" id=\"m6cad134f4d\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"71.426911\" xlink:href=\"#m6cad134f4d\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <defs>\n", "       <path d=\"M 31.78125 66.40625 \n", "Q 24.171875 66.40625 20.328125 58.90625 \n", "Q 16.5 51.421875 16.5 36.375 \n", "Q 16.5 21.390625 20.328125 13.890625 \n", "Q 24.171875 6.390625 31.78125 6.390625 \n", "Q 39.453125 6.390625 43.28125 13.890625 \n", "Q 47.125 21.390625 47.125 36.375 \n", "Q 47.125 51.421875 43.28125 58.90625 \n", "Q 39.453125 66.40625 31.78125 66.40625 \n", "z\n", "M 31.78125 74.21875 \n", "Q 44.046875 74.21875 50.515625 64.515625 \n", "Q 56.984375 54.828125 56.984375 36.375 \n", "Q 56.984375 17.96875 50.515625 8.265625 \n", "Q 44.046875 -1.421875 31.78125 -1.421875 \n", "Q 19.53125 -1.421875 13.0625 8.265625 \n", "Q 6.59375 17.96875 6.59375 36.375 \n", "Q 6.59375 54.828125 13.0625 64.515625 \n", "Q 19.53125 74.21875 31.78125 74.21875 \n", "z\n", "\" id=\"DejaVuSans-48\"/>\n", "      </defs>\n", "      <g transform=\"translate(68.245661 254.356562)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"130.931043\" xlink:href=\"#m6cad134f4d\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <defs>\n", "       <path d=\"M 19.1875 8.296875 \n", "L 53.609375 8.296875 \n", "L 53.609375 0 \n", "L 7.328125 0 \n", "L 7.328125 8.296875 \n", "Q 12.9375 14.109375 22.625 23.890625 \n", "Q 32.328125 33.6875 34.8125 36.53125 \n", "Q 39.546875 41.84375 41.421875 45.53125 \n", "Q 43.3125 49.21875 43.3125 52.78125 \n", "Q 43.3125 58.59375 39.234375 62.25 \n", "Q 35.15625 65.921875 28.609375 65.921875 \n", "Q 23.96875 65.921875 18.8125 64.3125 \n", "Q 13.671875 62.703125 7.8125 59.421875 \n", "L 7.8125 69.390625 \n", "Q 13.765625 71.78125 18.9375 73 \n", "Q 24.125 74.21875 28.421875 74.21875 \n", "Q 39.75 74.21875 46.484375 68.546875 \n", "Q 53.21875 62.890625 53.21875 53.421875 \n", "Q 53.21875 48.921875 51.53125 44.890625 \n", "Q 49.859375 40.875 45.40625 35.40625 \n", "Q 44.1875 33.984375 37.640625 27.21875 \n", "Q 31.109375 20.453125 19.1875 8.296875 \n", "z\n", "\" id=\"DejaVuSans-50\"/>\n", "      </defs>\n", "      <g transform=\"translate(121.387293 254.356562)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"190.435176\" xlink:href=\"#m6cad134f4d\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <defs>\n", "       <path d=\"M 37.796875 64.3125 \n", "L 12.890625 25.390625 \n", "L 37.796875 25.390625 \n", "z\n", "M 35.203125 72.90625 \n", "L 47.609375 72.90625 \n", "L 47.609375 25.390625 \n", "L 58.015625 25.390625 \n", "L 58.015625 17.1875 \n", "L 47.609375 17.1875 \n", "L 47.609375 0 \n", "L 37.796875 0 \n", "L 37.796875 17.1875 \n", "L 4.890625 17.1875 \n", "L 4.890625 26.703125 \n", "z\n", "\" id=\"DejaVuSans-52\"/>\n", "      </defs>\n", "      <g transform=\"translate(180.891426 254.356562)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-52\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"249.939308\" xlink:href=\"#m6cad134f4d\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <defs>\n", "       <path d=\"M 33.015625 40.375 \n", "Q 26.375 40.375 22.484375 35.828125 \n", "Q 18.609375 31.296875 18.609375 23.390625 \n", "Q 18.609375 15.53125 22.484375 10.953125 \n", "Q 26.375 6.390625 33.015625 6.390625 \n", "Q 39.65625 6.390625 43.53125 10.953125 \n", "Q 47.40625 15.53125 47.40625 23.390625 \n", "Q 47.40625 31.296875 43.53125 35.828125 \n", "Q 39.65625 40.375 33.015625 40.375 \n", "z\n", "M 52.59375 71.296875 \n", "L 52.59375 62.3125 \n", "Q 48.875 64.0625 45.09375 64.984375 \n", "Q 41.3125 65.921875 37.59375 65.921875 \n", "Q 27.828125 65.921875 22.671875 59.328125 \n", "Q 17.53125 52.734375 16.796875 39.40625 \n", "Q 19.671875 43.65625 24.015625 45.921875 \n", "Q 28.375 48.1875 33.59375 48.1875 \n", "Q 44.578125 48.1875 50.953125 41.515625 \n", "Q 57.328125 34.859375 57.328125 23.390625 \n", "Q 57.328125 12.15625 50.6875 5.359375 \n", "Q 44.046875 -1.421875 33.015625 -1.421875 \n", "Q 20.359375 -1.421875 13.671875 8.265625 \n", "Q 6.984375 17.96875 6.984375 36.375 \n", "Q 6.984375 53.65625 15.1875 63.9375 \n", "Q 23.390625 74.21875 37.203125 74.21875 \n", "Q 40.921875 74.21875 44.703125 73.484375 \n", "Q 48.484375 72.75 52.59375 71.296875 \n", "z\n", "\" id=\"DejaVuSans-54\"/>\n", "      </defs>\n", "      <g transform=\"translate(240.395558 254.356562)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"309.44344\" xlink:href=\"#m6cad134f4d\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 800 -->\n", "      <defs>\n", "       <path d=\"M 31.78125 34.625 \n", "Q 24.75 34.625 20.71875 30.859375 \n", "Q 16.703125 27.09375 16.703125 20.515625 \n", "Q 16.703125 13.921875 20.71875 10.15625 \n", "Q 24.75 6.390625 31.78125 6.390625 \n", "Q 38.8125 6.390625 42.859375 10.171875 \n", "Q 46.921875 13.96875 46.921875 20.515625 \n", "Q 46.921875 27.09375 42.890625 30.859375 \n", "Q 38.875 34.625 31.78125 34.625 \n", "z\n", "M 21.921875 38.8125 \n", "Q 15.578125 40.375 12.03125 44.71875 \n", "Q 8.5 49.078125 8.5 55.328125 \n", "Q 8.5 64.0625 14.71875 69.140625 \n", "Q 20.953125 74.21875 31.78125 74.21875 \n", "Q 42.671875 74.21875 48.875 69.140625 \n", "Q 55.078125 64.0625 55.078125 55.328125 \n", "Q 55.078125 49.078125 51.53125 44.71875 \n", "Q 48 40.375 41.703125 38.8125 \n", "Q 48.828125 37.15625 52.796875 32.3125 \n", "Q 56.78125 27.484375 56.78125 20.515625 \n", "Q 56.78125 9.90625 50.3125 4.234375 \n", "Q 43.84375 -1.421875 31.78125 -1.421875 \n", "Q 19.734375 -1.421875 13.25 4.234375 \n", "Q 6.78125 9.90625 6.78125 20.515625 \n", "Q 6.78125 27.484375 10.78125 32.3125 \n", "Q 14.796875 37.15625 21.921875 38.8125 \n", "z\n", "M 18.3125 54.390625 \n", "Q 18.3125 48.734375 21.84375 45.5625 \n", "Q 25.390625 42.390625 31.78125 42.390625 \n", "Q 38.140625 42.390625 41.71875 45.5625 \n", "Q 45.3125 48.734375 45.3125 54.390625 \n", "Q 45.3125 60.0625 41.71875 63.234375 \n", "Q 38.140625 66.40625 31.78125 66.40625 \n", "Q 25.390625 66.40625 21.84375 63.234375 \n", "Q 18.3125 60.0625 18.3125 54.390625 \n", "z\n", "\" id=\"DejaVuSans-56\"/>\n", "      </defs>\n", "      <g transform=\"translate(299.89969 254.356562)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"368.947572\" xlink:href=\"#m6cad134f4d\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1000 -->\n", "      <defs>\n", "       <path d=\"M 12.40625 8.296875 \n", "L 28.515625 8.296875 \n", "L 28.515625 63.921875 \n", "L 10.984375 60.40625 \n", "L 10.984375 69.390625 \n", "L 28.421875 72.90625 \n", "L 38.28125 72.90625 \n", "L 38.28125 8.296875 \n", "L 54.390625 8.296875 \n", "L 54.390625 0 \n", "L 12.40625 0 \n", "z\n", "\" id=\"DejaVuSans-49\"/>\n", "      </defs>\n", "      <g transform=\"translate(356.222572 254.356562)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- Epochs -->\n", "     <defs>\n", "      <path d=\"M 9.8125 72.90625 \n", "L 55.90625 72.90625 \n", "L 55.90625 64.59375 \n", "L 19.671875 64.59375 \n", "L 19.671875 43.015625 \n", "L 54.390625 43.015625 \n", "L 54.390625 34.71875 \n", "L 19.671875 34.71875 \n", "L 19.671875 8.296875 \n", "L 56.78125 8.296875 \n", "L 56.78125 0 \n", "L 9.8125 0 \n", "z\n", "\" id=\"DejaVuSans-69\"/>\n", "      <path d=\"M 18.109375 8.203125 \n", "L 18.109375 -20.796875 \n", "L 9.078125 -20.796875 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.390625 \n", "Q 20.953125 51.265625 25.265625 53.625 \n", "Q 29.59375 56 35.59375 56 \n", "Q 45.5625 56 51.78125 48.09375 \n", "Q 58.015625 40.1875 58.015625 27.296875 \n", "Q 58.015625 14.40625 51.78125 6.484375 \n", "Q 45.5625 -1.421875 35.59375 -1.421875 \n", "Q 29.59375 -1.421875 25.265625 0.953125 \n", "Q 20.953125 3.328125 18.109375 8.203125 \n", "z\n", "M 48.6875 27.296875 \n", "Q 48.6875 37.203125 44.609375 42.84375 \n", "Q 40.53125 48.484375 33.40625 48.484375 \n", "Q 26.265625 48.484375 22.1875 42.84375 \n", "Q 18.109375 37.203125 18.109375 27.296875 \n", "Q 18.109375 17.390625 22.1875 11.75 \n", "Q 26.265625 6.109375 33.40625 6.109375 \n", "Q 40.53125 6.109375 44.609375 11.75 \n", "Q 48.6875 17.390625 48.6875 27.296875 \n", "z\n", "\" id=\"DejaVuSans-112\"/>\n", "      <path d=\"M 30.609375 48.390625 \n", "Q 23.390625 48.390625 19.1875 42.75 \n", "Q 14.984375 37.109375 14.984375 27.296875 \n", "Q 14.984375 17.484375 19.15625 11.84375 \n", "Q 23.34375 6.203125 30.609375 6.203125 \n", "Q 37.796875 6.203125 41.984375 11.859375 \n", "Q 46.1875 17.53125 46.1875 27.296875 \n", "Q 46.1875 37.015625 41.984375 42.703125 \n", "Q 37.796875 48.390625 30.609375 48.390625 \n", "z\n", "M 30.609375 56 \n", "Q 42.328125 56 49.015625 48.375 \n", "Q 55.71875 40.765625 55.71875 27.296875 \n", "Q 55.71875 13.875 49.015625 6.21875 \n", "Q 42.328125 -1.421875 30.609375 -1.421875 \n", "Q 18.84375 -1.421875 12.171875 6.21875 \n", "Q 5.515625 13.875 5.515625 27.296875 \n", "Q 5.515625 40.765625 12.171875 48.375 \n", "Q 18.84375 56 30.609375 56 \n", "z\n", "\" id=\"DejaVuSans-111\"/>\n", "      <path d=\"M 48.78125 52.59375 \n", "L 48.78125 44.1875 \n", "Q 44.96875 46.296875 41.140625 47.34375 \n", "Q 37.3125 48.390625 33.40625 48.390625 \n", "Q 24.65625 48.390625 19.8125 42.84375 \n", "Q 14.984375 37.3125 14.984375 27.296875 \n", "Q 14.984375 17.28125 19.8125 11.734375 \n", "Q 24.65625 6.203125 33.40625 6.203125 \n", "Q 37.3125 6.203125 41.140625 7.25 \n", "Q 44.96875 8.296875 48.78125 10.40625 \n", "L 48.78125 2.09375 \n", "Q 45.015625 0.34375 40.984375 -0.53125 \n", "Q 36.96875 -1.421875 32.421875 -1.421875 \n", "Q 20.0625 -1.421875 12.78125 6.34375 \n", "Q 5.515625 14.109375 5.515625 27.296875 \n", "Q 5.515625 40.671875 12.859375 48.328125 \n", "Q 20.21875 56 33.015625 56 \n", "Q 37.15625 56 41.109375 55.140625 \n", "Q 45.0625 54.296875 48.78125 52.59375 \n", "z\n", "\" id=\"DejaVuSans-99\"/>\n", "      <path d=\"M 54.890625 33.015625 \n", "L 54.890625 0 \n", "L 45.90625 0 \n", "L 45.90625 32.71875 \n", "Q 45.90625 40.484375 42.875 44.328125 \n", "Q 39.84375 48.1875 33.796875 48.1875 \n", "Q 26.515625 48.1875 22.3125 43.546875 \n", "Q 18.109375 38.921875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 75.984375 \n", "L 18.109375 75.984375 \n", "L 18.109375 46.1875 \n", "Q 21.34375 51.125 25.703125 53.5625 \n", "Q 30.078125 56 35.796875 56 \n", "Q 45.21875 56 50.046875 50.171875 \n", "Q 54.890625 44.34375 54.890625 33.015625 \n", "z\n", "\" id=\"DejaVuSans-104\"/>\n", "      <path d=\"M 44.28125 53.078125 \n", "L 44.28125 44.578125 \n", "Q 40.484375 46.53125 36.375 47.5 \n", "Q 32.28125 48.484375 27.875 48.484375 \n", "Q 21.1875 48.484375 17.84375 46.4375 \n", "Q 14.5 44.390625 14.5 40.28125 \n", "Q 14.5 37.15625 16.890625 35.375 \n", "Q 19.28125 33.59375 26.515625 31.984375 \n", "L 29.59375 31.296875 \n", "Q 39.15625 29.25 43.1875 25.515625 \n", "Q 47.21875 21.78125 47.21875 15.09375 \n", "Q 47.21875 7.46875 41.1875 3.015625 \n", "Q 35.15625 -1.421875 24.609375 -1.421875 \n", "Q 20.21875 -1.421875 15.453125 -0.5625 \n", "Q 10.6875 0.296875 5.421875 2 \n", "L 5.421875 11.28125 \n", "Q 10.40625 8.6875 15.234375 7.390625 \n", "Q 20.0625 6.109375 24.8125 6.109375 \n", "Q 31.15625 6.109375 34.5625 8.28125 \n", "Q 37.984375 10.453125 37.984375 14.40625 \n", "Q 37.984375 18.0625 35.515625 20.015625 \n", "Q 33.0625 21.96875 24.703125 23.78125 \n", "L 21.578125 24.515625 \n", "Q 13.234375 26.265625 9.515625 29.90625 \n", "Q 5.8125 33.546875 5.8125 39.890625 \n", "Q 5.8125 47.609375 11.28125 51.796875 \n", "Q 16.75 56 26.8125 56 \n", "Q 31.78125 56 36.171875 55.265625 \n", "Q 40.578125 54.546875 44.28125 53.078125 \n", "z\n", "\" id=\"DejaVuSans-115\"/>\n", "     </defs>\n", "     <g transform=\"translate(205.990625 268.034687)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-69\"/>\n", "      <use x=\"63.183594\" xlink:href=\"#DejaVuSans-112\"/>\n", "      <use x=\"126.660156\" xlink:href=\"#DejaVuSans-111\"/>\n", "      <use x=\"187.841797\" xlink:href=\"#DejaVuSans-99\"/>\n", "      <use x=\"242.822266\" xlink:href=\"#DejaVuSans-104\"/>\n", "      <use x=\"306.201172\" xlink:href=\"#DejaVuSans-115\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L -3.5 0 \n", "\" id=\"m6d3c125393\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"56.50625\" xlink:href=\"#m6d3c125393\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.000 -->\n", "      <defs>\n", "       <path d=\"M 10.6875 12.40625 \n", "L 21 12.40625 \n", "L 21 0 \n", "L 10.6875 0 \n", "z\n", "\" id=\"DejaVuSans-46\"/>\n", "      </defs>\n", "      <g transform=\"translate(20.878125 243.557344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"56.50625\" xlink:href=\"#m6d3c125393\" y=\"203.518125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.005 -->\n", "      <defs>\n", "       <path d=\"M 10.796875 72.90625 \n", "L 49.515625 72.90625 \n", "L 49.515625 64.59375 \n", "L 19.828125 64.59375 \n", "L 19.828125 46.734375 \n", "Q 21.96875 47.46875 24.109375 47.828125 \n", "Q 26.265625 48.1875 28.421875 48.1875 \n", "Q 40.625 48.1875 47.75 41.5 \n", "Q 54.890625 34.8125 54.890625 23.390625 \n", "Q 54.890625 11.625 47.5625 5.09375 \n", "Q 40.234375 -1.421875 26.90625 -1.421875 \n", "Q 22.3125 -1.421875 17.546875 -0.640625 \n", "Q 12.796875 0.140625 7.71875 1.703125 \n", "L 7.71875 11.625 \n", "Q 12.109375 9.234375 16.796875 8.0625 \n", "Q 21.484375 6.890625 26.703125 6.890625 \n", "Q 35.15625 6.890625 40.078125 11.328125 \n", "Q 45.015625 15.765625 45.015625 23.390625 \n", "Q 45.015625 31 40.078125 35.4375 \n", "Q 35.15625 39.890625 26.703125 39.890625 \n", "Q 22.75 39.890625 18.8125 39.015625 \n", "Q 14.890625 38.140625 10.796875 36.28125 \n", "z\n", "\" id=\"DejaVuSans-53\"/>\n", "      </defs>\n", "      <g transform=\"translate(20.878125 207.317344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-53\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"56.50625\" xlink:href=\"#m6d3c125393\" y=\"167.278125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.010 -->\n", "      <g transform=\"translate(20.878125 171.077344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"56.50625\" xlink:href=\"#m6d3c125393\" y=\"131.038125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.015 -->\n", "      <g transform=\"translate(20.878125 134.837344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-53\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"56.50625\" xlink:href=\"#m6d3c125393\" y=\"94.798125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.020 -->\n", "      <g transform=\"translate(20.878125 98.597344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"56.50625\" xlink:href=\"#m6d3c125393\" y=\"58.558125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.025 -->\n", "      <g transform=\"translate(20.878125 62.357344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-53\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"56.50625\" xlink:href=\"#m6d3c125393\" y=\"22.318125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0.030 -->\n", "      <defs>\n", "       <path d=\"M 40.578125 39.3125 \n", "Q 47.65625 37.796875 51.625 33 \n", "Q 55.609375 28.21875 55.609375 21.1875 \n", "Q 55.609375 10.40625 48.1875 4.484375 \n", "Q 40.765625 -1.421875 27.09375 -1.421875 \n", "Q 22.515625 -1.421875 17.65625 -0.515625 \n", "Q 12.796875 0.390625 7.625 2.203125 \n", "L 7.625 11.71875 \n", "Q 11.71875 9.328125 16.59375 8.109375 \n", "Q 21.484375 6.890625 26.8125 6.890625 \n", "Q 36.078125 6.890625 40.9375 10.546875 \n", "Q 45.796875 14.203125 45.796875 21.1875 \n", "Q 45.796875 27.640625 41.28125 31.265625 \n", "Q 36.765625 34.90625 28.71875 34.90625 \n", "L 20.21875 34.90625 \n", "L 20.21875 43.015625 \n", "L 29.109375 43.015625 \n", "Q 36.375 43.015625 40.234375 45.921875 \n", "Q 44.09375 48.828125 44.09375 54.296875 \n", "Q 44.09375 59.90625 40.109375 62.90625 \n", "Q 36.140625 65.921875 28.71875 65.921875 \n", "Q 24.65625 65.921875 20.015625 65.03125 \n", "Q 15.375 64.15625 9.8125 62.3125 \n", "L 9.8125 71.09375 \n", "Q 15.4375 72.65625 20.34375 73.4375 \n", "Q 25.25 74.21875 29.59375 74.21875 \n", "Q 40.828125 74.21875 47.359375 69.109375 \n", "Q 53.90625 64.015625 53.90625 55.328125 \n", "Q 53.90625 49.265625 50.4375 45.09375 \n", "Q 46.96875 40.921875 40.578125 39.3125 \n", "z\n", "\" id=\"DejaVuSans-51\"/>\n", "      </defs>\n", "      <g transform=\"translate(20.878125 26.117344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-51\"/>\n", "       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- Loss -->\n", "     <defs>\n", "      <path d=\"M 9.8125 72.90625 \n", "L 19.671875 72.90625 \n", "L 19.671875 8.296875 \n", "L 55.171875 8.296875 \n", "L 55.171875 0 \n", "L 9.8125 0 \n", "z\n", "\" id=\"DejaVuSans-76\"/>\n", "     </defs>\n", "     <g transform=\"translate(14.798438 142.005312)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use x=\"53.962891\" xlink:href=\"#DejaVuSans-111\"/>\n", "      <use x=\"115.144531\" xlink:href=\"#DejaVuSans-115\"/>\n", "      <use x=\"167.244141\" xlink:href=\"#DejaVuSans-115\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path clip-path=\"url(#pb5d3382f96)\" d=\"M 88.650963 -1 \n", "L 88.98063 36.794881 \n", "L 89.278151 59.292344 \n", "L 89.575671 72.672966 \n", "L 89.873192 78.54303 \n", "L 90.170713 79.121282 \n", "L 90.468233 75.866822 \n", "L 91.063275 62.440893 \n", "L 91.955837 40.466923 \n", "L 92.550878 31.855929 \n", "L 92.848399 30.535869 \n", "L 93.145919 31.343385 \n", "L 93.44344 34.330603 \n", "L 93.740961 39.508687 \n", "L 94.336002 55.688493 \n", "L 95.823605 104.617021 \n", "L 96.121126 110.063211 \n", "L 96.418647 113.0497 \n", "L 96.716167 113.813326 \n", "L 97.013688 112.933866 \n", "L 97.90625 108.396364 \n", "L 98.203771 108.396621 \n", "L 98.501291 109.627308 \n", "L 99.096333 115.163371 \n", "L 100.286415 128.876125 \n", "L 100.881457 133.273816 \n", "L 101.476498 136.010506 \n", "L 102.964101 141.649226 \n", "L 103.856663 146.49308 \n", "L 105.641787 156.497334 \n", "L 106.83187 161.467478 \n", "L 109.509556 170.992533 \n", "L 110.997159 175.508333 \n", "L 112.484762 179.291652 \n", "L 114.567407 183.997829 \n", "L 116.650052 188.140514 \n", "L 118.732696 191.771072 \n", "L 120.815341 194.988486 \n", "L 122.897986 197.822714 \n", "L 125.278151 200.670452 \n", "L 127.658316 203.173986 \n", "L 130.336002 205.646226 \n", "L 133.013688 207.813149 \n", "L 135.988895 209.922457 \n", "L 138.36906 211.397567 \n", "L 138.964101 211.536328 \n", "L 139.261622 211.193291 \n", "L 139.559143 209.297612 \n", "L 139.856663 205.375113 \n", "L 140.154184 189.270454 \n", "L 140.451705 205.113089 \n", "L 140.749225 210.890908 \n", "L 141.046746 212.743943 \n", "L 141.344267 209.525695 \n", "L 141.641787 203.4614 \n", "L 141.939308 210.622185 \n", "L 142.236829 213.369423 \n", "L 142.534349 211.34534 \n", "L 142.83187 210.37836 \n", "L 143.12939 210.777397 \n", "L 143.426911 213.716065 \n", "L 143.724432 212.9347 \n", "L 144.021952 210.458533 \n", "L 144.616994 214.392563 \n", "L 145.212035 213.100686 \n", "L 145.509556 213.406049 \n", "L 145.807076 214.820341 \n", "L 146.104597 214.758462 \n", "L 146.402118 213.828953 \n", "L 146.699638 214.309964 \n", "L 146.997159 215.126474 \n", "L 147.29468 215.601025 \n", "L 147.5922 215.302532 \n", "L 147.889721 214.838928 \n", "L 148.782283 216.211028 \n", "L 149.079804 216.166315 \n", "L 149.377324 215.91096 \n", "L 149.674845 215.943771 \n", "L 149.972366 216.107483 \n", "L 150.567407 216.846295 \n", "L 150.864928 216.989803 \n", "L 152.05501 216.91235 \n", "L 154.435176 218.239947 \n", "L 155.922779 218.665716 \n", "L 156.51782 218.68319 \n", "L 156.815341 218.585832 \n", "L 157.112862 218.17475 \n", "L 157.410382 217.564107 \n", "L 158.005424 213.921561 \n", "L 158.302944 206.706933 \n", "L 158.600465 211.840844 \n", "L 158.897986 211.020226 \n", "L 159.195506 217.511449 \n", "L 159.493027 219.705043 \n", "L 159.790548 218.932523 \n", "L 160.088068 217.030382 \n", "L 160.385589 214.343664 \n", "L 160.68311 217.617147 \n", "L 160.98063 219.590922 \n", "L 161.278151 220.037174 \n", "L 161.873192 217.373162 \n", "L 162.468233 219.93447 \n", "L 162.765754 220.507328 \n", "L 163.063275 220.000472 \n", "L 163.360795 219.094034 \n", "L 163.955837 220.037218 \n", "L 164.253357 220.798492 \n", "L 164.550878 220.972261 \n", "L 165.44344 220.057901 \n", "L 166.633523 221.498764 \n", "L 166.931043 221.631556 \n", "L 167.526085 221.543302 \n", "L 167.823605 221.410018 \n", "L 168.418647 220.87459 \n", "L 168.716167 220.084475 \n", "L 169.013688 219.747667 \n", "L 169.311209 217.884554 \n", "L 169.608729 218.039698 \n", "L 169.90625 215.353041 \n", "L 170.203771 217.775415 \n", "L 170.501291 217.478084 \n", "L 170.798812 220.395176 \n", "L 171.096333 221.750527 \n", "L 171.393853 222.56699 \n", "L 171.691374 222.488799 \n", "L 172.286415 221.314066 \n", "L 172.583936 220.692539 \n", "L 173.476498 222.795332 \n", "L 173.774019 223.073855 \n", "L 174.071539 222.961401 \n", "L 174.666581 222.282706 \n", "L 175.261622 222.29716 \n", "L 175.856663 222.974066 \n", "L 176.451705 223.523133 \n", "L 177.046746 223.76135 \n", "L 177.641787 223.812481 \n", "L 178.236829 223.675867 \n", "L 178.83187 222.955177 \n", "L 179.426911 220.211688 \n", "L 179.724432 214.529839 \n", "L 180.021952 215.224577 \n", "L 180.319473 207.12009 \n", "L 180.616994 218.707044 \n", "L 180.914514 222.83773 \n", "L 181.212035 224.486743 \n", "L 181.509556 222.90328 \n", "L 181.807076 219.265396 \n", "L 182.699638 224.418954 \n", "L 182.997159 224.192242 \n", "L 183.29468 222.367202 \n", "L 183.5922 222.650825 \n", "L 183.889721 223.457954 \n", "L 184.187242 224.744927 \n", "L 184.484762 224.595654 \n", "L 184.782283 223.561533 \n", "L 185.079804 223.633767 \n", "L 185.377324 224.099204 \n", "L 185.674845 224.982341 \n", "L 185.972366 225.145369 \n", "L 186.567407 224.380442 \n", "L 186.864928 224.193803 \n", "L 187.459969 225.195327 \n", "L 187.75749 225.519515 \n", "L 188.05501 225.577606 \n", "L 188.650052 225.224735 \n", "L 188.947572 224.886037 \n", "L 189.245093 224.768421 \n", "L 189.542614 224.323969 \n", "L 189.840134 224.335069 \n", "L 190.137655 223.668696 \n", "L 190.435176 223.770635 \n", "L 190.732696 222.707646 \n", "L 191.030217 223.059676 \n", "L 191.327738 221.703756 \n", "L 191.625258 222.745944 \n", "L 191.922779 221.956895 \n", "L 192.2203 223.624454 \n", "L 192.51782 224.029342 \n", "L 192.815341 225.306669 \n", "L 193.410382 226.376473 \n", "L 193.707903 226.433672 \n", "L 194.302944 225.958695 \n", "L 194.600465 225.56582 \n", "L 194.897986 225.568431 \n", "L 195.195506 225.396383 \n", "L 195.493027 225.740426 \n", "L 195.790548 225.862625 \n", "L 196.385589 226.431142 \n", "L 196.98063 226.800276 \n", "L 197.873192 227.064716 \n", "L 199.955837 227.361565 \n", "L 200.550878 227.264034 \n", "L 200.848399 226.984447 \n", "L 201.145919 225.974944 \n", "L 201.44344 223.904453 \n", "L 201.740961 215.405129 \n", "L 202.038481 211.468264 \n", "L 202.336002 182.232456 \n", "L 202.633523 224.897197 \n", "L 202.931043 215.962535 \n", "L 203.228564 169.37013 \n", "L 203.526085 225.738127 \n", "L 204.16991 -1 \n", "M 204.559887 -1 \n", "L 204.716167 137.624944 \n", "L 205.013688 166.811092 \n", "L 205.311209 85.684027 \n", "L 205.608729 175.769803 \n", "L 205.735104 -1 \n", "M 206.635384 -1 \n", "L 206.798812 141.916873 \n", "L 207.08804 -1 \n", "M 207.555573 -1 \n", "L 207.691374 106.596686 \n", "L 207.762501 -1 \n", "M 208.211459 -1 \n", "L 208.286415 114.342058 \n", "L 208.554606 -1 \n", "M 208.74643 -1 \n", "L 208.881457 9.481698 \n", "L 209.178977 135.085414 \n", "L 209.476498 100.876315 \n", "L 210.071539 120.814539 \n", "L 210.36906 110.712448 \n", "L 210.964101 157.535107 \n", "L 211.856663 184.776343 \n", "L 212.154184 181.149868 \n", "L 212.451705 168.659641 \n", "L 212.749225 173.120913 \n", "L 213.046746 186.476016 \n", "L 213.344267 193.755281 \n", "L 213.641787 194.465499 \n", "L 214.236829 182.262238 \n", "L 214.534349 184.863529 \n", "L 214.83187 193.203014 \n", "L 215.12939 198.253243 \n", "L 215.426911 198.860608 \n", "L 216.319473 195.40834 \n", "L 216.616994 195.499553 \n", "L 216.914514 196.544855 \n", "L 217.509556 200.943414 \n", "L 218.104597 205.332043 \n", "L 218.402118 205.765337 \n", "L 218.997159 204.035534 \n", "L 219.29468 204.73456 \n", "L 219.889721 208.781321 \n", "L 220.187242 209.750633 \n", "L 220.484762 209.84731 \n", "L 220.782283 209.700513 \n", "L 221.079804 209.857641 \n", "L 221.377324 210.559131 \n", "L 222.269886 213.648877 \n", "L 222.567407 213.781816 \n", "L 222.864928 213.672206 \n", "L 223.162448 213.946045 \n", "L 224.05501 216.064297 \n", "L 224.650052 216.211529 \n", "L 224.947572 216.495052 \n", "L 225.542614 217.509562 \n", "L 225.840134 217.766635 \n", "L 226.435176 217.869653 \n", "L 227.327738 218.755347 \n", "L 228.2203 219.024517 \n", "L 228.815341 219.442409 \n", "L 230.005424 219.811501 \n", "L 230.600465 219.984263 \n", "L 231.195506 220.110652 \n", "L 232.088068 220.348841 \n", "L 233.873192 220.697902 \n", "L 235.955837 221.07544 \n", "L 241.311209 221.892634 \n", "L 255.889721 224.149505 \n", "L 271.063275 226.697338 \n", "L 285.344267 229.089159 \n", "L 292.484762 230.039663 \n", "L 299.922779 230.798692 \n", "L 311.228564 231.698323 \n", "L 331.75749 233.08742 \n", "L 351.988895 234.239287 \n", "L 354.36906 234.294558 \n", "L 354.666581 234.209083 \n", "L 354.964101 233.926192 \n", "L 355.261622 233.343573 \n", "L 355.559143 231.583129 \n", "L 355.856663 230.657496 \n", "L 356.154184 227.698813 \n", "L 356.451705 231.966785 \n", "L 356.749225 234.107075 \n", "L 357.046746 234.246644 \n", "L 357.641787 231.28782 \n", "L 357.939308 233.421817 \n", "L 358.236829 234.492402 \n", "L 358.83187 233.236693 \n", "L 359.12939 233.368677 \n", "L 359.426911 234.42645 \n", "L 359.724432 234.248093 \n", "L 360.021952 233.443602 \n", "L 360.616994 234.51378 \n", "L 360.914514 234.407067 \n", "L 361.212035 234.048781 \n", "L 361.509556 234.085648 \n", "L 361.807076 234.547425 \n", "L 362.104597 234.511784 \n", "L 362.402118 234.188983 \n", "L 362.699638 234.330378 \n", "L 362.997159 234.603248 \n", "L 363.29468 234.626166 \n", "L 363.5922 234.453242 \n", "L 363.889721 234.410412 \n", "L 364.484762 234.714013 \n", "L 365.079804 234.559295 \n", "L 365.972366 234.768797 \n", "L 366.567407 234.682489 \n", "L 367.75749 234.819734 \n", "L 368.650052 234.844681 \n", "L 369.542614 234.91896 \n", "L 371.030217 234.956697 \n", "L 372.815341 235.039747 \n", "L 374.600465 234.976548 \n", "L 375.195506 234.810673 \n", "L 375.790548 234.323464 \n", "L 376.088068 233.578034 \n", "L 376.088068 233.578034 \n", "\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;stroke-width:1.5;\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path clip-path=\"url(#pb5d3382f96)\" d=\"M 85.236441 -1 \n", "L 86.005424 142.158085 \n", "L 86.600465 203.65556 \n", "L 86.897986 221.8224 \n", "L 87.195506 230.331552 \n", "L 87.493027 229.987596 \n", "L 87.790548 221.457849 \n", "L 88.088068 204.861357 \n", "L 88.68311 150.631583 \n", "L 89.575671 39.324987 \n", "L 89.872092 -1 \n", "M 95.833505 -1 \n", "L 96.716167 76.177211 \n", "L 97.311209 104.447549 \n", "L 97.90625 119.303676 \n", "L 98.203771 123.422934 \n", "L 98.501291 124.697404 \n", "L 98.798812 123.046272 \n", "L 99.691374 113.249831 \n", "L 100.881457 106.32256 \n", "L 101.178977 107.123622 \n", "L 101.774019 114.33501 \n", "L 102.36906 123.076688 \n", "L 102.964101 136.997774 \n", "L 104.749225 183.947378 \n", "L 105.939308 207.744169 \n", "L 106.534349 215.960964 \n", "L 107.426911 225.440668 \n", "L 108.319473 231.081522 \n", "L 108.914514 233.431133 \n", "L 109.509556 235.046688 \n", "L 110.104597 235.929215 \n", "L 110.699638 236.340864 \n", "L 111.29468 236.306907 \n", "L 111.889721 235.904438 \n", "L 112.484762 235.263456 \n", "L 113.674845 233.284289 \n", "L 115.75749 229.259142 \n", "L 116.947572 227.410829 \n", "L 118.435176 225.886063 \n", "L 119.030217 225.468968 \n", "L 120.51782 224.677369 \n", "L 122.005424 224.098152 \n", "L 124.98063 222.508613 \n", "L 126.170713 221.674342 \n", "L 131.228564 217.715017 \n", "L 132.418647 216.895417 \n", "L 135.988895 215.152636 \n", "L 136.286415 215.191609 \n", "L 136.583936 214.89118 \n", "L 136.881457 215.123884 \n", "L 137.178977 214.520409 \n", "L 137.476498 215.299741 \n", "L 137.774019 213.761989 \n", "L 138.071539 216.200609 \n", "L 138.36906 211.61659 \n", "L 138.666581 219.4599 \n", "L 138.964101 204.070564 \n", "L 139.261622 228.992238 \n", "L 139.559143 177.559443 \n", "L 139.856663 233.965574 \n", "L 140.154184 176.493036 \n", "L 140.451705 227.359585 \n", "L 140.749225 219.150957 \n", "L 141.046746 192.2257 \n", "L 141.344267 235.539418 \n", "L 141.641787 196.642359 \n", "L 142.236829 229.272992 \n", "L 142.534349 194.975671 \n", "L 142.83187 230.921591 \n", "L 143.12939 213.143599 \n", "L 143.426911 205.532339 \n", "L 143.724432 231.886533 \n", "L 144.021952 203.242642 \n", "L 144.319473 217.939147 \n", "L 144.616994 224.903902 \n", "L 144.914514 201.461605 \n", "L 145.212035 226.652662 \n", "L 145.509556 211.816155 \n", "L 145.807076 208.88708 \n", "L 146.104597 225.774801 \n", "L 146.402118 202.206024 \n", "L 146.699638 220.109829 \n", "L 146.997159 214.396107 \n", "L 147.29468 205.416897 \n", "L 147.5922 222.817847 \n", "L 147.889721 202.018141 \n", "L 148.187242 217.669328 \n", "L 148.782283 206.531302 \n", "L 149.079804 218.874554 \n", "L 149.377324 201.004608 \n", "L 149.674845 218.674101 \n", "L 149.972366 204.409993 \n", "L 150.269886 212.710108 \n", "L 150.567407 211.284733 \n", "L 150.864928 206.011575 \n", "L 151.162448 216.266758 \n", "L 151.459969 202.239167 \n", "L 151.75749 217.998542 \n", "L 152.05501 202.101115 \n", "L 152.352531 217.300413 \n", "L 152.650052 204.292022 \n", "L 152.947572 215.496201 \n", "L 153.245093 207.151533 \n", "L 153.542614 213.645731 \n", "L 153.840134 209.700246 \n", "L 154.137655 212.231265 \n", "L 154.732696 211.230262 \n", "L 155.030217 213.394005 \n", "L 155.327738 210.277708 \n", "L 155.625258 215.222965 \n", "L 155.922779 208.732686 \n", "L 156.2203 217.903424 \n", "L 156.51782 205.377128 \n", "L 156.815341 222.580492 \n", "L 157.112862 197.643883 \n", "L 157.410382 230.12471 \n", "L 157.707903 182.620486 \n", "L 158.005424 235.902517 \n", "L 158.302944 176.574707 \n", "L 158.600465 234.794778 \n", "L 158.897986 197.580543 \n", "L 159.195506 217.690877 \n", "L 159.493027 224.563933 \n", "L 159.790548 196.061306 \n", "L 160.088068 233.451781 \n", "L 160.385589 198.604552 \n", "L 160.68311 223.254583 \n", "L 160.98063 219.998065 \n", "L 161.278151 204.261596 \n", "L 161.575671 230.438492 \n", "L 161.873192 202.390713 \n", "L 162.170713 223.28797 \n", "L 162.468233 217.379123 \n", "L 162.765754 208.015808 \n", "L 163.063275 227.106391 \n", "L 163.360795 203.045131 \n", "L 163.658316 224.038575 \n", "L 163.955837 211.706871 \n", "L 164.253357 213.243229 \n", "L 164.550878 221.462302 \n", "L 164.848399 204.535446 \n", "L 165.145919 224.742414 \n", "L 165.44344 203.738504 \n", "L 165.740961 222.176783 \n", "L 166.038481 208.704679 \n", "L 166.336002 216.868109 \n", "L 166.931043 211.600263 \n", "L 167.228564 219.242124 \n", "L 167.526085 207.179965 \n", "L 167.823605 223.118658 \n", "L 168.121126 202.742633 \n", "L 168.418647 227.198116 \n", "L 168.716167 197.013176 \n", "L 169.013688 231.558059 \n", "L 169.311209 190.824694 \n", "L 169.608729 234.292004 \n", "L 169.90625 190.754586 \n", "L 170.203771 233.047422 \n", "L 170.501291 201.486412 \n", "L 170.798812 225.754878 \n", "L 171.096333 216.431811 \n", "L 171.393853 214.696169 \n", "L 171.691374 226.984481 \n", "L 171.988895 206.606314 \n", "L 172.286415 230.40595 \n", "L 172.583936 207.368438 \n", "L 172.881457 227.256582 \n", "L 173.178977 215.335061 \n", "L 173.774019 223.390017 \n", "L 174.071539 212.643907 \n", "L 174.36906 227.565831 \n", "L 174.666581 209.543931 \n", "L 174.964101 227.835828 \n", "L 175.261622 210.921082 \n", "L 175.559143 225.555688 \n", "L 175.856663 214.445859 \n", "L 176.154184 222.367474 \n", "L 176.451705 217.897064 \n", "L 177.046746 220.621302 \n", "L 177.344267 216.976861 \n", "L 177.641787 223.12022 \n", "L 177.939308 214.13113 \n", "L 178.236829 226.420169 \n", "L 178.534349 209.062579 \n", "L 178.83187 231.552913 \n", "L 179.12939 198.567297 \n", "L 179.426911 236.596938 \n", "L 179.724432 185.859113 \n", "L 180.021952 236.288519 \n", "L 180.319473 196.331488 \n", "L 180.616994 231.301222 \n", "L 180.914514 223.588877 \n", "L 181.212035 211.927564 \n", "L 181.509556 235.911302 \n", "L 181.807076 205.598427 \n", "L 182.104597 233.800621 \n", "L 182.402118 221.071878 \n", "L 182.699638 219.187378 \n", "L 182.997159 233.818638 \n", "L 183.29468 211.727899 \n", "L 183.5922 232.081564 \n", "L 183.889721 222.135129 \n", "L 184.187242 219.932445 \n", "L 184.484762 231.88226 \n", "L 184.782283 213.443208 \n", "L 185.079804 230.543075 \n", "L 185.377324 220.301083 \n", "L 185.674845 221.439331 \n", "L 185.972366 228.609589 \n", "L 186.269886 214.186637 \n", "L 186.567407 230.404814 \n", "L 186.864928 215.309694 \n", "L 187.162448 226.869891 \n", "L 187.459969 221.229418 \n", "L 187.75749 221.349125 \n", "L 188.05501 226.48306 \n", "L 188.352531 216.875171 \n", "L 188.650052 229.710163 \n", "L 188.947572 214.240234 \n", "L 189.245093 231.717533 \n", "L 189.542614 212.659684 \n", "L 189.840134 233.376563 \n", "L 190.137655 211.18159 \n", "L 190.435176 234.951182 \n", "L 190.732696 209.744136 \n", "L 191.030217 236.075212 \n", "L 191.327738 209.838356 \n", "L 191.625258 236.259795 \n", "L 191.922779 213.412288 \n", "L 192.2203 234.995492 \n", "L 192.51782 219.975216 \n", "L 192.815341 231.717979 \n", "L 193.112862 226.696147 \n", "L 193.410382 227.142095 \n", "L 193.707903 231.420003 \n", "L 194.005424 223.002511 \n", "L 194.302944 233.744516 \n", "L 194.600465 220.855793 \n", "L 194.897986 234.202163 \n", "L 195.195506 221.109235 \n", "L 195.493027 233.413564 \n", "L 195.790548 222.844995 \n", "L 196.088068 231.972707 \n", "L 196.385589 224.806469 \n", "L 196.68311 230.520558 \n", "L 196.98063 226.305025 \n", "L 197.278151 229.487427 \n", "L 197.575671 227.247809 \n", "L 197.873192 228.986562 \n", "L 198.170713 227.78697 \n", "L 198.468233 228.957648 \n", "L 198.765754 228.06362 \n", "L 199.063275 229.344473 \n", "L 199.360795 228.066746 \n", "L 199.658316 230.242779 \n", "L 199.955837 227.453714 \n", "L 200.253357 232.067371 \n", "L 200.550878 224.935849 \n", "L 200.848399 235.548488 \n", "L 201.145919 215.902933 \n", "L 201.44344 236.601256 \n", "L 201.740961 194.356469 \n", "L 202.038481 213.209019 \n", "L 202.336002 220.875721 \n", "L 202.931043 195.406831 \n", "L 203.228564 237.413868 \n", "L 203.730439 -1 \n", "M 204.3219 -1 \n", "L 204.418647 79.848983 \n", "L 204.716167 228.388143 \n", "L 205.013688 208.663013 \n", "L 205.311209 212.724351 \n", "L 205.608729 230.72769 \n", "L 205.90625 133.901337 \n", "L 206.203771 84.302634 \n", "L 206.501291 91.176119 \n", "L 206.584332 -1 \n", "M 207.486445 -1 \n", "L 207.691374 234.884593 \n", "L 207.884068 -1 \n", "M 208.182801 -1 \n", "L 208.286415 67.569269 \n", "L 208.583936 105.344884 \n", "L 208.740228 -1 \n", "M 209.856477 -1 \n", "L 210.071539 50.804637 \n", "L 210.36906 74.414336 \n", "L 210.666581 76.30664 \n", "L 210.964101 88.381944 \n", "L 211.559143 144.963189 \n", "L 212.154184 196.652906 \n", "L 212.451705 205.345041 \n", "L 212.749225 210.264221 \n", "L 213.641787 232.370733 \n", "L 213.939308 233.884125 \n", "L 214.236829 233.74109 \n", "L 214.534349 231.866765 \n", "L 215.724432 213.003893 \n", "L 216.021952 212.807203 \n", "L 216.319473 214.754393 \n", "L 216.914514 222.898124 \n", "L 217.509556 231.291662 \n", "L 217.807076 232.515402 \n", "L 218.104597 230.369411 \n", "L 218.699638 220.78822 \n", "L 218.997159 218.971518 \n", "L 219.29468 220.622535 \n", "L 219.889721 226.874212 \n", "L 220.187242 228.664861 \n", "L 220.484762 229.33243 \n", "L 220.782283 229.065528 \n", "L 221.079804 227.728081 \n", "L 221.377324 224.767591 \n", "L 221.674845 219.49261 \n", "L 222.864928 190.329463 \n", "L 223.162448 190.705005 \n", "L 224.05501 201.348944 \n", "L 224.352531 201.893994 \n", "L 224.650052 199.590929 \n", "L 224.947572 194.300716 \n", "L 226.137655 163.135223 \n", "L 226.435176 162.147023 \n", "L 226.732696 164.66261 \n", "L 227.327738 171.413963 \n", "L 227.625258 171.930517 \n", "L 227.922779 169.456911 \n", "L 228.51782 157.308893 \n", "L 229.112862 145.864385 \n", "L 229.410382 144.527043 \n", "L 229.707903 146.188179 \n", "L 230.302944 151.888617 \n", "L 230.600465 152.634949 \n", "L 230.897986 150.977492 \n", "L 232.088068 136.498763 \n", "L 232.385589 136.804279 \n", "L 233.278151 142.719664 \n", "L 233.575671 142.552069 \n", "L 233.873192 140.748442 \n", "L 234.468233 135.445141 \n", "L 234.765754 134.064672 \n", "L 235.063275 134.302894 \n", "L 235.955837 138.984088 \n", "L 236.253357 139.160222 \n", "L 236.550878 138.216763 \n", "L 237.145919 135.327586 \n", "L 237.44344 134.824545 \n", "L 237.740961 135.388709 \n", "L 238.633523 139.085038 \n", "L 238.931043 139.238856 \n", "L 239.228564 138.714775 \n", "L 239.823605 137.484587 \n", "L 240.121126 137.671311 \n", "L 240.418647 138.515036 \n", "L 241.013688 140.758493 \n", "L 241.311209 141.377408 \n", "L 241.608729 141.476596 \n", "L 242.203771 141.075282 \n", "L 242.501291 141.238684 \n", "L 242.798812 141.860778 \n", "L 243.691374 144.545357 \n", "L 243.988895 144.94848 \n", "L 244.881457 145.433187 \n", "L 245.178977 145.99515 \n", "L 246.36906 148.958837 \n", "L 247.261622 149.943323 \n", "L 247.856663 151.246846 \n", "L 248.749225 153.297126 \n", "L 249.939308 155.153755 \n", "L 251.12939 157.755178 \n", "L 252.319473 159.763782 \n", "L 253.509556 162.165654 \n", "L 254.699638 164.220767 \n", "L 255.889721 166.414833 \n", "L 257.079804 168.439976 \n", "L 258.269886 170.430712 \n", "L 263.625258 178.550849 \n", "L 266.302944 182.146816 \n", "L 271.063275 188.010737 \n", "L 274.931043 192.448103 \n", "L 279.393853 197.281906 \n", "L 283.856663 201.851714 \n", "L 288.616994 206.472656 \n", "L 294.269886 211.641383 \n", "L 298.435176 215.210567 \n", "L 302.897986 218.783845 \n", "L 307.955837 222.470206 \n", "L 312.121126 225.190349 \n", "L 315.988895 227.454964 \n", "L 319.856663 229.470883 \n", "L 323.724432 231.247891 \n", "L 328.187242 233.009819 \n", "L 332.947572 234.566757 \n", "L 337.410382 235.740818 \n", "L 341.278151 236.555062 \n", "L 345.740961 237.268883 \n", "L 349.608729 237.708397 \n", "L 352.881457 237.987697 \n", "L 353.178977 237.933518 \n", "L 353.476498 238.048265 \n", "L 353.774019 237.899337 \n", "L 354.071539 238.113042 \n", "L 354.36906 237.700119 \n", "L 354.666581 238.008613 \n", "L 354.964101 236.771216 \n", "L 355.261622 236.487516 \n", "L 355.559143 234.346148 \n", "L 355.856663 233.598986 \n", "L 356.451705 238.062006 \n", "L 356.749225 238.11866 \n", "L 357.046746 236.255274 \n", "L 357.344267 236.278942 \n", "L 357.641787 236.829504 \n", "L 357.939308 238.128646 \n", "L 358.236829 237.906764 \n", "L 358.534349 236.732876 \n", "L 358.83187 237.562968 \n", "L 359.12939 237.970449 \n", "L 360.021952 237.546323 \n", "L 360.319473 238.155717 \n", "L 360.616994 238.079436 \n", "L 360.914514 237.710144 \n", "L 361.212035 237.800106 \n", "L 361.509556 238.125942 \n", "L 361.807076 238.098001 \n", "L 362.104597 237.801568 \n", "L 362.699638 238.113464 \n", "L 363.29468 238.055796 \n", "L 363.5922 237.874955 \n", "L 363.889721 238.148644 \n", "L 364.187242 238.170628 \n", "L 364.484762 238.006033 \n", "L 364.782283 238.112978 \n", "L 365.079804 237.962734 \n", "L 365.377324 238.171517 \n", "L 365.674845 238.167432 \n", "L 365.972366 237.963952 \n", "L 366.269886 238.16007 \n", "L 366.567407 237.934205 \n", "L 366.864928 238.153327 \n", "L 367.162448 238.116188 \n", "L 367.459969 237.959212 \n", "L 367.75749 238.159855 \n", "L 368.05501 237.829797 \n", "L 368.352531 238.138246 \n", "L 368.650052 237.940722 \n", "L 369.245093 238.06069 \n", "L 369.542614 237.816705 \n", "L 369.840134 238.095087 \n", "L 370.137655 237.713563 \n", "L 370.435176 238.068685 \n", "L 370.732696 237.741482 \n", "L 371.030217 237.988822 \n", "L 371.327738 237.818602 \n", "L 371.922779 237.881729 \n", "L 372.2203 237.734014 \n", "L 372.51782 237.91811 \n", "L 372.815341 237.600701 \n", "L 373.112862 237.938985 \n", "L 373.410382 237.456021 \n", "L 373.707903 237.962425 \n", "L 374.005424 237.258623 \n", "L 374.302944 238.00703 \n", "L 374.600465 236.92132 \n", "L 374.897986 238.078778 \n", "L 375.195506 236.248656 \n", "L 375.493027 238.119887 \n", "L 375.790548 234.80332 \n", "L 376.088068 237.917091 \n", "L 376.088068 237.917091 \n", "\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;stroke-width:1.5;\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 56.50625 239.758125 \n", "L 56.50625 22.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 391.30625 239.758125 \n", "L 391.30625 22.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 56.50625 239.758125 \n", "L 391.30625 239.758125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 56.50625 22.318125 \n", "L 391.30625 22.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"text_16\">\n", "    <!-- Training and validation loss -->\n", "    <defs>\n", "     <path d=\"M -0.296875 72.90625 \n", "L 61.375 72.90625 \n", "L 61.375 64.59375 \n", "L 35.5 64.59375 \n", "L 35.5 0 \n", "L 25.59375 0 \n", "L 25.59375 64.59375 \n", "L -0.296875 64.59375 \n", "z\n", "\" id=\"DejaVuSans-84\"/>\n", "     <path d=\"M 41.109375 46.296875 \n", "Q 39.59375 47.171875 37.8125 47.578125 \n", "Q 36.03125 48 33.890625 48 \n", "Q 26.265625 48 22.1875 43.046875 \n", "Q 18.109375 38.09375 18.109375 28.8125 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 20.953125 51.171875 25.484375 53.578125 \n", "Q 30.03125 56 36.53125 56 \n", "Q 37.453125 56 38.578125 55.875 \n", "Q 39.703125 55.765625 41.0625 55.515625 \n", "z\n", "\" id=\"DejaVuSans-114\"/>\n", "     <path d=\"M 34.28125 27.484375 \n", "Q 23.390625 27.484375 19.1875 25 \n", "Q 14.984375 22.515625 14.984375 16.5 \n", "Q 14.984375 11.71875 18.140625 8.90625 \n", "Q 21.296875 6.109375 26.703125 6.109375 \n", "Q 34.1875 6.109375 38.703125 11.40625 \n", "Q 43.21875 16.703125 43.21875 25.484375 \n", "L 43.21875 27.484375 \n", "z\n", "M 52.203125 31.203125 \n", "L 52.203125 0 \n", "L 43.21875 0 \n", "L 43.21875 8.296875 \n", "Q 40.140625 3.328125 35.546875 0.953125 \n", "Q 30.953125 -1.421875 24.3125 -1.421875 \n", "Q 15.921875 -1.421875 10.953125 3.296875 \n", "Q 6 8.015625 6 15.921875 \n", "Q 6 25.140625 12.171875 29.828125 \n", "Q 18.359375 34.515625 30.609375 34.515625 \n", "L 43.21875 34.515625 \n", "L 43.21875 35.40625 \n", "Q 43.21875 41.609375 39.140625 45 \n", "Q 35.0625 48.390625 27.6875 48.390625 \n", "Q 23 48.390625 18.546875 47.265625 \n", "Q 14.109375 46.140625 10.015625 43.890625 \n", "L 10.015625 52.203125 \n", "Q 14.9375 54.109375 19.578125 55.046875 \n", "Q 24.21875 56 28.609375 56 \n", "Q 40.484375 56 46.34375 49.84375 \n", "Q 52.203125 43.703125 52.203125 31.203125 \n", "z\n", "\" id=\"DejaVuSans-97\"/>\n", "     <path d=\"M 9.421875 54.6875 \n", "L 18.40625 54.6875 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 64.59375 \n", "L 9.421875 64.59375 \n", "z\n", "\" id=\"DejaVuSans-105\"/>\n", "     <path d=\"M 54.890625 33.015625 \n", "L 54.890625 0 \n", "L 45.90625 0 \n", "L 45.90625 32.71875 \n", "Q 45.90625 40.484375 42.875 44.328125 \n", "Q 39.84375 48.1875 33.796875 48.1875 \n", "Q 26.515625 48.1875 22.3125 43.546875 \n", "Q 18.109375 38.921875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.34375 51.125 25.703125 53.5625 \n", "Q 30.078125 56 35.796875 56 \n", "Q 45.21875 56 50.046875 50.171875 \n", "Q 54.890625 44.34375 54.890625 33.015625 \n", "z\n", "\" id=\"DejaVuSans-110\"/>\n", "     <path d=\"M 45.40625 27.984375 \n", "Q 45.40625 37.75 41.375 43.109375 \n", "Q 37.359375 48.484375 30.078125 48.484375 \n", "Q 22.859375 48.484375 18.828125 43.109375 \n", "Q 14.796875 37.75 14.796875 27.984375 \n", "Q 14.796875 18.265625 18.828125 12.890625 \n", "Q 22.859375 7.515625 30.078125 7.515625 \n", "Q 37.359375 7.515625 41.375 12.890625 \n", "Q 45.40625 18.265625 45.40625 27.984375 \n", "z\n", "M 54.390625 6.78125 \n", "Q 54.390625 -7.171875 48.1875 -13.984375 \n", "Q 42 -20.796875 29.203125 -20.796875 \n", "Q 24.46875 -20.796875 20.265625 -20.09375 \n", "Q 16.0625 -19.390625 12.109375 -17.921875 \n", "L 12.109375 -9.1875 \n", "Q 16.0625 -11.328125 19.921875 -12.34375 \n", "Q 23.78125 -13.375 27.78125 -13.375 \n", "Q 36.625 -13.375 41.015625 -8.765625 \n", "Q 45.40625 -4.15625 45.40625 5.171875 \n", "L 45.40625 9.625 \n", "Q 42.625 4.78125 38.28125 2.390625 \n", "Q 33.9375 0 27.875 0 \n", "Q 17.828125 0 11.671875 7.65625 \n", "Q 5.515625 15.328125 5.515625 27.984375 \n", "Q 5.515625 40.671875 11.671875 48.328125 \n", "Q 17.828125 56 27.875 56 \n", "Q 33.9375 56 38.28125 53.609375 \n", "Q 42.625 51.21875 45.40625 46.390625 \n", "L 45.40625 54.6875 \n", "L 54.390625 54.6875 \n", "z\n", "\" id=\"DejaVuSans-103\"/>\n", "     <path id=\"DejaVuSans-32\"/>\n", "     <path d=\"M 45.40625 46.390625 \n", "L 45.40625 75.984375 \n", "L 54.390625 75.984375 \n", "L 54.390625 0 \n", "L 45.40625 0 \n", "L 45.40625 8.203125 \n", "Q 42.578125 3.328125 38.25 0.953125 \n", "Q 33.9375 -1.421875 27.875 -1.421875 \n", "Q 17.96875 -1.421875 11.734375 6.484375 \n", "Q 5.515625 14.40625 5.515625 27.296875 \n", "Q 5.515625 40.1875 11.734375 48.09375 \n", "Q 17.96875 56 27.875 56 \n", "Q 33.9375 56 38.25 53.625 \n", "Q 42.578125 51.265625 45.40625 46.390625 \n", "z\n", "M 14.796875 27.296875 \n", "Q 14.796875 17.390625 18.875 11.75 \n", "Q 22.953125 6.109375 30.078125 6.109375 \n", "Q 37.203125 6.109375 41.296875 11.75 \n", "Q 45.40625 17.390625 45.40625 27.296875 \n", "Q 45.40625 37.203125 41.296875 42.84375 \n", "Q 37.203125 48.484375 30.078125 48.484375 \n", "Q 22.953125 48.484375 18.875 42.84375 \n", "Q 14.796875 37.203125 14.796875 27.296875 \n", "z\n", "\" id=\"DejaVuSans-100\"/>\n", "     <path d=\"M 2.984375 54.6875 \n", "L 12.5 54.6875 \n", "L 29.59375 8.796875 \n", "L 46.6875 54.6875 \n", "L 56.203125 54.6875 \n", "L 35.6875 0 \n", "L 23.484375 0 \n", "z\n", "\" id=\"DejaVuSans-118\"/>\n", "     <path d=\"M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "\" id=\"DejaVuSans-108\"/>\n", "     <path d=\"M 18.3125 70.21875 \n", "L 18.3125 54.6875 \n", "L 36.8125 54.6875 \n", "L 36.8125 47.703125 \n", "L 18.3125 47.703125 \n", "L 18.3125 18.015625 \n", "Q 18.3125 11.328125 20.140625 9.421875 \n", "Q 21.96875 7.515625 27.59375 7.515625 \n", "L 36.8125 7.515625 \n", "L 36.8125 0 \n", "L 27.59375 0 \n", "Q 17.1875 0 13.234375 3.875 \n", "Q 9.28125 7.765625 9.28125 18.015625 \n", "L 9.28125 47.703125 \n", "L 2.6875 47.703125 \n", "L 2.6875 54.6875 \n", "L 9.28125 54.6875 \n", "L 9.28125 70.21875 \n", "z\n", "\" id=\"DejaVuSans-116\"/>\n", "    </defs>\n", "    <g transform=\"translate(142.09625 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-84\"/>\n", "     <use x=\"46.333984\" xlink:href=\"#DejaVuSans-114\"/>\n", "     <use x=\"87.447266\" xlink:href=\"#DejaVuSans-97\"/>\n", "     <use x=\"148.726562\" xlink:href=\"#DejaVuSans-105\"/>\n", "     <use x=\"176.509766\" xlink:href=\"#DejaVuSans-110\"/>\n", "     <use x=\"239.888672\" xlink:href=\"#DejaVuSans-105\"/>\n", "     <use x=\"267.671875\" xlink:href=\"#DejaVuSans-110\"/>\n", "     <use x=\"331.050781\" xlink:href=\"#DejaVuSans-103\"/>\n", "     <use x=\"394.527344\" xlink:href=\"#DejaVuSans-32\"/>\n", "     <use x=\"426.314453\" xlink:href=\"#DejaVuSans-97\"/>\n", "     <use x=\"487.59375\" xlink:href=\"#DejaVuSans-110\"/>\n", "     <use x=\"550.972656\" xlink:href=\"#DejaVuSans-100\"/>\n", "     <use x=\"614.449219\" xlink:href=\"#DejaVuSans-32\"/>\n", "     <use x=\"646.236328\" xlink:href=\"#DejaVuSans-118\"/>\n", "     <use x=\"705.416016\" xlink:href=\"#DejaVuSans-97\"/>\n", "     <use x=\"766.695312\" xlink:href=\"#DejaVuSans-108\"/>\n", "     <use x=\"794.478516\" xlink:href=\"#DejaVuSans-105\"/>\n", "     <use x=\"822.261719\" xlink:href=\"#DejaVuSans-100\"/>\n", "     <use x=\"885.738281\" xlink:href=\"#DejaVuSans-97\"/>\n", "     <use x=\"947.017578\" xlink:href=\"#DejaVuSans-116\"/>\n", "     <use x=\"986.226562\" xlink:href=\"#DejaVuSans-105\"/>\n", "     <use x=\"1014.009766\" xlink:href=\"#DejaVuSans-111\"/>\n", "     <use x=\"1075.191406\" xlink:href=\"#DejaVuSans-110\"/>\n", "     <use x=\"1138.570312\" xlink:href=\"#DejaVuSans-32\"/>\n", "     <use x=\"1170.357422\" xlink:href=\"#DejaVuSans-108\"/>\n", "     <use x=\"1198.140625\" xlink:href=\"#DejaVuSans-111\"/>\n", "     <use x=\"1259.322266\" xlink:href=\"#DejaVuSans-115\"/>\n", "     <use x=\"1311.421875\" xlink:href=\"#DejaVuSans-115\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 280.43125 59.674375 \n", "L 384.30625 59.674375 \n", "Q 386.30625 59.674375 386.30625 57.674375 \n", "L 386.30625 29.318125 \n", "Q 386.30625 27.318125 384.30625 27.318125 \n", "L 280.43125 27.318125 \n", "Q 278.43125 27.318125 278.43125 29.318125 \n", "L 278.43125 57.674375 \n", "Q 278.43125 59.674375 280.43125 59.674375 \n", "z\n", "\" style=\"fill:#ffffff;opacity:0.8;stroke:#cccccc;stroke-linejoin:miter;\"/>\n", "    </g>\n", "    <g id=\"line2d_16\">\n", "     <path d=\"M 282.43125 35.416562 \n", "L 302.43125 35.416562 \n", "\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;stroke-width:1.5;\"/>\n", "    </g>\n", "    <g id=\"line2d_17\"/>\n", "    <g id=\"text_17\">\n", "     <!-- Training loss -->\n", "     <g transform=\"translate(310.43125 38.916562)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-84\"/>\n", "      <use x=\"46.333984\" xlink:href=\"#DejaVuSans-114\"/>\n", "      <use x=\"87.447266\" xlink:href=\"#DejaVuSans-97\"/>\n", "      <use x=\"148.726562\" xlink:href=\"#DejaVuSans-105\"/>\n", "      <use x=\"176.509766\" xlink:href=\"#DejaVuSans-110\"/>\n", "      <use x=\"239.888672\" xlink:href=\"#DejaVuSans-105\"/>\n", "      <use x=\"267.671875\" xlink:href=\"#DejaVuSans-110\"/>\n", "      <use x=\"331.050781\" xlink:href=\"#DejaVuSans-103\"/>\n", "      <use x=\"394.527344\" xlink:href=\"#DejaVuSans-32\"/>\n", "      <use x=\"426.314453\" xlink:href=\"#DejaVuSans-108\"/>\n", "      <use x=\"454.097656\" xlink:href=\"#DejaVuSans-111\"/>\n", "      <use x=\"515.279297\" xlink:href=\"#DejaVuSans-115\"/>\n", "      <use x=\"567.378906\" xlink:href=\"#DejaVuSans-115\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_18\">\n", "     <path d=\"M 282.43125 50.094687 \n", "L 302.43125 50.094687 \n", "\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;stroke-width:1.5;\"/>\n", "    </g>\n", "    <g id=\"line2d_19\"/>\n", "    <g id=\"text_18\">\n", "     <!-- Validation loss -->\n", "     <defs>\n", "      <path d=\"M 28.609375 0 \n", "L 0.78125 72.90625 \n", "L 11.078125 72.90625 \n", "L 34.1875 11.53125 \n", "L 57.328125 72.90625 \n", "L 67.578125 72.90625 \n", "L 39.796875 0 \n", "z\n", "\" id=\"DejaVuSans-86\"/>\n", "     </defs>\n", "     <g transform=\"translate(310.43125 53.594687)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-86\"/>\n", "      <use x=\"60.658203\" xlink:href=\"#DejaVuSans-97\"/>\n", "      <use x=\"121.9375\" xlink:href=\"#DejaVuSans-108\"/>\n", "      <use x=\"149.720703\" xlink:href=\"#DejaVuSans-105\"/>\n", "      <use x=\"177.503906\" xlink:href=\"#DejaVuSans-100\"/>\n", "      <use x=\"240.980469\" xlink:href=\"#DejaVuSans-97\"/>\n", "      <use x=\"302.259766\" xlink:href=\"#DejaVuSans-116\"/>\n", "      <use x=\"341.46875\" xlink:href=\"#DejaVuSans-105\"/>\n", "      <use x=\"369.251953\" xlink:href=\"#DejaVuSans-111\"/>\n", "      <use x=\"430.433594\" xlink:href=\"#DejaVuSans-110\"/>\n", "      <use x=\"493.8125\" xlink:href=\"#DejaVuSans-32\"/>\n", "      <use x=\"525.599609\" xlink:href=\"#DejaVuSans-108\"/>\n", "      <use x=\"553.382812\" xlink:href=\"#DejaVuSans-111\"/>\n", "      <use x=\"614.564453\" xlink:href=\"#DejaVuSans-115\"/>\n", "      <use x=\"666.664062\" xlink:href=\"#DejaVuSans-115\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb5d3382f96\">\n", "   <rect height=\"217.44\" width=\"334.8\" x=\"56.50625\" y=\"22.318125\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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", "image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Created with matplotlib (https://matplotlib.org/) -->\n", "<svg height=\"277.314375pt\" version=\"1.1\" viewBox=\"0 0 392.14375 277.314375\" width=\"392.14375pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", " <defs>\n", "  <style type=\"text/css\">\n", "*{stroke-linecap:butt;stroke-linejoin:round;}\n", "  </style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 277.314375 \n", "L 392.14375 277.314375 \n", "L 392.14375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill:none;\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 50.14375 239.758125 \n", "L 384.94375 239.758125 \n", "L 384.94375 22.318125 \n", "L 50.14375 22.318125 \n", "z\n", "\" style=\"fill:#ffffff;\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 0 3.5 \n", "\" id=\"mee04f48fe8\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"65.064411\" xlink:href=\"#mee04f48fe8\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <defs>\n", "       <path d=\"M 31.78125 66.40625 \n", "Q 24.171875 66.40625 20.328125 58.90625 \n", "Q 16.5 51.421875 16.5 36.375 \n", "Q 16.5 21.390625 20.328125 13.890625 \n", "Q 24.171875 6.390625 31.78125 6.390625 \n", "Q 39.453125 6.390625 43.28125 13.890625 \n", "Q 47.125 21.390625 47.125 36.375 \n", "Q 47.125 51.421875 43.28125 58.90625 \n", "Q 39.453125 66.40625 31.78125 66.40625 \n", "z\n", "M 31.78125 74.21875 \n", "Q 44.046875 74.21875 50.515625 64.515625 \n", "Q 56.984375 54.828125 56.984375 36.375 \n", "Q 56.984375 17.96875 50.515625 8.265625 \n", "Q 44.046875 -1.421875 31.78125 -1.421875 \n", "Q 19.53125 -1.421875 13.0625 8.265625 \n", "Q 6.59375 17.96875 6.59375 36.375 \n", "Q 6.59375 54.828125 13.0625 64.515625 \n", "Q 19.53125 74.21875 31.78125 74.21875 \n", "z\n", "\" id=\"DejaVuSans-48\"/>\n", "      </defs>\n", "      <g transform=\"translate(61.883161 254.356562)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"124.568543\" xlink:href=\"#mee04f48fe8\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <defs>\n", "       <path d=\"M 19.1875 8.296875 \n", "L 53.609375 8.296875 \n", "L 53.609375 0 \n", "L 7.328125 0 \n", "L 7.328125 8.296875 \n", "Q 12.9375 14.109375 22.625 23.890625 \n", "Q 32.328125 33.6875 34.8125 36.53125 \n", "Q 39.546875 41.84375 41.421875 45.53125 \n", "Q 43.3125 49.21875 43.3125 52.78125 \n", "Q 43.3125 58.59375 39.234375 62.25 \n", "Q 35.15625 65.921875 28.609375 65.921875 \n", "Q 23.96875 65.921875 18.8125 64.3125 \n", "Q 13.671875 62.703125 7.8125 59.421875 \n", "L 7.8125 69.390625 \n", "Q 13.765625 71.78125 18.9375 73 \n", "Q 24.125 74.21875 28.421875 74.21875 \n", "Q 39.75 74.21875 46.484375 68.546875 \n", "Q 53.21875 62.890625 53.21875 53.421875 \n", "Q 53.21875 48.921875 51.53125 44.890625 \n", "Q 49.859375 40.875 45.40625 35.40625 \n", "Q 44.1875 33.984375 37.640625 27.21875 \n", "Q 31.109375 20.453125 19.1875 8.296875 \n", "z\n", "\" id=\"DejaVuSans-50\"/>\n", "      </defs>\n", "      <g transform=\"translate(115.024793 254.356562)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"184.072676\" xlink:href=\"#mee04f48fe8\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <defs>\n", "       <path d=\"M 37.796875 64.3125 \n", "L 12.890625 25.390625 \n", "L 37.796875 25.390625 \n", "z\n", "M 35.203125 72.90625 \n", "L 47.609375 72.90625 \n", "L 47.609375 25.390625 \n", "L 58.015625 25.390625 \n", "L 58.015625 17.1875 \n", "L 47.609375 17.1875 \n", "L 47.609375 0 \n", "L 37.796875 0 \n", "L 37.796875 17.1875 \n", "L 4.890625 17.1875 \n", "L 4.890625 26.703125 \n", "z\n", "\" id=\"DejaVuSans-52\"/>\n", "      </defs>\n", "      <g transform=\"translate(174.528926 254.356562)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-52\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"243.576808\" xlink:href=\"#mee04f48fe8\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <defs>\n", "       <path d=\"M 33.015625 40.375 \n", "Q 26.375 40.375 22.484375 35.828125 \n", "Q 18.609375 31.296875 18.609375 23.390625 \n", "Q 18.609375 15.53125 22.484375 10.953125 \n", "Q 26.375 6.390625 33.015625 6.390625 \n", "Q 39.65625 6.390625 43.53125 10.953125 \n", "Q 47.40625 15.53125 47.40625 23.390625 \n", "Q 47.40625 31.296875 43.53125 35.828125 \n", "Q 39.65625 40.375 33.015625 40.375 \n", "z\n", "M 52.59375 71.296875 \n", "L 52.59375 62.3125 \n", "Q 48.875 64.0625 45.09375 64.984375 \n", "Q 41.3125 65.921875 37.59375 65.921875 \n", "Q 27.828125 65.921875 22.671875 59.328125 \n", "Q 17.53125 52.734375 16.796875 39.40625 \n", "Q 19.671875 43.65625 24.015625 45.921875 \n", "Q 28.375 48.1875 33.59375 48.1875 \n", "Q 44.578125 48.1875 50.953125 41.515625 \n", "Q 57.328125 34.859375 57.328125 23.390625 \n", "Q 57.328125 12.15625 50.6875 5.359375 \n", "Q 44.046875 -1.421875 33.015625 -1.421875 \n", "Q 20.359375 -1.421875 13.671875 8.265625 \n", "Q 6.984375 17.96875 6.984375 36.375 \n", "Q 6.984375 53.65625 15.1875 63.9375 \n", "Q 23.390625 74.21875 37.203125 74.21875 \n", "Q 40.921875 74.21875 44.703125 73.484375 \n", "Q 48.484375 72.75 52.59375 71.296875 \n", "z\n", "\" id=\"DejaVuSans-54\"/>\n", "      </defs>\n", "      <g transform=\"translate(234.033058 254.356562)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"303.08094\" xlink:href=\"#mee04f48fe8\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 800 -->\n", "      <defs>\n", "       <path d=\"M 31.78125 34.625 \n", "Q 24.75 34.625 20.71875 30.859375 \n", "Q 16.703125 27.09375 16.703125 20.515625 \n", "Q 16.703125 13.921875 20.71875 10.15625 \n", "Q 24.75 6.390625 31.78125 6.390625 \n", "Q 38.8125 6.390625 42.859375 10.171875 \n", "Q 46.921875 13.96875 46.921875 20.515625 \n", "Q 46.921875 27.09375 42.890625 30.859375 \n", "Q 38.875 34.625 31.78125 34.625 \n", "z\n", "M 21.921875 38.8125 \n", "Q 15.578125 40.375 12.03125 44.71875 \n", "Q 8.5 49.078125 8.5 55.328125 \n", "Q 8.5 64.0625 14.71875 69.140625 \n", "Q 20.953125 74.21875 31.78125 74.21875 \n", "Q 42.671875 74.21875 48.875 69.140625 \n", "Q 55.078125 64.0625 55.078125 55.328125 \n", "Q 55.078125 49.078125 51.53125 44.71875 \n", "Q 48 40.375 41.703125 38.8125 \n", "Q 48.828125 37.15625 52.796875 32.3125 \n", "Q 56.78125 27.484375 56.78125 20.515625 \n", "Q 56.78125 9.90625 50.3125 4.234375 \n", "Q 43.84375 -1.421875 31.78125 -1.421875 \n", "Q 19.734375 -1.421875 13.25 4.234375 \n", "Q 6.78125 9.90625 6.78125 20.515625 \n", "Q 6.78125 27.484375 10.78125 32.3125 \n", "Q 14.796875 37.15625 21.921875 38.8125 \n", "z\n", "M 18.3125 54.390625 \n", "Q 18.3125 48.734375 21.84375 45.5625 \n", "Q 25.390625 42.390625 31.78125 42.390625 \n", "Q 38.140625 42.390625 41.71875 45.5625 \n", "Q 45.3125 48.734375 45.3125 54.390625 \n", "Q 45.3125 60.0625 41.71875 63.234375 \n", "Q 38.140625 66.40625 31.78125 66.40625 \n", "Q 25.390625 66.40625 21.84375 63.234375 \n", "Q 18.3125 60.0625 18.3125 54.390625 \n", "z\n", "\" id=\"DejaVuSans-56\"/>\n", "      </defs>\n", "      <g transform=\"translate(293.53719 254.356562)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"362.585072\" xlink:href=\"#mee04f48fe8\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1000 -->\n", "      <defs>\n", "       <path d=\"M 12.40625 8.296875 \n", "L 28.515625 8.296875 \n", "L 28.515625 63.921875 \n", "L 10.984375 60.40625 \n", "L 10.984375 69.390625 \n", "L 28.421875 72.90625 \n", "L 38.28125 72.90625 \n", "L 38.28125 8.296875 \n", "L 54.390625 8.296875 \n", "L 54.390625 0 \n", "L 12.40625 0 \n", "z\n", "\" id=\"DejaVuSans-49\"/>\n", "      </defs>\n", "      <g transform=\"translate(349.860072 254.356562)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- Epochs -->\n", "     <defs>\n", "      <path d=\"M 9.8125 72.90625 \n", "L 55.90625 72.90625 \n", "L 55.90625 64.59375 \n", "L 19.671875 64.59375 \n", "L 19.671875 43.015625 \n", "L 54.390625 43.015625 \n", "L 54.390625 34.71875 \n", "L 19.671875 34.71875 \n", "L 19.671875 8.296875 \n", "L 56.78125 8.296875 \n", "L 56.78125 0 \n", "L 9.8125 0 \n", "z\n", "\" id=\"DejaVuSans-69\"/>\n", "      <path d=\"M 18.109375 8.203125 \n", "L 18.109375 -20.796875 \n", "L 9.078125 -20.796875 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.390625 \n", "Q 20.953125 51.265625 25.265625 53.625 \n", "Q 29.59375 56 35.59375 56 \n", "Q 45.5625 56 51.78125 48.09375 \n", "Q 58.015625 40.1875 58.015625 27.296875 \n", "Q 58.015625 14.40625 51.78125 6.484375 \n", "Q 45.5625 -1.421875 35.59375 -1.421875 \n", "Q 29.59375 -1.421875 25.265625 0.953125 \n", "Q 20.953125 3.328125 18.109375 8.203125 \n", "z\n", "M 48.6875 27.296875 \n", "Q 48.6875 37.203125 44.609375 42.84375 \n", "Q 40.53125 48.484375 33.40625 48.484375 \n", "Q 26.265625 48.484375 22.1875 42.84375 \n", "Q 18.109375 37.203125 18.109375 27.296875 \n", "Q 18.109375 17.390625 22.1875 11.75 \n", "Q 26.265625 6.109375 33.40625 6.109375 \n", "Q 40.53125 6.109375 44.609375 11.75 \n", "Q 48.6875 17.390625 48.6875 27.296875 \n", "z\n", "\" id=\"DejaVuSans-112\"/>\n", "      <path d=\"M 30.609375 48.390625 \n", "Q 23.390625 48.390625 19.1875 42.75 \n", "Q 14.984375 37.109375 14.984375 27.296875 \n", "Q 14.984375 17.484375 19.15625 11.84375 \n", "Q 23.34375 6.203125 30.609375 6.203125 \n", "Q 37.796875 6.203125 41.984375 11.859375 \n", "Q 46.1875 17.53125 46.1875 27.296875 \n", "Q 46.1875 37.015625 41.984375 42.703125 \n", "Q 37.796875 48.390625 30.609375 48.390625 \n", "z\n", "M 30.609375 56 \n", "Q 42.328125 56 49.015625 48.375 \n", "Q 55.71875 40.765625 55.71875 27.296875 \n", "Q 55.71875 13.875 49.015625 6.21875 \n", "Q 42.328125 -1.421875 30.609375 -1.421875 \n", "Q 18.84375 -1.421875 12.171875 6.21875 \n", "Q 5.515625 13.875 5.515625 27.296875 \n", "Q 5.515625 40.765625 12.171875 48.375 \n", "Q 18.84375 56 30.609375 56 \n", "z\n", "\" id=\"DejaVuSans-111\"/>\n", "      <path d=\"M 48.78125 52.59375 \n", "L 48.78125 44.1875 \n", "Q 44.96875 46.296875 41.140625 47.34375 \n", "Q 37.3125 48.390625 33.40625 48.390625 \n", "Q 24.65625 48.390625 19.8125 42.84375 \n", "Q 14.984375 37.3125 14.984375 27.296875 \n", "Q 14.984375 17.28125 19.8125 11.734375 \n", "Q 24.65625 6.203125 33.40625 6.203125 \n", "Q 37.3125 6.203125 41.140625 7.25 \n", "Q 44.96875 8.296875 48.78125 10.40625 \n", "L 48.78125 2.09375 \n", "Q 45.015625 0.34375 40.984375 -0.53125 \n", "Q 36.96875 -1.421875 32.421875 -1.421875 \n", "Q 20.0625 -1.421875 12.78125 6.34375 \n", "Q 5.515625 14.109375 5.515625 27.296875 \n", "Q 5.515625 40.671875 12.859375 48.328125 \n", "Q 20.21875 56 33.015625 56 \n", "Q 37.15625 56 41.109375 55.140625 \n", "Q 45.0625 54.296875 48.78125 52.59375 \n", "z\n", "\" id=\"DejaVuSans-99\"/>\n", "      <path d=\"M 54.890625 33.015625 \n", "L 54.890625 0 \n", "L 45.90625 0 \n", "L 45.90625 32.71875 \n", "Q 45.90625 40.484375 42.875 44.328125 \n", "Q 39.84375 48.1875 33.796875 48.1875 \n", "Q 26.515625 48.1875 22.3125 43.546875 \n", "Q 18.109375 38.921875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 75.984375 \n", "L 18.109375 75.984375 \n", "L 18.109375 46.1875 \n", "Q 21.34375 51.125 25.703125 53.5625 \n", "Q 30.078125 56 35.796875 56 \n", "Q 45.21875 56 50.046875 50.171875 \n", "Q 54.890625 44.34375 54.890625 33.015625 \n", "z\n", "\" id=\"DejaVuSans-104\"/>\n", "      <path d=\"M 44.28125 53.078125 \n", "L 44.28125 44.578125 \n", "Q 40.484375 46.53125 36.375 47.5 \n", "Q 32.28125 48.484375 27.875 48.484375 \n", "Q 21.1875 48.484375 17.84375 46.4375 \n", "Q 14.5 44.390625 14.5 40.28125 \n", "Q 14.5 37.15625 16.890625 35.375 \n", "Q 19.28125 33.59375 26.515625 31.984375 \n", "L 29.59375 31.296875 \n", "Q 39.15625 29.25 43.1875 25.515625 \n", "Q 47.21875 21.78125 47.21875 15.09375 \n", "Q 47.21875 7.46875 41.1875 3.015625 \n", "Q 35.15625 -1.421875 24.609375 -1.421875 \n", "Q 20.21875 -1.421875 15.453125 -0.5625 \n", "Q 10.6875 0.296875 5.421875 2 \n", "L 5.421875 11.28125 \n", "Q 10.40625 8.6875 15.234375 7.390625 \n", "Q 20.0625 6.109375 24.8125 6.109375 \n", "Q 31.15625 6.109375 34.5625 8.28125 \n", "Q 37.984375 10.453125 37.984375 14.40625 \n", "Q 37.984375 18.0625 35.515625 20.015625 \n", "Q 33.0625 21.96875 24.703125 23.78125 \n", "L 21.578125 24.515625 \n", "Q 13.234375 26.265625 9.515625 29.90625 \n", "Q 5.8125 33.546875 5.8125 39.890625 \n", "Q 5.8125 47.609375 11.28125 51.796875 \n", "Q 16.75 56 26.8125 56 \n", "Q 31.78125 56 36.171875 55.265625 \n", "Q 40.578125 54.546875 44.28125 53.078125 \n", "z\n", "\" id=\"DejaVuSans-115\"/>\n", "     </defs>\n", "     <g transform=\"translate(199.628125 268.034687)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-69\"/>\n", "      <use x=\"63.183594\" xlink:href=\"#DejaVuSans-112\"/>\n", "      <use x=\"126.660156\" xlink:href=\"#DejaVuSans-111\"/>\n", "      <use x=\"187.841797\" xlink:href=\"#DejaVuSans-99\"/>\n", "      <use x=\"242.822266\" xlink:href=\"#DejaVuSans-104\"/>\n", "      <use x=\"306.201172\" xlink:href=\"#DejaVuSans-115\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L -3.5 0 \n", "\" id=\"m9d54897533\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#m9d54897533\" y=\"239.758125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.00 -->\n", "      <defs>\n", "       <path d=\"M 10.6875 12.40625 \n", "L 21 12.40625 \n", "L 21 0 \n", "L 10.6875 0 \n", "z\n", "\" id=\"DejaVuSans-46\"/>\n", "      </defs>\n", "      <g transform=\"translate(20.878125 243.557344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#m9d54897533\" y=\"203.518125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.01 -->\n", "      <g transform=\"translate(20.878125 207.317344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-49\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#m9d54897533\" y=\"167.278125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.02 -->\n", "      <g transform=\"translate(20.878125 171.077344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-50\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#m9d54897533\" y=\"131.038125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.03 -->\n", "      <defs>\n", "       <path d=\"M 40.578125 39.3125 \n", "Q 47.65625 37.796875 51.625 33 \n", "Q 55.609375 28.21875 55.609375 21.1875 \n", "Q 55.609375 10.40625 48.1875 4.484375 \n", "Q 40.765625 -1.421875 27.09375 -1.421875 \n", "Q 22.515625 -1.421875 17.65625 -0.515625 \n", "Q 12.796875 0.390625 7.625 2.203125 \n", "L 7.625 11.71875 \n", "Q 11.71875 9.328125 16.59375 8.109375 \n", "Q 21.484375 6.890625 26.8125 6.890625 \n", "Q 36.078125 6.890625 40.9375 10.546875 \n", "Q 45.796875 14.203125 45.796875 21.1875 \n", "Q 45.796875 27.640625 41.28125 31.265625 \n", "Q 36.765625 34.90625 28.71875 34.90625 \n", "L 20.21875 34.90625 \n", "L 20.21875 43.015625 \n", "L 29.109375 43.015625 \n", "Q 36.375 43.015625 40.234375 45.921875 \n", "Q 44.09375 48.828125 44.09375 54.296875 \n", "Q 44.09375 59.90625 40.109375 62.90625 \n", "Q 36.140625 65.921875 28.71875 65.921875 \n", "Q 24.65625 65.921875 20.015625 65.03125 \n", "Q 15.375 64.15625 9.8125 62.3125 \n", "L 9.8125 71.09375 \n", "Q 15.4375 72.65625 20.34375 73.4375 \n", "Q 25.25 74.21875 29.59375 74.21875 \n", "Q 40.828125 74.21875 47.359375 69.109375 \n", "Q 53.90625 64.015625 53.90625 55.328125 \n", "Q 53.90625 49.265625 50.4375 45.09375 \n", "Q 46.96875 40.921875 40.578125 39.3125 \n", "z\n", "\" id=\"DejaVuSans-51\"/>\n", "      </defs>\n", "      <g transform=\"translate(20.878125 134.837344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-51\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#m9d54897533\" y=\"94.798125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.04 -->\n", "      <g transform=\"translate(20.878125 98.597344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-52\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#m9d54897533\" y=\"58.558125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.05 -->\n", "      <defs>\n", "       <path d=\"M 10.796875 72.90625 \n", "L 49.515625 72.90625 \n", "L 49.515625 64.59375 \n", "L 19.828125 64.59375 \n", "L 19.828125 46.734375 \n", "Q 21.96875 47.46875 24.109375 47.828125 \n", "Q 26.265625 48.1875 28.421875 48.1875 \n", "Q 40.625 48.1875 47.75 41.5 \n", "Q 54.890625 34.8125 54.890625 23.390625 \n", "Q 54.890625 11.625 47.5625 5.09375 \n", "Q 40.234375 -1.421875 26.90625 -1.421875 \n", "Q 22.3125 -1.421875 17.546875 -0.640625 \n", "Q 12.796875 0.140625 7.71875 1.703125 \n", "L 7.71875 11.625 \n", "Q 12.109375 9.234375 16.796875 8.0625 \n", "Q 21.484375 6.890625 26.703125 6.890625 \n", "Q 35.15625 6.890625 40.078125 11.328125 \n", "Q 45.015625 15.765625 45.015625 23.390625 \n", "Q 45.015625 31 40.078125 35.4375 \n", "Q 35.15625 39.890625 26.703125 39.890625 \n", "Q 22.75 39.890625 18.8125 39.015625 \n", "Q 14.890625 38.140625 10.796875 36.28125 \n", "z\n", "\" id=\"DejaVuSans-53\"/>\n", "      </defs>\n", "      <g transform=\"translate(20.878125 62.357344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-53\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#m9d54897533\" y=\"22.318125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0.06 -->\n", "      <g transform=\"translate(20.878125 26.117344)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-54\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- mae -->\n", "     <defs>\n", "      <path d=\"M 52 44.1875 \n", "Q 55.375 50.25 60.0625 53.125 \n", "Q 64.75 56 71.09375 56 \n", "Q 79.640625 56 84.28125 50.015625 \n", "Q 88.921875 44.046875 88.921875 33.015625 \n", "L 88.921875 0 \n", "L 79.890625 0 \n", "L 79.890625 32.71875 \n", "Q 79.890625 40.578125 77.09375 44.375 \n", "Q 74.3125 48.1875 68.609375 48.1875 \n", "Q 61.625 48.1875 57.5625 43.546875 \n", "Q 53.515625 38.921875 53.515625 30.90625 \n", "L 53.515625 0 \n", "L 44.484375 0 \n", "L 44.484375 32.71875 \n", "Q 44.484375 40.625 41.703125 44.40625 \n", "Q 38.921875 48.1875 33.109375 48.1875 \n", "Q 26.21875 48.1875 22.15625 43.53125 \n", "Q 18.109375 38.875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.1875 51.21875 25.484375 53.609375 \n", "Q 29.78125 56 35.6875 56 \n", "Q 41.65625 56 45.828125 52.96875 \n", "Q 50 49.953125 52 44.1875 \n", "z\n", "\" id=\"DejaVuSans-109\"/>\n", "      <path d=\"M 34.28125 27.484375 \n", "Q 23.390625 27.484375 19.1875 25 \n", "Q 14.984375 22.515625 14.984375 16.5 \n", "Q 14.984375 11.71875 18.140625 8.90625 \n", "Q 21.296875 6.109375 26.703125 6.109375 \n", "Q 34.1875 6.109375 38.703125 11.40625 \n", "Q 43.21875 16.703125 43.21875 25.484375 \n", "L 43.21875 27.484375 \n", "z\n", "M 52.203125 31.203125 \n", "L 52.203125 0 \n", "L 43.21875 0 \n", "L 43.21875 8.296875 \n", "Q 40.140625 3.328125 35.546875 0.953125 \n", "Q 30.953125 -1.421875 24.3125 -1.421875 \n", "Q 15.921875 -1.421875 10.953125 3.296875 \n", "Q 6 8.015625 6 15.921875 \n", "Q 6 25.140625 12.171875 29.828125 \n", "Q 18.359375 34.515625 30.609375 34.515625 \n", "L 43.21875 34.515625 \n", "L 43.21875 35.40625 \n", "Q 43.21875 41.609375 39.140625 45 \n", "Q 35.0625 48.390625 27.6875 48.390625 \n", "Q 23 48.390625 18.546875 47.265625 \n", "Q 14.109375 46.140625 10.015625 43.890625 \n", "L 10.015625 52.203125 \n", "Q 14.9375 54.109375 19.578125 55.046875 \n", "Q 24.21875 56 28.609375 56 \n", "Q 40.484375 56 46.34375 49.84375 \n", "Q 52.203125 43.703125 52.203125 31.203125 \n", "z\n", "\" id=\"DejaVuSans-97\"/>\n", "      <path d=\"M 56.203125 29.59375 \n", "L 56.203125 25.203125 \n", "L 14.890625 25.203125 \n", "Q 15.484375 15.921875 20.484375 11.0625 \n", "Q 25.484375 6.203125 34.421875 6.203125 \n", "Q 39.59375 6.203125 44.453125 7.46875 \n", "Q 49.3125 8.734375 54.109375 11.28125 \n", "L 54.109375 2.78125 \n", "Q 49.265625 0.734375 44.1875 -0.34375 \n", "Q 39.109375 -1.421875 33.890625 -1.421875 \n", "Q 20.796875 -1.421875 13.15625 6.1875 \n", "Q 5.515625 13.8125 5.515625 26.8125 \n", "Q 5.515625 40.234375 12.765625 48.109375 \n", "Q 20.015625 56 32.328125 56 \n", "Q 43.359375 56 49.78125 48.890625 \n", "Q 56.203125 41.796875 56.203125 29.59375 \n", "z\n", "M 47.21875 32.234375 \n", "Q 47.125 39.59375 43.09375 43.984375 \n", "Q 39.0625 48.390625 32.421875 48.390625 \n", "Q 24.90625 48.390625 20.390625 44.140625 \n", "Q 15.875 39.890625 15.1875 32.171875 \n", "z\n", "\" id=\"DejaVuSans-101\"/>\n", "     </defs>\n", "     <g transform=\"translate(14.798438 142.049062)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-109\"/>\n", "      <use x=\"97.412109\" xlink:href=\"#DejaVuSans-97\"/>\n", "      <use x=\"158.691406\" xlink:href=\"#DejaVuSans-101\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path clip-path=\"url(#pd4addc3cb9)\" d=\"M 101.57559 -1 \n", "L 103.444576 14.515233 \n", "L 104.634659 22.672503 \n", "L 106.122262 31.127958 \n", "L 107.609866 38.287734 \n", "L 109.097469 44.37151 \n", "L 110.882593 50.682661 \n", "L 112.965238 57.389091 \n", "L 115.047882 63.48329 \n", "L 117.130527 68.953524 \n", "L 119.510692 74.542401 \n", "L 121.890857 79.55767 \n", "L 124.271023 84.066511 \n", "L 126.948709 88.653451 \n", "L 129.923915 93.266556 \n", "L 131.411519 95.238553 \n", "L 131.709039 95.934785 \n", "L 132.00656 95.690103 \n", "L 132.304081 96.737887 \n", "L 132.601601 94.233998 \n", "L 132.899122 94.090664 \n", "L 133.494163 57.263145 \n", "L 133.723269 -1 \n", "M 133.862636 -1 \n", "L 134.089205 54.557979 \n", "L 134.386725 83.66305 \n", "L 134.684246 97.96798 \n", "L 134.981767 79.387983 \n", "L 135.279287 37.865925 \n", "L 135.576808 85.278554 \n", "L 135.874329 101.013669 \n", "L 136.171849 83.034307 \n", "L 136.46937 82.120772 \n", "L 136.76689 77.698105 \n", "L 137.064411 101.851223 \n", "L 137.361932 96.881896 \n", "L 137.659452 73.6192 \n", "L 137.956973 95.328885 \n", "L 138.254494 102.806799 \n", "L 138.552014 95.442032 \n", "L 138.849535 95.279001 \n", "L 139.147056 91.167425 \n", "L 139.444576 104.276647 \n", "L 139.742097 103.676917 \n", "L 140.039618 92.055876 \n", "L 140.337138 100.142336 \n", "L 140.634659 101.120619 \n", "L 140.93218 105.199781 \n", "L 141.2297 104.518643 \n", "L 141.527221 96.12822 \n", "L 141.824742 103.47387 \n", "L 142.122262 102.963715 \n", "L 142.419783 106.767764 \n", "L 142.717304 107.050936 \n", "L 143.014824 101.300243 \n", "L 143.312345 105.372438 \n", "L 143.609866 101.522406 \n", "L 143.907386 107.782944 \n", "L 144.204907 107.119032 \n", "L 144.799948 109.033545 \n", "L 145.097469 105.098906 \n", "L 145.39499 108.399334 \n", "L 145.69251 103.700974 \n", "L 145.990031 108.816485 \n", "L 146.287552 105.286373 \n", "L 146.585072 110.225513 \n", "L 146.882593 107.92883 \n", "L 147.180114 111.381125 \n", "L 147.477634 110.078682 \n", "L 147.775155 111.908102 \n", "L 148.072676 111.560262 \n", "L 148.667717 112.614013 \n", "L 148.965238 112.176491 \n", "L 149.262758 113.438189 \n", "L 149.560279 111.918605 \n", "L 149.8578 113.991532 \n", "L 150.15532 110.493389 \n", "L 150.452841 113.205347 \n", "L 150.750362 104.758087 \n", "L 151.047882 106.58297 \n", "L 151.345403 83.474705 \n", "L 151.642924 82.746639 \n", "L 151.940444 35.444444 \n", "L 152.237965 69.526254 \n", "L 152.535486 56.373466 \n", "L 152.833006 104.422803 \n", "L 153.130527 115.579482 \n", "L 153.725568 100.385614 \n", "L 154.023089 74.381327 \n", "L 154.32061 103.708791 \n", "L 154.61813 111.919375 \n", "L 154.915651 116.035014 \n", "L 155.213171 111.352275 \n", "L 155.510692 93.290708 \n", "L 155.808213 109.573563 \n", "L 156.105733 112.652193 \n", "L 156.403254 118.415333 \n", "L 156.700775 116.644681 \n", "L 156.998295 104.138564 \n", "L 157.295816 112.73842 \n", "L 157.593337 110.768933 \n", "L 157.890857 119.872059 \n", "L 158.188378 120.235059 \n", "L 158.485899 114.604736 \n", "L 158.783419 116.678378 \n", "L 159.08094 108.429616 \n", "L 159.378461 116.706297 \n", "L 159.675981 113.345252 \n", "L 159.973502 120.66035 \n", "L 160.271023 119.699996 \n", "L 160.568543 121.187381 \n", "L 160.866064 121.988714 \n", "L 161.163585 118.194115 \n", "L 161.461105 120.833304 \n", "L 161.758626 113.172783 \n", "L 162.056147 117.275706 \n", "L 162.353667 104.359783 \n", "L 162.651188 109.561048 \n", "L 162.948709 88.164951 \n", "L 163.246229 97.740187 \n", "L 163.54375 71.815823 \n", "L 163.841271 95.498532 \n", "L 164.138791 84.427175 \n", "L 164.436312 112.53027 \n", "L 164.733833 115.477068 \n", "L 165.031353 125.243578 \n", "L 165.328874 125.134454 \n", "L 165.626395 115.00871 \n", "L 165.923915 117.65384 \n", "L 166.221436 105.61496 \n", "L 166.518957 118.454862 \n", "L 166.816477 116.254424 \n", "L 167.113998 126.081214 \n", "L 167.411519 126.338991 \n", "L 167.709039 123.690095 \n", "L 168.00656 124.877513 \n", "L 168.304081 116.352612 \n", "L 168.601601 122.26172 \n", "L 168.899122 115.762899 \n", "L 169.196643 124.229951 \n", "L 169.494163 120.994392 \n", "L 169.791684 127.37403 \n", "L 170.089205 125.770136 \n", "L 170.386725 128.479171 \n", "L 170.684246 128.235373 \n", "L 170.981767 127.788197 \n", "L 171.279287 129.067574 \n", "L 171.576808 125.95331 \n", "L 171.874329 128.344281 \n", "L 172.171849 121.27026 \n", "L 172.46937 123.551985 \n", "L 172.76689 105.840985 \n", "L 173.064411 104.169454 \n", "L 173.361932 61.350799 \n", "L 173.659452 72.099454 \n", "L 173.956973 26.566844 \n", "L 174.254494 93.241351 \n", "L 174.849535 131.013354 \n", "L 175.147056 121.93486 \n", "L 175.444576 88.127595 \n", "L 175.742097 106.896545 \n", "L 176.039618 108.741463 \n", "L 176.337138 131.657926 \n", "L 176.634659 130.183454 \n", "L 176.93218 110.070069 \n", "L 177.2297 118.951598 \n", "L 177.527221 118.671032 \n", "L 177.824742 132.7022 \n", "L 178.122262 131.783265 \n", "L 178.419783 118.631138 \n", "L 178.717304 124.694906 \n", "L 179.014824 122.536198 \n", "L 179.312345 133.08294 \n", "L 179.609866 133.577542 \n", "L 179.907386 126.736688 \n", "L 180.204907 128.514724 \n", "L 180.502428 121.448385 \n", "L 180.799948 130.511617 \n", "L 181.097469 129.882914 \n", "L 181.39499 134.474053 \n", "L 181.69251 134.643558 \n", "L 181.990031 131.109734 \n", "L 182.287552 132.720257 \n", "L 182.585072 125.129351 \n", "L 182.882593 129.471987 \n", "L 183.180114 119.735434 \n", "L 183.477634 126.16601 \n", "L 183.775155 113.964085 \n", "L 184.072676 121.768886 \n", "L 184.370196 106.04175 \n", "L 184.667717 116.244069 \n", "L 184.965238 98.310811 \n", "L 185.262758 113.644611 \n", "L 185.560279 99.702802 \n", "L 185.8578 119.784954 \n", "L 186.15532 115.280636 \n", "L 186.452841 131.959034 \n", "L 186.750362 132.38041 \n", "L 187.047882 138.467143 \n", "L 187.345403 138.641758 \n", "L 187.642924 134.484867 \n", "L 187.940444 135.802774 \n", "L 188.237965 127.342217 \n", "L 188.535486 132.669839 \n", "L 188.833006 125.312511 \n", "L 189.130527 133.491544 \n", "L 189.428048 129.045413 \n", "L 189.725568 136.547925 \n", "L 190.023089 134.043605 \n", "L 190.32061 139.019925 \n", "L 190.61813 137.381349 \n", "L 190.915651 140.128859 \n", "L 191.213171 139.109724 \n", "L 191.510692 140.532111 \n", "L 191.808213 139.975001 \n", "L 192.105733 140.809944 \n", "L 192.403254 140.471953 \n", "L 192.700775 141.150385 \n", "L 192.998295 140.745486 \n", "L 193.295816 141.569135 \n", "L 193.593337 140.598763 \n", "L 193.890857 141.815808 \n", "L 194.188378 138.584225 \n", "L 194.485899 139.40292 \n", "L 195.08094 115.833547 \n", "L 195.378461 57.316324 \n", "L 195.675981 40.706583 \n", "L 195.79888 -1 \n", "M 196.069787 -1 \n", "L 196.271023 122.853229 \n", "L 196.568543 63.487529 \n", "L 196.69124 -1 \n", "M 196.991778 -1 \n", "L 197.163585 124.574226 \n", "L 197.265993 -1 \n", "M 206.560474 -1 \n", "L 206.981767 22.949789 \n", "L 207.279287 30.627537 \n", "L 207.576808 24.67242 \n", "L 207.874329 11.880161 \n", "L 208.171849 17.641654 \n", "L 208.46937 32.891535 \n", "L 208.76689 35.516482 \n", "L 209.659452 12.273051 \n", "L 209.956973 9.763668 \n", "L 210.254494 10.611874 \n", "L 210.552014 14.65341 \n", "L 210.849535 21.862557 \n", "L 212.039618 62.76836 \n", "L 212.337138 62.340422 \n", "L 212.634659 59.449476 \n", "L 212.93218 61.045108 \n", "L 213.527221 71.626249 \n", "L 213.824742 71.224422 \n", "L 214.419783 63.794354 \n", "L 214.717304 62.728425 \n", "L 215.014824 65.433876 \n", "L 215.907386 83.668032 \n", "L 216.204907 85.136152 \n", "L 216.502428 84.230879 \n", "L 216.799948 84.557792 \n", "L 217.39499 89.492451 \n", "L 217.69251 89.515968 \n", "L 218.287552 86.498347 \n", "L 218.585072 87.148448 \n", "L 219.477634 94.088531 \n", "L 219.775155 94.181765 \n", "L 220.072676 94.089841 \n", "L 220.667717 95.635075 \n", "L 220.965238 95.564913 \n", "L 221.560279 94.124604 \n", "L 221.8578 94.554297 \n", "L 222.452841 96.896314 \n", "L 222.750362 97.3831 \n", "L 223.047882 97.444082 \n", "L 223.642924 97.902814 \n", "L 223.940444 97.815709 \n", "L 224.535486 97.144102 \n", "L 224.833006 97.424925 \n", "L 225.428048 98.67612 \n", "L 226.023089 99.115586 \n", "L 226.61813 99.307995 \n", "L 227.213171 98.988331 \n", "L 227.510692 99.076489 \n", "L 228.700775 100.392162 \n", "L 229.295816 100.578252 \n", "L 229.890857 100.499261 \n", "L 230.188378 100.617768 \n", "L 231.378461 101.694335 \n", "L 231.973502 101.894303 \n", "L 232.568543 101.991088 \n", "L 233.163585 102.423697 \n", "L 234.056147 103.105942 \n", "L 235.54375 103.780722 \n", "L 236.733833 104.629886 \n", "L 237.923915 105.242942 \n", "L 239.411519 106.257879 \n", "L 240.601601 106.994491 \n", "L 242.089205 107.980253 \n", "L 243.576808 109.011971 \n", "L 249.824742 113.34293 \n", "L 260.833006 121.597511 \n", "L 268.271023 127.612934 \n", "L 277.494163 135.007247 \n", "L 281.659452 138.029553 \n", "L 285.527221 140.53604 \n", "L 289.097469 142.590174 \n", "L 294.15532 145.177171 \n", "L 302.188378 148.978493 \n", "L 317.956973 156.171035 \n", "L 330.750362 161.689061 \n", "L 341.163585 165.878677 \n", "L 346.518957 167.938454 \n", "L 346.816477 167.922294 \n", "L 347.113998 168.181374 \n", "L 347.411519 167.959987 \n", "L 347.709039 168.342212 \n", "L 348.00656 167.35944 \n", "L 348.304081 167.497225 \n", "L 348.601601 162.779952 \n", "L 348.899122 159.828219 \n", "L 349.196643 139.623072 \n", "L 349.494163 136.649199 \n", "L 349.791684 110.480091 \n", "L 350.089205 147.347909 \n", "L 350.386725 164.604875 \n", "L 350.684246 166.213002 \n", "L 350.981767 154.839964 \n", "L 351.279287 137.264414 \n", "L 351.576808 159.953875 \n", "L 351.874329 169.353389 \n", "L 352.171849 161.855589 \n", "L 352.46937 158.147845 \n", "L 352.76689 156.752965 \n", "L 353.064411 169.078554 \n", "L 353.361932 167.486459 \n", "L 353.659452 157.510975 \n", "L 354.254494 169.439691 \n", "L 354.552014 168.040767 \n", "L 354.849535 165.582099 \n", "L 355.147056 164.285461 \n", "L 355.444576 170.157618 \n", "L 355.742097 169.803832 \n", "L 356.039618 165.288889 \n", "L 356.634659 170.106215 \n", "L 356.93218 170.320406 \n", "L 357.527221 167.469772 \n", "L 357.824742 170.532242 \n", "L 358.122262 171.256332 \n", "L 358.419783 169.718853 \n", "L 358.717304 169.824683 \n", "L 359.014824 169.635852 \n", "L 359.312345 171.469868 \n", "L 359.609866 171.588848 \n", "L 359.907386 170.356182 \n", "L 360.204907 170.766022 \n", "L 360.502428 170.570563 \n", "L 360.799948 171.889098 \n", "L 361.097469 172.072016 \n", "L 361.39499 171.482687 \n", "L 361.69251 171.657883 \n", "L 361.990031 171.031097 \n", "L 362.287552 172.072097 \n", "L 362.585072 172.161834 \n", "L 362.882593 172.554259 \n", "L 363.180114 172.629476 \n", "L 363.477634 172.097727 \n", "L 363.775155 172.471838 \n", "L 364.072676 171.938665 \n", "L 364.370196 172.678793 \n", "L 364.667717 172.464724 \n", "L 364.965238 173.086203 \n", "L 365.262758 173.061086 \n", "L 365.8578 173.371934 \n", "L 366.15532 173.226817 \n", "L 366.452841 173.470973 \n", "L 366.750362 173.08376 \n", "L 367.047882 173.460233 \n", "L 367.345403 172.859544 \n", "L 367.642924 173.324689 \n", "L 367.940444 172.362188 \n", "L 368.237965 172.79109 \n", "L 368.535486 170.970716 \n", "L 368.833006 171.187412 \n", "L 369.130527 166.866552 \n", "L 369.428048 166.389007 \n", "L 369.725568 155.860342 \n", "L 369.725568 155.860342 \n", "\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;stroke-width:1.5;\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path clip-path=\"url(#pd4addc3cb9)\" d=\"M 80.188512 -1 \n", "L 80.535486 92.90091 \n", "L 80.833006 138.272007 \n", "L 81.130527 138.101253 \n", "L 81.725568 13.094851 \n", "L 81.778862 -1 \n", "M 99.384094 -1 \n", "L 100.171849 47.177174 \n", "L 101.361932 109.4931 \n", "L 102.254494 146.185837 \n", "L 103.147056 168.686055 \n", "L 103.742097 176.979909 \n", "L 104.039618 179.709917 \n", "L 104.337138 181.232545 \n", "L 104.634659 181.390851 \n", "L 104.93218 180.62005 \n", "L 105.2297 178.662093 \n", "L 105.824742 172.713881 \n", "L 106.419783 164.57647 \n", "L 108.204907 135.991653 \n", "L 108.799948 127.056169 \n", "L 110.287552 109.554946 \n", "L 110.882593 104.781537 \n", "L 111.477634 100.413304 \n", "L 112.965238 93.886146 \n", "L 113.262758 92.729913 \n", "L 113.8578 91.280491 \n", "L 114.15532 90.355467 \n", "L 114.750362 89.13936 \n", "L 115.345403 87.787749 \n", "L 116.833006 84.000615 \n", "L 117.428048 82.34843 \n", "L 119.213171 76.292708 \n", "L 120.403254 71.833738 \n", "L 122.485899 63.407647 \n", "L 123.378461 60.065218 \n", "L 124.568543 55.666811 \n", "L 127.246229 47.941799 \n", "L 127.54375 47.422234 \n", "L 127.841271 46.627678 \n", "L 128.138791 46.168946 \n", "L 128.436312 45.423546 \n", "L 128.733833 45.072156 \n", "L 129.031353 44.309502 \n", "L 129.328874 44.134698 \n", "L 129.626395 43.247948 \n", "L 129.923915 43.428193 \n", "L 130.221436 42.092863 \n", "L 130.518957 43.132087 \n", "L 130.816477 40.459997 \n", "L 131.113998 43.919825 \n", "L 131.411519 37.142841 \n", "L 131.709039 47.978763 \n", "L 132.00656 27.974806 \n", "L 132.304081 63.391824 \n", "L 132.598952 -1 \n", "M 132.603036 -1 \n", "L 132.899122 118.365651 \n", "L 133.072908 -1 \n", "M 133.299858 -1 \n", "L 133.494163 158.990503 \n", "L 133.686105 -1 \n", "M 133.924949 -1 \n", "L 134.089205 107.469437 \n", "L 134.386725 61.658798 \n", "L 134.565675 -1 \n", "M 134.741847 -1 \n", "L 134.981767 171.929134 \n", "L 135.238985 -1 \n", "M 135.385597 -1 \n", "L 135.874329 119.978915 \n", "L 136.1085 -1 \n", "M 136.230616 -1 \n", "L 136.46937 131.965845 \n", "L 136.76689 33.996156 \n", "L 137.064411 3.455285 \n", "L 137.361932 139.541954 \n", "L 137.651256 -1 \n", "M 137.67898 -1 \n", "L 137.956973 55.68308 \n", "L 138.254494 92.316759 \n", "L 138.522393 -1 \n", "M 138.578876 -1 \n", "L 138.849535 102.966374 \n", "L 139.147056 28.61362 \n", "L 139.444576 16.714524 \n", "L 139.742097 97.745587 \n", "L 140.018849 -1 \n", "M 140.068988 -1 \n", "L 140.337138 66.656652 \n", "L 140.93218 3.56372 \n", "L 141.2297 81.041141 \n", "L 141.501206 -1 \n", "M 141.563826 -1 \n", "L 141.824742 55.03306 \n", "L 142.419783 7.987846 \n", "L 142.717304 60.897116 \n", "L 142.968693 -1 \n", "M 143.061557 -1 \n", "L 143.312345 59.954569 \n", "L 143.609866 0.035296 \n", "L 143.907386 32.979275 \n", "L 144.204907 26.981956 \n", "L 144.502428 6.041971 \n", "L 144.799948 48.596679 \n", "L 145.060691 -1 \n", "M 145.129677 -1 \n", "L 145.39499 56.626316 \n", "L 145.657992 -1 \n", "M 145.728913 -1 \n", "L 145.990031 53.250487 \n", "L 146.287552 -0.614913 \n", "L 146.585072 44.878546 \n", "L 146.882593 10.148404 \n", "L 147.180114 36.634441 \n", "L 147.477634 20.10145 \n", "L 147.775155 30.497392 \n", "L 148.370196 26.200023 \n", "L 148.667717 35.288028 \n", "L 148.965238 22.182599 \n", "L 149.262758 43.186845 \n", "L 149.560279 15.886001 \n", "L 149.8578 55.416486 \n", "L 150.15532 2.77767 \n", "L 150.452841 78.895892 \n", "L 150.681431 -1 \n", "M 150.797823 -1 \n", "L 151.047882 125.93472 \n", "L 151.238329 -1 \n", "M 151.431261 -1 \n", "L 151.642924 174.934523 \n", "L 151.840789 -1 \n", "M 152.044075 -1 \n", "L 152.237965 164.785201 \n", "L 152.496908 -1 \n", "M 152.627965 -1 \n", "L 152.833006 53.760507 \n", "L 153.130527 89.313421 \n", "L 153.353836 -1 \n", "M 153.476811 -1 \n", "L 153.725568 152.107783 \n", "L 153.9865 -1 \n", "M 154.084392 -1 \n", "L 154.32061 81.72695 \n", "L 154.61813 64.668913 \n", "L 154.909869 -1 \n", "M 154.918633 -1 \n", "L 155.213171 127.531392 \n", "L 155.492829 -1 \n", "M 155.537521 -1 \n", "L 155.808213 81.831984 \n", "L 156.403254 12.013572 \n", "L 156.700775 104.529821 \n", "L 156.982747 -1 \n", "M 157.016937 -1 \n", "L 157.295816 86.053386 \n", "L 157.593337 26.98286 \n", "L 157.890857 33.478765 \n", "L 158.188378 72.122877 \n", "L 158.484759 -1 \n", "M 158.486814 -1 \n", "L 158.783419 90.110164 \n", "L 159.070646 -1 \n", "M 159.093066 -1 \n", "L 159.378461 75.848637 \n", "L 159.675981 14.730916 \n", "L 159.973502 49.562933 \n", "L 160.568543 26.443139 \n", "L 160.866064 60.763732 \n", "L 161.163585 8.57498 \n", "L 161.461105 80.674104 \n", "L 161.734417 -1 \n", "M 161.77769 -1 \n", "L 162.056147 104.665217 \n", "L 162.292353 -1 \n", "M 162.403407 -1 \n", "L 162.651188 135.637853 \n", "L 162.871719 -1 \n", "M 163.016976 -1 \n", "L 163.246229 159.191741 \n", "L 163.475086 -1 \n", "M 163.616448 -1 \n", "L 163.841271 147.635144 \n", "L 164.115972 -1 \n", "M 164.172706 -1 \n", "L 164.436312 94.966276 \n", "L 164.733833 46.542679 \n", "L 165.031353 38.683189 \n", "L 165.328874 102.335485 \n", "L 165.626395 5.284407 \n", "L 165.923915 125.876938 \n", "L 166.221436 8.14086 \n", "L 166.518957 103.8479 \n", "L 166.816477 41.219586 \n", "L 167.411519 80.85879 \n", "L 167.709039 29.421676 \n", "L 168.00656 105.679722 \n", "L 168.304081 16.556164 \n", "L 168.601601 107.396278 \n", "L 168.899122 22.120146 \n", "L 169.196643 93.084948 \n", "L 169.494163 37.064039 \n", "L 169.791684 75.12234 \n", "L 170.089205 52.705015 \n", "L 170.684246 65.966387 \n", "L 170.981767 48.297711 \n", "L 171.279287 78.972156 \n", "L 171.576808 35.434508 \n", "L 171.874329 97.931084 \n", "L 172.171849 14.135735 \n", "L 172.46937 133.866546 \n", "L 172.722379 -1 \n", "M 172.801135 -1 \n", "L 173.064411 181.411959 \n", "L 173.284394 -1 \n", "M 173.440244 -1 \n", "L 173.659452 178.972495 \n", "L 173.912589 -1 \n", "M 174.014291 -1 \n", "L 174.254494 131.241255 \n", "L 174.849535 25.139144 \n", "L 175.147056 173.984591 \n", "L 175.444576 -0.141911 \n", "L 175.742097 152.397955 \n", "L 176.039618 67.039741 \n", "L 176.337138 57.583984 \n", "L 176.634659 152.467429 \n", "L 176.93218 24.002743 \n", "L 177.2297 137.117246 \n", "L 177.527221 72.472066 \n", "L 177.824742 61.198959 \n", "L 178.122262 135.442272 \n", "L 178.419783 31.277664 \n", "L 178.717304 124.797037 \n", "L 179.014824 63.058673 \n", "L 179.312345 68.858306 \n", "L 179.609866 110.874305 \n", "L 179.907386 34.517624 \n", "L 180.204907 123.699693 \n", "L 180.502428 39.449245 \n", "L 180.799948 99.490089 \n", "L 181.097469 67.690652 \n", "L 181.39499 68.271603 \n", "L 181.69251 96.964735 \n", "L 181.990031 46.371156 \n", "L 182.287552 118.223519 \n", "L 182.585072 34.421663 \n", "L 182.882593 133.492948 \n", "L 183.180114 27.469943 \n", "L 183.477634 147.701567 \n", "L 183.775155 21.135315 \n", "L 184.072676 162.86516 \n", "L 184.370196 15.110873 \n", "L 184.667717 174.972034 \n", "L 184.965238 15.340084 \n", "L 185.262758 176.958079 \n", "L 185.560279 30.072898 \n", "L 185.8578 162.851936 \n", "L 186.15532 60.176246 \n", "L 186.452841 132.367058 \n", "L 186.750362 97.065381 \n", "L 187.047882 99.798169 \n", "L 187.345403 129.79369 \n", "L 187.642924 75.649992 \n", "L 187.940444 149.974719 \n", "L 188.237965 64.317942 \n", "L 188.535486 154.313217 \n", "L 188.833006 65.567841 \n", "L 189.130527 146.762671 \n", "L 189.428048 74.649352 \n", "L 189.725568 134.043598 \n", "L 190.023089 85.504431 \n", "L 190.32061 122.519902 \n", "L 190.61813 94.295304 \n", "L 190.915651 114.971246 \n", "L 191.213171 100.075333 \n", "L 191.510692 111.438299 \n", "L 191.808213 103.439957 \n", "L 192.105733 111.161527 \n", "L 192.403254 105.148601 \n", "L 192.700775 113.724857 \n", "L 192.998295 105.068962 \n", "L 193.295816 120.009331 \n", "L 193.593337 101.011441 \n", "L 193.890857 134.088224 \n", "L 194.188378 85.674226 \n", "L 194.485899 167.528675 \n", "L 194.783419 39.848764 \n", "L 195.08094 180.55518 \n", "L 195.324511 -1 \n", "M 195.547077 -1 \n", "L 195.675981 29.742919 \n", "L 195.973502 63.253688 \n", "L 196.263308 -1 \n", "M 196.615122 -1 \n", "L 196.866064 190.08898 \n", "L 196.989209 -1 \n", "M 198.272763 -1 \n", "L 198.353667 108.232739 \n", "L 198.651188 11.593249 \n", "L 198.948709 39.830903 \n", "L 199.246229 138.774345 \n", "L 199.371903 -1 \n", "M 201.258989 -1 \n", "L 201.328874 167.383619 \n", "L 201.396546 -1 \n", "M 205.989136 -1 \n", "L 206.981767 112.671984 \n", "L 207.279287 151.026404 \n", "L 207.576808 161.255237 \n", "L 207.874329 160.307735 \n", "L 208.171849 147.574224 \n", "L 209.064411 57.343905 \n", "L 209.361932 45.231151 \n", "L 209.659452 44.593579 \n", "L 209.956973 53.681287 \n", "L 210.254494 70.961892 \n", "L 211.147056 143.859554 \n", "L 211.444576 151.981183 \n", "L 211.742097 136.10883 \n", "L 212.337138 78.395228 \n", "L 212.634659 69.798342 \n", "L 212.93218 78.01712 \n", "L 213.824742 125.719226 \n", "L 214.122262 130.415811 \n", "L 214.419783 128.799185 \n", "L 214.717304 120.207734 \n", "L 215.014824 102.26836 \n", "L 215.609866 40.550234 \n", "L 215.986674 -1 \n", "M 217.568811 -1 \n", "L 217.69251 3.276944 \n", "L 217.990031 5.416414 \n", "L 218.235417 -1 \n", "M 279.239757 -1 \n", "L 286.122262 22.308491 \n", "L 291.775155 42.07268 \n", "L 298.023089 64.103838 \n", "L 303.378461 82.810537 \n", "L 311.113998 109.124173 \n", "L 318.254494 132.265845 \n", "L 323.609866 148.604308 \n", "L 327.775155 160.49203 \n", "L 330.15532 166.806914 \n", "L 332.833006 173.419131 \n", "L 336.105733 180.774846 \n", "L 338.783419 185.976496 \n", "L 340.271023 188.561836 \n", "L 342.353667 191.785278 \n", "L 344.733833 194.878633 \n", "L 345.031353 195.057737 \n", "L 345.328874 195.566606 \n", "L 345.626395 195.605423 \n", "L 345.923915 196.254626 \n", "L 346.221436 196.017626 \n", "L 346.518957 197.029866 \n", "L 346.816477 196.169131 \n", "L 347.113998 197.976724 \n", "L 347.411519 195.625252 \n", "L 347.709039 198.993878 \n", "L 348.00656 192.597097 \n", "L 348.304081 197.439156 \n", "L 348.601601 179.997989 \n", "L 348.899122 176.909761 \n", "L 349.196643 153.04194 \n", "L 349.494163 147.123214 \n", "L 349.791684 164.576855 \n", "L 350.089205 198.261975 \n", "L 350.386725 199.116391 \n", "L 350.684246 173.324 \n", "L 350.981767 174.471491 \n", "L 351.279287 180.493409 \n", "L 351.576808 199.061819 \n", "L 351.874329 196.074729 \n", "L 352.171849 179.175683 \n", "L 352.46937 191.057941 \n", "L 352.76689 196.451186 \n", "L 353.361932 190.266349 \n", "L 353.659452 190.089135 \n", "L 353.956973 199.738043 \n", "L 354.254494 198.666114 \n", "L 354.552014 192.480973 \n", "L 354.849535 194.526379 \n", "L 355.147056 198.917786 \n", "L 355.444576 198.439229 \n", "L 355.742097 194.532964 \n", "L 356.039618 196.413294 \n", "L 356.337138 199.133878 \n", "L 356.634659 199.205332 \n", "L 356.93218 197.744327 \n", "L 357.2297 195.583417 \n", "L 357.527221 199.285805 \n", "L 357.824742 199.85522 \n", "L 358.122262 197.551854 \n", "L 358.419783 198.660457 \n", "L 358.717304 196.862504 \n", "L 359.014824 199.819704 \n", "L 359.312345 199.78598 \n", "L 359.609866 196.844383 \n", "L 359.907386 199.482058 \n", "L 360.204907 196.363163 \n", "L 360.502428 199.569588 \n", "L 360.799948 199.012401 \n", "L 361.097469 196.697036 \n", "L 361.39499 199.606117 \n", "L 361.69251 194.709766 \n", "L 361.990031 199.287935 \n", "L 362.287552 196.360807 \n", "L 362.882593 198.116335 \n", "L 363.180114 194.466778 \n", "L 363.477634 198.6009 \n", "L 363.775155 192.924689 \n", "L 364.072676 198.18753 \n", "L 364.370196 193.315392 \n", "L 364.667717 196.986874 \n", "L 364.965238 194.427985 \n", "L 365.262758 195.152476 \n", "L 365.560279 195.345955 \n", "L 365.8578 193.170023 \n", "L 366.15532 195.884359 \n", "L 366.452841 191.252003 \n", "L 366.750362 196.173799 \n", "L 367.047882 189.228552 \n", "L 367.345403 196.499619 \n", "L 367.642924 186.523531 \n", "L 367.940444 197.131032 \n", "L 368.237965 182.083607 \n", "L 368.535486 198.208273 \n", "L 368.833006 173.787106 \n", "L 369.130527 198.826682 \n", "L 369.428048 158.201725 \n", "L 369.725568 196.069035 \n", "L 369.725568 196.069035 \n", "\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;stroke-width:1.5;\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 50.14375 239.758125 \n", "L 50.14375 22.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 384.94375 239.758125 \n", "L 384.94375 22.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 50.14375 239.758125 \n", "L 384.94375 239.758125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 50.14375 22.318125 \n", "L 384.94375 22.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"text_16\">\n", "    <!-- Training and validation mae -->\n", "    <defs>\n", "     <path d=\"M -0.296875 72.90625 \n", "L 61.375 72.90625 \n", "L 61.375 64.59375 \n", "L 35.5 64.59375 \n", "L 35.5 0 \n", "L 25.59375 0 \n", "L 25.59375 64.59375 \n", "L -0.296875 64.59375 \n", "z\n", "\" id=\"DejaVuSans-84\"/>\n", "     <path d=\"M 41.109375 46.296875 \n", "Q 39.59375 47.171875 37.8125 47.578125 \n", "Q 36.03125 48 33.890625 48 \n", "Q 26.265625 48 22.1875 43.046875 \n", "Q 18.109375 38.09375 18.109375 28.8125 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 20.953125 51.171875 25.484375 53.578125 \n", "Q 30.03125 56 36.53125 56 \n", "Q 37.453125 56 38.578125 55.875 \n", "Q 39.703125 55.765625 41.0625 55.515625 \n", "z\n", "\" id=\"DejaVuSans-114\"/>\n", "     <path d=\"M 9.421875 54.6875 \n", "L 18.40625 54.6875 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 64.59375 \n", "L 9.421875 64.59375 \n", "z\n", "\" id=\"DejaVuSans-105\"/>\n", "     <path d=\"M 54.890625 33.015625 \n", "L 54.890625 0 \n", "L 45.90625 0 \n", "L 45.90625 32.71875 \n", "Q 45.90625 40.484375 42.875 44.328125 \n", "Q 39.84375 48.1875 33.796875 48.1875 \n", "Q 26.515625 48.1875 22.3125 43.546875 \n", "Q 18.109375 38.921875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.34375 51.125 25.703125 53.5625 \n", "Q 30.078125 56 35.796875 56 \n", "Q 45.21875 56 50.046875 50.171875 \n", "Q 54.890625 44.34375 54.890625 33.015625 \n", "z\n", "\" id=\"DejaVuSans-110\"/>\n", "     <path d=\"M 45.40625 27.984375 \n", "Q 45.40625 37.75 41.375 43.109375 \n", "Q 37.359375 48.484375 30.078125 48.484375 \n", "Q 22.859375 48.484375 18.828125 43.109375 \n", "Q 14.796875 37.75 14.796875 27.984375 \n", "Q 14.796875 18.265625 18.828125 12.890625 \n", "Q 22.859375 7.515625 30.078125 7.515625 \n", "Q 37.359375 7.515625 41.375 12.890625 \n", "Q 45.40625 18.265625 45.40625 27.984375 \n", "z\n", "M 54.390625 6.78125 \n", "Q 54.390625 -7.171875 48.1875 -13.984375 \n", "Q 42 -20.796875 29.203125 -20.796875 \n", "Q 24.46875 -20.796875 20.265625 -20.09375 \n", "Q 16.0625 -19.390625 12.109375 -17.921875 \n", "L 12.109375 -9.1875 \n", "Q 16.0625 -11.328125 19.921875 -12.34375 \n", "Q 23.78125 -13.375 27.78125 -13.375 \n", "Q 36.625 -13.375 41.015625 -8.765625 \n", "Q 45.40625 -4.15625 45.40625 5.171875 \n", "L 45.40625 9.625 \n", "Q 42.625 4.78125 38.28125 2.390625 \n", "Q 33.9375 0 27.875 0 \n", "Q 17.828125 0 11.671875 7.65625 \n", "Q 5.515625 15.328125 5.515625 27.984375 \n", "Q 5.515625 40.671875 11.671875 48.328125 \n", "Q 17.828125 56 27.875 56 \n", "Q 33.9375 56 38.28125 53.609375 \n", "Q 42.625 51.21875 45.40625 46.390625 \n", "L 45.40625 54.6875 \n", "L 54.390625 54.6875 \n", "z\n", "\" id=\"DejaVuSans-103\"/>\n", "     <path id=\"DejaVuSans-32\"/>\n", "     <path d=\"M 45.40625 46.390625 \n", "L 45.40625 75.984375 \n", "L 54.390625 75.984375 \n", "L 54.390625 0 \n", "L 45.40625 0 \n", "L 45.40625 8.203125 \n", "Q 42.578125 3.328125 38.25 0.953125 \n", "Q 33.9375 -1.421875 27.875 -1.421875 \n", "Q 17.96875 -1.421875 11.734375 6.484375 \n", "Q 5.515625 14.40625 5.515625 27.296875 \n", "Q 5.515625 40.1875 11.734375 48.09375 \n", "Q 17.96875 56 27.875 56 \n", "Q 33.9375 56 38.25 53.625 \n", "Q 42.578125 51.265625 45.40625 46.390625 \n", "z\n", "M 14.796875 27.296875 \n", "Q 14.796875 17.390625 18.875 11.75 \n", "Q 22.953125 6.109375 30.078125 6.109375 \n", "Q 37.203125 6.109375 41.296875 11.75 \n", "Q 45.40625 17.390625 45.40625 27.296875 \n", "Q 45.40625 37.203125 41.296875 42.84375 \n", "Q 37.203125 48.484375 30.078125 48.484375 \n", "Q 22.953125 48.484375 18.875 42.84375 \n", "Q 14.796875 37.203125 14.796875 27.296875 \n", "z\n", "\" id=\"DejaVuSans-100\"/>\n", "     <path d=\"M 2.984375 54.6875 \n", "L 12.5 54.6875 \n", "L 29.59375 8.796875 \n", "L 46.6875 54.6875 \n", "L 56.203125 54.6875 \n", "L 35.6875 0 \n", "L 23.484375 0 \n", "z\n", "\" id=\"DejaVuSans-118\"/>\n", "     <path d=\"M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "\" id=\"DejaVuSans-108\"/>\n", "     <path d=\"M 18.3125 70.21875 \n", "L 18.3125 54.6875 \n", "L 36.8125 54.6875 \n", "L 36.8125 47.703125 \n", "L 18.3125 47.703125 \n", "L 18.3125 18.015625 \n", "Q 18.3125 11.328125 20.140625 9.421875 \n", "Q 21.96875 7.515625 27.59375 7.515625 \n", "L 36.8125 7.515625 \n", "L 36.8125 0 \n", "L 27.59375 0 \n", "Q 17.1875 0 13.234375 3.875 \n", "Q 9.28125 7.765625 9.28125 18.015625 \n", "L 9.28125 47.703125 \n", "L 2.6875 47.703125 \n", "L 2.6875 54.6875 \n", "L 9.28125 54.6875 \n", "L 9.28125 70.21875 \n", "z\n", "\" id=\"DejaVuSans-116\"/>\n", "    </defs>\n", "    <g transform=\"translate(134.11 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-84\"/>\n", "     <use x=\"46.333984\" xlink:href=\"#DejaVuSans-114\"/>\n", "     <use x=\"87.447266\" xlink:href=\"#DejaVuSans-97\"/>\n", "     <use x=\"148.726562\" xlink:href=\"#DejaVuSans-105\"/>\n", "     <use x=\"176.509766\" xlink:href=\"#DejaVuSans-110\"/>\n", "     <use x=\"239.888672\" xlink:href=\"#DejaVuSans-105\"/>\n", "     <use x=\"267.671875\" xlink:href=\"#DejaVuSans-110\"/>\n", "     <use x=\"331.050781\" xlink:href=\"#DejaVuSans-103\"/>\n", "     <use x=\"394.527344\" xlink:href=\"#DejaVuSans-32\"/>\n", "     <use x=\"426.314453\" xlink:href=\"#DejaVuSans-97\"/>\n", "     <use x=\"487.59375\" xlink:href=\"#DejaVuSans-110\"/>\n", "     <use x=\"550.972656\" xlink:href=\"#DejaVuSans-100\"/>\n", "     <use x=\"614.449219\" xlink:href=\"#DejaVuSans-32\"/>\n", "     <use x=\"646.236328\" xlink:href=\"#DejaVuSans-118\"/>\n", "     <use x=\"705.416016\" xlink:href=\"#DejaVuSans-97\"/>\n", "     <use x=\"766.695312\" xlink:href=\"#DejaVuSans-108\"/>\n", "     <use x=\"794.478516\" xlink:href=\"#DejaVuSans-105\"/>\n", "     <use x=\"822.261719\" xlink:href=\"#DejaVuSans-100\"/>\n", "     <use x=\"885.738281\" xlink:href=\"#DejaVuSans-97\"/>\n", "     <use x=\"947.017578\" xlink:href=\"#DejaVuSans-116\"/>\n", "     <use x=\"986.226562\" xlink:href=\"#DejaVuSans-105\"/>\n", "     <use x=\"1014.009766\" xlink:href=\"#DejaVuSans-111\"/>\n", "     <use x=\"1075.191406\" xlink:href=\"#DejaVuSans-110\"/>\n", "     <use x=\"1138.570312\" xlink:href=\"#DejaVuSans-32\"/>\n", "     <use x=\"1170.357422\" xlink:href=\"#DejaVuSans-109\"/>\n", "     <use x=\"1267.769531\" xlink:href=\"#DejaVuSans-97\"/>\n", "     <use x=\"1329.048828\" xlink:href=\"#DejaVuSans-101\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 57.14375 234.758125 \n", "L 163.725 234.758125 \n", "Q 165.725 234.758125 165.725 232.758125 \n", "L 165.725 204.401875 \n", "Q 165.725 202.401875 163.725 202.401875 \n", "L 57.14375 202.401875 \n", "Q 55.14375 202.401875 55.14375 204.401875 \n", "L 55.14375 232.758125 \n", "Q 55.14375 234.758125 57.14375 234.758125 \n", "z\n", "\" style=\"fill:#ffffff;opacity:0.8;stroke:#cccccc;stroke-linejoin:miter;\"/>\n", "    </g>\n", "    <g id=\"line2d_16\">\n", "     <path d=\"M 59.14375 210.500312 \n", "L 79.14375 210.500312 \n", "\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;stroke-width:1.5;\"/>\n", "    </g>\n", "    <g id=\"line2d_17\"/>\n", "    <g id=\"text_17\">\n", "     <!-- Training mae -->\n", "     <g transform=\"translate(87.14375 214.000312)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-84\"/>\n", "      <use x=\"46.333984\" xlink:href=\"#DejaVuSans-114\"/>\n", "      <use x=\"87.447266\" xlink:href=\"#DejaVuSans-97\"/>\n", "      <use x=\"148.726562\" xlink:href=\"#DejaVuSans-105\"/>\n", "      <use x=\"176.509766\" xlink:href=\"#DejaVuSans-110\"/>\n", "      <use x=\"239.888672\" xlink:href=\"#DejaVuSans-105\"/>\n", "      <use x=\"267.671875\" xlink:href=\"#DejaVuSans-110\"/>\n", "      <use x=\"331.050781\" xlink:href=\"#DejaVuSans-103\"/>\n", "      <use x=\"394.527344\" xlink:href=\"#DejaVuSans-32\"/>\n", "      <use x=\"426.314453\" xlink:href=\"#DejaVuSans-109\"/>\n", "      <use x=\"523.726562\" xlink:href=\"#DejaVuSans-97\"/>\n", "      <use x=\"585.005859\" xlink:href=\"#DejaVuSans-101\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_18\">\n", "     <path d=\"M 59.14375 225.178437 \n", "L 79.14375 225.178437 \n", "\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;stroke-width:1.5;\"/>\n", "    </g>\n", "    <g id=\"line2d_19\"/>\n", "    <g id=\"text_18\">\n", "     <!-- Validation mae -->\n", "     <defs>\n", "      <path d=\"M 28.609375 0 \n", "L 0.78125 72.90625 \n", "L 11.078125 72.90625 \n", "L 34.1875 11.53125 \n", "L 57.328125 72.90625 \n", "L 67.578125 72.90625 \n", "L 39.796875 0 \n", "z\n", "\" id=\"DejaVuSans-86\"/>\n", "     </defs>\n", "     <g transform=\"translate(87.14375 228.678437)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-86\"/>\n", "      <use x=\"60.658203\" xlink:href=\"#DejaVuSans-97\"/>\n", "      <use x=\"121.9375\" xlink:href=\"#DejaVuSans-108\"/>\n", "      <use x=\"149.720703\" xlink:href=\"#DejaVuSans-105\"/>\n", "      <use x=\"177.503906\" xlink:href=\"#DejaVuSans-100\"/>\n", "      <use x=\"240.980469\" xlink:href=\"#DejaVuSans-97\"/>\n", "      <use x=\"302.259766\" xlink:href=\"#DejaVuSans-116\"/>\n", "      <use x=\"341.46875\" xlink:href=\"#DejaVuSans-105\"/>\n", "      <use x=\"369.251953\" xlink:href=\"#DejaVuSans-111\"/>\n", "      <use x=\"430.433594\" xlink:href=\"#DejaVuSans-110\"/>\n", "      <use x=\"493.8125\" xlink:href=\"#DejaVuSans-32\"/>\n", "      <use x=\"525.599609\" xlink:href=\"#DejaVuSans-109\"/>\n", "      <use x=\"623.011719\" xlink:href=\"#DejaVuSans-97\"/>\n", "      <use x=\"684.291016\" xlink:href=\"#DejaVuSans-101\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pd4addc3cb9\">\n", "   <rect height=\"217.44\" width=\"334.8\" x=\"50.14375\" y=\"22.318125\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# get the history of the fitting process\n", "history_dict = history.history\n", "loss_values = history_dict['loss']\n", "val_loss_values = history_dict['val_loss']\n", "mae_values = history_dict['mae']\n", "val_mae_values = history_dict['val_mae']\n", "epochs = range(1, len(history_dict['mae']) + 1)\n", "# plot the training and validation losses\n", "plt.plot(epochs, loss_values, label = 'Training loss', color='blue')\n", "plt.plot(epochs, val_loss_values, label = 'Validation loss', color='red')\n", "plt.ylim(0,0.03)\n", "plt.title('Training and validation loss')\n", "plt.xlabel('Epochs')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.show()\n", "# plot the training and validation mae (mean absolute error)\n", "plt.plot(epochs, mae_values, label = 'Training mae', color='blue')\n", "plt.plot(epochs, val_mae_values, label = 'Validation mae', color='red')\n", "plt.ylim(0,0.06)\n", "plt.title('Training and validation mae')\n", "plt.xlabel('Epochs')\n", "plt.ylabel('mae')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["50/50 [==============================] - 0s 2ms/step\n"]}], "source": ["# predict the model on the test set\n", "yhat = model.predict(x_test, verbose=1)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# convert the predictions series\n", "predictions = pd.Series(yhat.reshape((50)), index=df['GBP'].index[-50:])\n", "# extract the actual values\n", "actuals = df['GBP'][-50:]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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********************************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", "image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Created with matplotlib (https://matplotlib.org/) -->\n", "<svg height=\"351.315625pt\" version=\"1.1\" viewBox=\"0 0 1159.665625 351.315625\" width=\"1159.665625pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", " <defs>\n", "  <style type=\"text/css\">\n", "*{stroke-linecap:butt;stroke-linejoin:round;}\n", "  </style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 351.315625 \n", "L 1159.665625 351.315625 \n", "L 1159.665625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill:none;\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 36.465625 279 \n", "L 1152.465625 279 \n", "L 1152.465625 7.2 \n", "L 36.465625 7.2 \n", "z\n", "\" style=\"fill:#ffffff;\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 87.192898 279 \n", "L 87.192898 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 0 3.5 \n", "\" id=\"ma7a730e8b6\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"87.192898\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 2020-06-12 -->\n", "      <defs>\n", "       <path d=\"M 19.1875 8.296875 \n", "L 53.609375 8.296875 \n", "L 53.609375 0 \n", "L 7.328125 0 \n", "L 7.328125 8.296875 \n", "Q 12.9375 14.109375 22.625 23.890625 \n", "Q 32.328125 33.6875 34.8125 36.53125 \n", "Q 39.546875 41.84375 41.421875 45.53125 \n", "Q 43.3125 49.21875 43.3125 52.78125 \n", "Q 43.3125 58.59375 39.234375 62.25 \n", "Q 35.15625 65.921875 28.609375 65.921875 \n", "Q 23.96875 65.921875 18.8125 64.3125 \n", "Q 13.671875 62.703125 7.8125 59.421875 \n", "L 7.8125 69.390625 \n", "Q 13.765625 71.78125 18.9375 73 \n", "Q 24.125 74.21875 28.421875 74.21875 \n", "Q 39.75 74.21875 46.484375 68.546875 \n", "Q 53.21875 62.890625 53.21875 53.421875 \n", "Q 53.21875 48.921875 51.53125 44.890625 \n", "Q 49.859375 40.875 45.40625 35.40625 \n", "Q 44.1875 33.984375 37.640625 27.21875 \n", "Q 31.109375 20.453125 19.1875 8.296875 \n", "z\n", "\" id=\"DejaVuSans-50\"/>\n", "       <path d=\"M 31.78125 66.40625 \n", "Q 24.171875 66.40625 20.328125 58.90625 \n", "Q 16.5 51.421875 16.5 36.375 \n", "Q 16.5 21.390625 20.328125 13.890625 \n", "Q 24.171875 6.390625 31.78125 6.390625 \n", "Q 39.453125 6.390625 43.28125 13.890625 \n", "Q 47.125 21.390625 47.125 36.375 \n", "Q 47.125 51.421875 43.28125 58.90625 \n", "Q 39.453125 66.40625 31.78125 66.40625 \n", "z\n", "M 31.78125 74.21875 \n", "Q 44.046875 74.21875 50.515625 64.515625 \n", "Q 56.984375 54.828125 56.984375 36.375 \n", "Q 56.984375 17.96875 50.515625 8.265625 \n", "Q 44.046875 -1.421875 31.78125 -1.421875 \n", "Q 19.53125 -1.421875 13.0625 8.265625 \n", "Q 6.59375 17.96875 6.59375 36.375 \n", "Q 6.59375 54.828125 13.0625 64.515625 \n", "Q 19.53125 74.21875 31.78125 74.21875 \n", "z\n", "\" id=\"DejaVuSans-48\"/>\n", "       <path d=\"M 4.890625 31.390625 \n", "L 31.203125 31.390625 \n", "L 31.203125 23.390625 \n", "L 4.890625 23.390625 \n", "z\n", "\" id=\"DejaVuSans-45\"/>\n", "       <path d=\"M 33.015625 40.375 \n", "Q 26.375 40.375 22.484375 35.828125 \n", "Q 18.609375 31.296875 18.609375 23.390625 \n", "Q 18.609375 15.53125 22.484375 10.953125 \n", "Q 26.375 6.390625 33.015625 6.390625 \n", "Q 39.65625 6.390625 43.53125 10.953125 \n", "Q 47.40625 15.53125 47.40625 23.390625 \n", "Q 47.40625 31.296875 43.53125 35.828125 \n", "Q 39.65625 40.375 33.015625 40.375 \n", "z\n", "M 52.59375 71.296875 \n", "L 52.59375 62.3125 \n", "Q 48.875 64.0625 45.09375 64.984375 \n", "Q 41.3125 65.921875 37.59375 65.921875 \n", "Q 27.828125 65.921875 22.671875 59.328125 \n", "Q 17.53125 52.734375 16.796875 39.40625 \n", "Q 19.671875 43.65625 24.015625 45.921875 \n", "Q 28.375 48.1875 33.59375 48.1875 \n", "Q 44.578125 48.1875 50.953125 41.515625 \n", "Q 57.328125 34.859375 57.328125 23.390625 \n", "Q 57.328125 12.15625 50.6875 5.359375 \n", "Q 44.046875 -1.421875 33.015625 -1.421875 \n", "Q 20.359375 -1.421875 13.671875 8.265625 \n", "Q 6.984375 17.96875 6.984375 36.375 \n", "Q 6.984375 53.65625 15.1875 63.9375 \n", "Q 23.390625 74.21875 37.203125 74.21875 \n", "Q 40.921875 74.21875 44.703125 73.484375 \n", "Q 48.484375 72.75 52.59375 71.296875 \n", "z\n", "\" id=\"DejaVuSans-54\"/>\n", "       <path d=\"M 12.40625 8.296875 \n", "L 28.515625 8.296875 \n", "L 28.515625 63.921875 \n", "L 10.984375 60.40625 \n", "L 10.984375 69.390625 \n", "L 28.421875 72.90625 \n", "L 38.28125 72.90625 \n", "L 38.28125 8.296875 \n", "L 54.390625 8.296875 \n", "L 54.390625 0 \n", "L 12.40625 0 \n", "z\n", "\" id=\"DejaVuSans-49\"/>\n", "      </defs>\n", "      <g transform=\"translate(89.952273 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-50\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 130.673417 279 \n", "L 130.673417 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"130.673417\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2020-06-15 -->\n", "      <defs>\n", "       <path d=\"M 10.796875 72.90625 \n", "L 49.515625 72.90625 \n", "L 49.515625 64.59375 \n", "L 19.828125 64.59375 \n", "L 19.828125 46.734375 \n", "Q 21.96875 47.46875 24.109375 47.828125 \n", "Q 26.265625 48.1875 28.421875 48.1875 \n", "Q 40.625 48.1875 47.75 41.5 \n", "Q 54.890625 34.8125 54.890625 23.390625 \n", "Q 54.890625 11.625 47.5625 5.09375 \n", "Q 40.234375 -1.421875 26.90625 -1.421875 \n", "Q 22.3125 -1.421875 17.546875 -0.640625 \n", "Q 12.796875 0.140625 7.71875 1.703125 \n", "L 7.71875 11.625 \n", "Q 12.109375 9.234375 16.796875 8.0625 \n", "Q 21.484375 6.890625 26.703125 6.890625 \n", "Q 35.15625 6.890625 40.078125 11.328125 \n", "Q 45.015625 15.765625 45.015625 23.390625 \n", "Q 45.015625 31 40.078125 35.4375 \n", "Q 35.15625 39.890625 26.703125 39.890625 \n", "Q 22.75 39.890625 18.8125 39.015625 \n", "Q 14.890625 38.140625 10.796875 36.28125 \n", "z\n", "\" id=\"DejaVuSans-53\"/>\n", "      </defs>\n", "      <g transform=\"translate(133.432792 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-53\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 145.166924 279 \n", "L 145.166924 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"145.166924\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2020-06-16 -->\n", "      <g transform=\"translate(147.926299 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-54\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 159.66043 279 \n", "L 159.66043 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"159.66043\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 2020-06-17 -->\n", "      <defs>\n", "       <path d=\"M 8.203125 72.90625 \n", "L 55.078125 72.90625 \n", "L 55.078125 68.703125 \n", "L 28.609375 0 \n", "L 18.3125 0 \n", "L 43.21875 64.59375 \n", "L 8.203125 64.59375 \n", "z\n", "\" id=\"DejaVuSans-55\"/>\n", "      </defs>\n", "      <g transform=\"translate(162.419805 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-55\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 174.153937 279 \n", "L 174.153937 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"174.153937\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2020-06-18 -->\n", "      <defs>\n", "       <path d=\"M 31.78125 34.625 \n", "Q 24.75 34.625 20.71875 30.859375 \n", "Q 16.703125 27.09375 16.703125 20.515625 \n", "Q 16.703125 13.921875 20.71875 10.15625 \n", "Q 24.75 6.390625 31.78125 6.390625 \n", "Q 38.8125 6.390625 42.859375 10.171875 \n", "Q 46.921875 13.96875 46.921875 20.515625 \n", "Q 46.921875 27.09375 42.890625 30.859375 \n", "Q 38.875 34.625 31.78125 34.625 \n", "z\n", "M 21.921875 38.8125 \n", "Q 15.578125 40.375 12.03125 44.71875 \n", "Q 8.5 49.078125 8.5 55.328125 \n", "Q 8.5 64.0625 14.71875 69.140625 \n", "Q 20.953125 74.21875 31.78125 74.21875 \n", "Q 42.671875 74.21875 48.875 69.140625 \n", "Q 55.078125 64.0625 55.078125 55.328125 \n", "Q 55.078125 49.078125 51.53125 44.71875 \n", "Q 48 40.375 41.703125 38.8125 \n", "Q 48.828125 37.15625 52.796875 32.3125 \n", "Q 56.78125 27.484375 56.78125 20.515625 \n", "Q 56.78125 9.90625 50.3125 4.234375 \n", "Q 43.84375 -1.421875 31.78125 -1.421875 \n", "Q 19.734375 -1.421875 13.25 4.234375 \n", "Q 6.78125 9.90625 6.78125 20.515625 \n", "Q 6.78125 27.484375 10.78125 32.3125 \n", "Q 14.796875 37.15625 21.921875 38.8125 \n", "z\n", "M 18.3125 54.390625 \n", "Q 18.3125 48.734375 21.84375 45.5625 \n", "Q 25.390625 42.390625 31.78125 42.390625 \n", "Q 38.140625 42.390625 41.71875 45.5625 \n", "Q 45.3125 48.734375 45.3125 54.390625 \n", "Q 45.3125 60.0625 41.71875 63.234375 \n", "Q 38.140625 66.40625 31.78125 66.40625 \n", "Q 25.390625 66.40625 21.84375 63.234375 \n", "Q 18.3125 60.0625 18.3125 54.390625 \n", "z\n", "\" id=\"DejaVuSans-56\"/>\n", "      </defs>\n", "      <g transform=\"translate(176.913312 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-56\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 188.647443 279 \n", "L 188.647443 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"188.647443\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 2020-06-19 -->\n", "      <defs>\n", "       <path d=\"M 10.984375 1.515625 \n", "L 10.984375 10.5 \n", "Q 14.703125 8.734375 18.5 7.8125 \n", "Q 22.3125 6.890625 25.984375 6.890625 \n", "Q 35.75 6.890625 40.890625 13.453125 \n", "Q 46.046875 20.015625 46.78125 33.40625 \n", "Q 43.953125 29.203125 39.59375 26.953125 \n", "Q 35.25 24.703125 29.984375 24.703125 \n", "Q 19.046875 24.703125 12.671875 31.3125 \n", "Q 6.296875 37.9375 6.296875 49.421875 \n", "Q 6.296875 60.640625 12.9375 67.421875 \n", "Q 19.578125 74.21875 30.609375 74.21875 \n", "Q 43.265625 74.21875 49.921875 64.515625 \n", "Q 56.59375 54.828125 56.59375 36.375 \n", "Q 56.59375 19.140625 48.40625 8.859375 \n", "Q 40.234375 -1.421875 26.421875 -1.421875 \n", "Q 22.703125 -1.421875 18.890625 -0.6875 \n", "Q 15.09375 0.046875 10.984375 1.515625 \n", "z\n", "M 30.609375 32.421875 \n", "Q 37.25 32.421875 41.125 36.953125 \n", "Q 45.015625 41.5 45.015625 49.421875 \n", "Q 45.015625 57.28125 41.125 61.84375 \n", "Q 37.25 66.40625 30.609375 66.40625 \n", "Q 23.96875 66.40625 20.09375 61.84375 \n", "Q 16.21875 57.28125 16.21875 49.421875 \n", "Q 16.21875 41.5 20.09375 36.953125 \n", "Q 23.96875 32.421875 30.609375 32.421875 \n", "z\n", "\" id=\"DejaVuSans-57\"/>\n", "      </defs>\n", "      <g transform=\"translate(191.406818 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-57\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 232.127963 279 \n", "L 232.127963 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"232.127963\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 2020-06-22 -->\n", "      <g transform=\"translate(234.887338 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-50\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_15\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 246.621469 279 \n", "L 246.621469 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"246.621469\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 2020-06-23 -->\n", "      <defs>\n", "       <path d=\"M 40.578125 39.3125 \n", "Q 47.65625 37.796875 51.625 33 \n", "Q 55.609375 28.21875 55.609375 21.1875 \n", "Q 55.609375 10.40625 48.1875 4.484375 \n", "Q 40.765625 -1.421875 27.09375 -1.421875 \n", "Q 22.515625 -1.421875 17.65625 -0.515625 \n", "Q 12.796875 0.390625 7.625 2.203125 \n", "L 7.625 11.71875 \n", "Q 11.71875 9.328125 16.59375 8.109375 \n", "Q 21.484375 6.890625 26.8125 6.890625 \n", "Q 36.078125 6.890625 40.9375 10.546875 \n", "Q 45.796875 14.203125 45.796875 21.1875 \n", "Q 45.796875 27.640625 41.28125 31.265625 \n", "Q 36.765625 34.90625 28.71875 34.90625 \n", "L 20.21875 34.90625 \n", "L 20.21875 43.015625 \n", "L 29.109375 43.015625 \n", "Q 36.375 43.015625 40.234375 45.921875 \n", "Q 44.09375 48.828125 44.09375 54.296875 \n", "Q 44.09375 59.90625 40.109375 62.90625 \n", "Q 36.140625 65.921875 28.71875 65.921875 \n", "Q 24.65625 65.921875 20.015625 65.03125 \n", "Q 15.375 64.15625 9.8125 62.3125 \n", "L 9.8125 71.09375 \n", "Q 15.4375 72.65625 20.34375 73.4375 \n", "Q 25.25 74.21875 29.59375 74.21875 \n", "Q 40.828125 74.21875 47.359375 69.109375 \n", "Q 53.90625 64.015625 53.90625 55.328125 \n", "Q 53.90625 49.265625 50.4375 45.09375 \n", "Q 46.96875 40.921875 40.578125 39.3125 \n", "z\n", "\" id=\"DejaVuSans-51\"/>\n", "      </defs>\n", "      <g transform=\"translate(249.380844 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-51\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_17\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 261.114976 279 \n", "L 261.114976 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"261.114976\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2020-06-24 -->\n", "      <defs>\n", "       <path d=\"M 37.796875 64.3125 \n", "L 12.890625 25.390625 \n", "L 37.796875 25.390625 \n", "z\n", "M 35.203125 72.90625 \n", "L 47.609375 72.90625 \n", "L 47.609375 25.390625 \n", "L 58.015625 25.390625 \n", "L 58.015625 17.1875 \n", "L 47.609375 17.1875 \n", "L 47.609375 0 \n", "L 37.796875 0 \n", "L 37.796875 17.1875 \n", "L 4.890625 17.1875 \n", "L 4.890625 26.703125 \n", "z\n", "\" id=\"DejaVuSans-52\"/>\n", "      </defs>\n", "      <g transform=\"translate(263.874351 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-52\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_19\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 275.608482 279 \n", "L 275.608482 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"275.608482\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 2020-06-25 -->\n", "      <g transform=\"translate(278.367857 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-53\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_21\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 290.101989 279 \n", "L 290.101989 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"290.101989\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2020-06-26 -->\n", "      <g transform=\"translate(292.861364 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-54\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_23\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 333.582508 279 \n", "L 333.582508 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"333.582508\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 2020-06-29 -->\n", "      <g transform=\"translate(336.341883 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-57\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_25\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 348.076015 279 \n", "L 348.076015 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"348.076015\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 2020-06-30 -->\n", "      <g transform=\"translate(350.83539 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-54\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-51\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_27\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 362.569521 279 \n", "L 362.569521 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"362.569521\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 2020-07-01 -->\n", "      <g transform=\"translate(365.328896 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-49\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_29\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 377.063028 279 \n", "L 377.063028 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"377.063028\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 2020-07-02 -->\n", "      <g transform=\"translate(379.822403 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-50\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_31\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 435.037054 279 \n", "L 435.037054 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"435.037054\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 2020-07-06 -->\n", "      <g transform=\"translate(437.796429 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-54\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_17\">\n", "     <g id=\"line2d_33\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 449.53056 279 \n", "L 449.53056 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"449.53056\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 2020-07-07 -->\n", "      <g transform=\"translate(452.289935 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-55\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_18\">\n", "     <g id=\"line2d_35\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 464.024067 279 \n", "L 464.024067 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"464.024067\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 2020-07-08 -->\n", "      <g transform=\"translate(466.783442 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-56\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_19\">\n", "     <g id=\"line2d_37\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 478.517573 279 \n", "L 478.517573 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"478.517573\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 2020-07-09 -->\n", "      <g transform=\"translate(481.276948 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-57\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_20\">\n", "     <g id=\"line2d_39\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 493.01108 279 \n", "L 493.01108 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"493.01108\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 2020-07-10 -->\n", "      <g transform=\"translate(495.770455 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_21\">\n", "     <g id=\"line2d_41\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 536.491599 279 \n", "L 536.491599 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"536.491599\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 2020-07-13 -->\n", "      <g transform=\"translate(539.250974 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-51\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_22\">\n", "     <g id=\"line2d_43\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 550.985106 279 \n", "L 550.985106 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"550.985106\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 2020-07-14 -->\n", "      <g transform=\"translate(553.744481 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-52\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_23\">\n", "     <g id=\"line2d_45\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 565.478612 279 \n", "L 565.478612 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"565.478612\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 2020-07-15 -->\n", "      <g transform=\"translate(568.237987 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-53\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_24\">\n", "     <g id=\"line2d_47\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 579.972119 279 \n", "L 579.972119 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"579.972119\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 2020-07-16 -->\n", "      <g transform=\"translate(582.731494 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-54\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_25\">\n", "     <g id=\"line2d_49\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 594.465625 279 \n", "L 594.465625 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"594.465625\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_25\">\n", "      <!-- 2020-07-17 -->\n", "      <g transform=\"translate(597.225 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-55\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_26\">\n", "     <g id=\"line2d_51\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 637.946144 279 \n", "L 637.946144 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"637.946144\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_26\">\n", "      <!-- 2020-07-20 -->\n", "      <g transform=\"translate(640.705519 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_27\">\n", "     <g id=\"line2d_53\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 652.439651 279 \n", "L 652.439651 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"652.439651\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 2020-07-21 -->\n", "      <g transform=\"translate(655.199026 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-49\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_28\">\n", "     <g id=\"line2d_55\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 666.933157 279 \n", "L 666.933157 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"666.933157\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 2020-07-22 -->\n", "      <g transform=\"translate(669.692532 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-50\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_29\">\n", "     <g id=\"line2d_57\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 681.426664 279 \n", "L 681.426664 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"681.426664\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 2020-07-23 -->\n", "      <g transform=\"translate(684.186039 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-51\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_30\">\n", "     <g id=\"line2d_59\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 695.92017 279 \n", "L 695.92017 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_60\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"695.92017\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 2020-07-24 -->\n", "      <g transform=\"translate(698.679545 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-52\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_31\">\n", "     <g id=\"line2d_61\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 739.40069 279 \n", "L 739.40069 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_62\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"739.40069\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_31\">\n", "      <!-- 2020-07-27 -->\n", "      <g transform=\"translate(742.160065 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-55\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_32\">\n", "     <g id=\"line2d_63\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 753.894196 279 \n", "L 753.894196 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_64\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"753.894196\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_32\">\n", "      <!-- 2020-07-28 -->\n", "      <g transform=\"translate(756.653571 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-56\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_33\">\n", "     <g id=\"line2d_65\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 768.387703 279 \n", "L 768.387703 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_66\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"768.387703\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_33\">\n", "      <!-- 2020-07-29 -->\n", "      <g transform=\"translate(771.147078 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-57\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_34\">\n", "     <g id=\"line2d_67\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 782.881209 279 \n", "L 782.881209 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_68\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"782.881209\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_34\">\n", "      <!-- 2020-07-30 -->\n", "      <g transform=\"translate(785.640584 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-51\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_35\">\n", "     <g id=\"line2d_69\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 797.374716 279 \n", "L 797.374716 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_70\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"797.374716\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_35\">\n", "      <!-- 2020-07-31 -->\n", "      <g transform=\"translate(800.134091 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-51\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-49\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_36\">\n", "     <g id=\"line2d_71\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 840.855235 279 \n", "L 840.855235 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_72\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"840.855235\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_36\">\n", "      <!-- 2020-08-03 -->\n", "      <g transform=\"translate(843.61461 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-51\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_37\">\n", "     <g id=\"line2d_73\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 855.348742 279 \n", "L 855.348742 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_74\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"855.348742\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_37\">\n", "      <!-- 2020-08-04 -->\n", "      <g transform=\"translate(858.108117 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-52\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_38\">\n", "     <g id=\"line2d_75\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 869.842248 279 \n", "L 869.842248 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_76\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"869.842248\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_38\">\n", "      <!-- 2020-08-05 -->\n", "      <g transform=\"translate(872.601623 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-53\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_39\">\n", "     <g id=\"line2d_77\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 884.335755 279 \n", "L 884.335755 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_78\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"884.335755\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_39\">\n", "      <!-- 2020-08-06 -->\n", "      <g transform=\"translate(887.09513 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-54\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_40\">\n", "     <g id=\"line2d_79\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 898.829261 279 \n", "L 898.829261 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_80\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"898.829261\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_40\">\n", "      <!-- 2020-08-07 -->\n", "      <g transform=\"translate(901.588636 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-55\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_41\">\n", "     <g id=\"line2d_81\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 942.309781 279 \n", "L 942.309781 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_82\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"942.309781\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_41\">\n", "      <!-- 2020-08-10 -->\n", "      <g transform=\"translate(945.069156 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_42\">\n", "     <g id=\"line2d_83\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 956.803287 279 \n", "L 956.803287 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_84\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"956.803287\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_42\">\n", "      <!-- 2020-08-11 -->\n", "      <g transform=\"translate(959.562662 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-49\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_43\">\n", "     <g id=\"line2d_85\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 971.296794 279 \n", "L 971.296794 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_86\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"971.296794\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_43\">\n", "      <!-- 2020-08-12 -->\n", "      <g transform=\"translate(974.056169 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-50\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_44\">\n", "     <g id=\"line2d_87\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 985.7903 279 \n", "L 985.7903 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_88\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"985.7903\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_44\">\n", "      <!-- 2020-08-13 -->\n", "      <g transform=\"translate(988.549675 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-51\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_45\">\n", "     <g id=\"line2d_89\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 1000.283807 279 \n", "L 1000.283807 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_90\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"1000.283807\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_45\">\n", "      <!-- 2020-08-14 -->\n", "      <g transform=\"translate(1003.043182 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-52\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_46\">\n", "     <g id=\"line2d_91\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 1043.764326 279 \n", "L 1043.764326 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_92\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"1043.764326\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_46\">\n", "      <!-- 2020-08-17 -->\n", "      <g transform=\"translate(1046.523701 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-55\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_47\">\n", "     <g id=\"line2d_93\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 1058.257833 279 \n", "L 1058.257833 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_94\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"1058.257833\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_47\">\n", "      <!-- 2020-08-18 -->\n", "      <g transform=\"translate(1061.017208 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-56\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_48\">\n", "     <g id=\"line2d_95\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 1072.751339 279 \n", "L 1072.751339 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_96\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"1072.751339\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_48\">\n", "      <!-- 2020-08-19 -->\n", "      <g transform=\"translate(1075.510714 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-57\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_49\">\n", "     <g id=\"line2d_97\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 1087.244846 279 \n", "L 1087.244846 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_98\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"1087.244846\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_49\">\n", "      <!-- 2020-08-20 -->\n", "      <g transform=\"translate(1090.004221 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_50\">\n", "     <g id=\"line2d_99\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 1101.738352 279 \n", "L 1101.738352 7.2 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_100\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"1101.738352\" xlink:href=\"#ma7a730e8b6\" y=\"279\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_50\">\n", "      <!-- 2020-08-21 -->\n", "      <g transform=\"translate(1104.497727 344.115625)rotate(-90)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\n", "       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-56\"/>\n", "       <use x=\"417.822266\" xlink:href=\"#DejaVuSans-45\"/>\n", "       <use x=\"453.90625\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"517.529297\" xlink:href=\"#DejaVuSans-49\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_101\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 36.465625 235.140713 \n", "L 1152.465625 235.140713 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_102\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L -3.5 0 \n", "\" id=\"m8ac9daaff4\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"36.465625\" xlink:href=\"#m8ac9daaff4\" y=\"235.140713\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_51\">\n", "      <!-- 1.24 -->\n", "      <defs>\n", "       <path d=\"M 10.6875 12.40625 \n", "L 21 12.40625 \n", "L 21 0 \n", "L 10.6875 0 \n", "z\n", "\" id=\"DejaVuSans-46\"/>\n", "      </defs>\n", "      <g transform=\"translate(7.2 238.939931)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-52\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_103\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 36.465625 183.066759 \n", "L 1152.465625 183.066759 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_104\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"36.465625\" xlink:href=\"#m8ac9daaff4\" y=\"183.066759\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_52\">\n", "      <!-- 1.26 -->\n", "      <g transform=\"translate(7.2 186.865978)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-54\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_105\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 36.465625 130.992806 \n", "L 1152.465625 130.992806 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_106\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"36.465625\" xlink:href=\"#m8ac9daaff4\" y=\"130.992806\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_53\">\n", "      <!-- 1.28 -->\n", "      <g transform=\"translate(7.2 134.792025)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-50\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-56\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_107\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 36.465625 78.918852 \n", "L 1152.465625 78.918852 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_108\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"36.465625\" xlink:href=\"#m8ac9daaff4\" y=\"78.918852\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_54\">\n", "      <!-- 1.30 -->\n", "      <g transform=\"translate(7.2 82.718071)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-51\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_109\">\n", "      <path clip-path=\"url(#paec105fa77)\" d=\"M 36.465625 26.844899 \n", "L 1152.465625 26.844899 \n", "\" style=\"fill:none;stroke:#b0b0b0;stroke-linecap:square;stroke-width:0.8;\"/>\n", "     </g>\n", "     <g id=\"line2d_110\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"36.465625\" xlink:href=\"#m8ac9daaff4\" y=\"26.844899\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_55\">\n", "      <!-- 1.32 -->\n", "      <g transform=\"translate(7.2 30.644118)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-49\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-51\"/>\n", "       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-50\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path clip-path=\"url(#paec105fa77)\" d=\"M 87.192898 202.073752 \n", "L 130.673417 197.907836 \n", "L 145.166924 186.711936 \n", "L 159.66043 202.334122 \n", "L 174.153937 227.32962 \n", "L 188.647443 245.034764 \n", "L 232.127963 222.903334 \n", "L 246.621469 201.032273 \n", "L 261.114976 226.80888 \n", "L 275.608482 233.578494 \n", "L 290.101989 251.544008 \n", "L 333.582508 266.645455 \n", "L 348.076015 243.212175 \n", "L 362.569521 215.87335 \n", "L 377.063028 217.175199 \n", "L 435.037054 213.790392 \n", "L 449.53056 190.357113 \n", "L 464.024067 184.889348 \n", "L 478.517573 179.421583 \n", "L 493.01108 169.006792 \n", "L 536.491599 179.421583 \n", "L 550.985106 197.126727 \n", "L 565.478612 186.711936 \n", "L 579.972119 177.598994 \n", "L 594.465625 196.085248 \n", "L 637.946144 167.965313 \n", "L 652.439651 147.656471 \n", "L 666.933157 149.479059 \n", "L 681.426664 141.667966 \n", "L 695.92017 133.336134 \n", "L 739.40069 108.340636 \n", "L 753.894196 91.937341 \n", "L 768.387703 85.688466 \n", "L 782.881209 69.805911 \n", "L 797.374716 44.289673 \n", "L 840.855235 65.119255 \n", "L 855.348742 63.557036 \n", "L 869.842248 42.206715 \n", "L 884.335755 40.644497 \n", "L 898.829261 67.722952 \n", "L 942.309781 57.568531 \n", "L 956.803287 58.87038 \n", "L 971.296794 66.681473 \n", "L 985.7903 57.568531 \n", "L 1000.283807 50.798918 \n", "L 1043.764326 51.580027 \n", "L 1058.257833 19.554545 \n", "L 1072.751339 29.188227 \n", "L 1087.244846 29.448597 \n", "L 1101.738352 53.402615 \n", "\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;stroke-width:1.5;\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path clip-path=\"url(#paec105fa77)\" d=\"M 87.192898 170.560754 \n", "L 130.673417 166.521094 \n", "L 145.166924 180.647954 \n", "L 159.66043 181.085597 \n", "L 174.153937 178.843686 \n", "L 188.647443 181.264068 \n", "L 232.127963 192.438237 \n", "L 246.621469 195.711246 \n", "L 261.114976 204.556596 \n", "L 275.608482 196.506763 \n", "L 290.101989 207.558019 \n", "L 333.582508 214.360726 \n", "L 348.076015 224.177271 \n", "L 362.569521 232.407128 \n", "L 377.063028 217.523238 \n", "L 435.037054 204.951717 \n", "L 449.53056 211.876094 \n", "L 464.024067 210.443047 \n", "L 478.517573 201.356838 \n", "L 493.01108 194.534887 \n", "L 536.491599 181.986644 \n", "L 550.985106 179.792844 \n", "L 565.478612 174.171151 \n", "L 579.972119 175.617235 \n", "L 594.465625 164.393405 \n", "L 637.946144 166.694599 \n", "L 652.439651 160.812804 \n", "L 666.933157 174.954253 \n", "L 681.426664 163.439592 \n", "L 695.92017 159.740424 \n", "L 739.40069 156.116369 \n", "L 753.894196 151.792086 \n", "L 768.387703 141.789 \n", "L 782.881209 136.445413 \n", "L 797.374716 122.370697 \n", "L 840.855235 115.27809 \n", "L 855.348742 111.725114 \n", "L 869.842248 100.8694 \n", "L 884.335755 87.7187 \n", "L 898.829261 84.902578 \n", "L 942.309781 82.05821 \n", "L 956.803287 80.836224 \n", "L 971.296794 80.221662 \n", "L 985.7903 71.278851 \n", "L 1000.283807 61.052288 \n", "L 1043.764326 59.565544 \n", "L 1058.257833 54.433018 \n", "L 1072.751339 49.453512 \n", "L 1087.244846 37.569494 \n", "L 1101.738352 32.788324 \n", "\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;stroke-width:1.5;\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 36.465625 279 \n", "L 36.465625 7.2 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 1152.465625 279 \n", "L 1152.465625 7.2 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 36.465625 279 \n", "L 1152.465625 279 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 36.465625 7.2 \n", "L 1152.465625 7.2 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 43.465625 44.55625 \n", "L 154.165625 44.55625 \n", "Q 156.165625 44.55625 156.165625 42.55625 \n", "L 156.165625 14.2 \n", "Q 156.165625 12.2 154.165625 12.2 \n", "L 43.465625 12.2 \n", "Q 41.465625 12.2 41.465625 14.2 \n", "L 41.465625 42.55625 \n", "Q 41.465625 44.55625 43.465625 44.55625 \n", "z\n", "\" style=\"fill:#ffffff;opacity:0.8;stroke:#cccccc;stroke-linejoin:miter;\"/>\n", "    </g>\n", "    <g id=\"line2d_113\">\n", "     <path d=\"M 45.465625 20.298438 \n", "L 65.465625 20.298438 \n", "\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;stroke-width:1.5;\"/>\n", "    </g>\n", "    <g id=\"line2d_114\"/>\n", "    <g id=\"text_56\">\n", "     <!-- GBP Actual -->\n", "     <defs>\n", "      <path d=\"M 59.515625 10.40625 \n", "L 59.515625 29.984375 \n", "L 43.40625 29.984375 \n", "L 43.40625 38.09375 \n", "L 69.28125 38.09375 \n", "L 69.28125 6.78125 \n", "Q 63.578125 2.734375 56.6875 0.65625 \n", "Q 49.8125 -1.421875 42 -1.421875 \n", "Q 24.90625 -1.421875 15.25 8.5625 \n", "Q 5.609375 18.5625 5.609375 36.375 \n", "Q 5.609375 54.25 15.25 64.234375 \n", "Q 24.90625 74.21875 42 74.21875 \n", "Q 49.125 74.21875 55.546875 72.453125 \n", "Q 61.96875 70.703125 67.390625 67.28125 \n", "L 67.390625 56.78125 \n", "Q 61.921875 61.421875 55.765625 63.765625 \n", "Q 49.609375 66.109375 42.828125 66.109375 \n", "Q 29.4375 66.109375 22.71875 58.640625 \n", "Q 16.015625 51.171875 16.015625 36.375 \n", "Q 16.015625 21.625 22.71875 14.15625 \n", "Q 29.4375 6.6875 42.828125 6.6875 \n", "Q 48.046875 6.6875 52.140625 7.59375 \n", "Q 56.25 8.5 59.515625 10.40625 \n", "z\n", "\" id=\"DejaVuSans-71\"/>\n", "      <path d=\"M 19.671875 34.8125 \n", "L 19.671875 8.109375 \n", "L 35.5 8.109375 \n", "Q 43.453125 8.109375 47.28125 11.40625 \n", "Q 51.125 14.703125 51.125 21.484375 \n", "Q 51.125 28.328125 47.28125 31.5625 \n", "Q 43.453125 34.8125 35.5 34.8125 \n", "z\n", "M 19.671875 64.796875 \n", "L 19.671875 42.828125 \n", "L 34.28125 42.828125 \n", "Q 41.5 42.828125 45.03125 45.53125 \n", "Q 48.578125 48.25 48.578125 53.8125 \n", "Q 48.578125 59.328125 45.03125 62.0625 \n", "Q 41.5 64.796875 34.28125 64.796875 \n", "z\n", "M 9.8125 72.90625 \n", "L 35.015625 72.90625 \n", "Q 46.296875 72.90625 52.390625 68.21875 \n", "Q 58.5 63.53125 58.5 54.890625 \n", "Q 58.5 48.1875 55.375 44.234375 \n", "Q 52.25 40.28125 46.1875 39.3125 \n", "Q 53.46875 37.75 57.5 32.78125 \n", "Q 61.53125 27.828125 61.53125 20.40625 \n", "Q 61.53125 10.640625 54.890625 5.3125 \n", "Q 48.25 0 35.984375 0 \n", "L 9.8125 0 \n", "z\n", "\" id=\"DejaVuSans-66\"/>\n", "      <path d=\"M 19.671875 64.796875 \n", "L 19.671875 37.40625 \n", "L 32.078125 37.40625 \n", "Q 38.96875 37.40625 42.71875 40.96875 \n", "Q 46.484375 44.53125 46.484375 51.125 \n", "Q 46.484375 57.671875 42.71875 61.234375 \n", "Q 38.96875 64.796875 32.078125 64.796875 \n", "z\n", "M 9.8125 72.90625 \n", "L 32.078125 72.90625 \n", "Q 44.34375 72.90625 50.609375 67.359375 \n", "Q 56.890625 61.8125 56.890625 51.125 \n", "Q 56.890625 40.328125 50.609375 34.8125 \n", "Q 44.34375 29.296875 32.078125 29.296875 \n", "L 19.671875 29.296875 \n", "L 19.671875 0 \n", "L 9.8125 0 \n", "z\n", "\" id=\"DejaVuSans-80\"/>\n", "      <path id=\"DejaVuSans-32\"/>\n", "      <path d=\"M 34.1875 63.1875 \n", "L 20.796875 26.90625 \n", "L 47.609375 26.90625 \n", "z\n", "M 28.609375 72.90625 \n", "L 39.796875 72.90625 \n", "L 67.578125 0 \n", "L 57.328125 0 \n", "L 50.6875 18.703125 \n", "L 17.828125 18.703125 \n", "L 11.1875 0 \n", "L 0.78125 0 \n", "z\n", "\" id=\"DejaVuSans-65\"/>\n", "      <path d=\"M 48.78125 52.59375 \n", "L 48.78125 44.1875 \n", "Q 44.96875 46.296875 41.140625 47.34375 \n", "Q 37.3125 48.390625 33.40625 48.390625 \n", "Q 24.65625 48.390625 19.8125 42.84375 \n", "Q 14.984375 37.3125 14.984375 27.296875 \n", "Q 14.984375 17.28125 19.8125 11.734375 \n", "Q 24.65625 6.203125 33.40625 6.203125 \n", "Q 37.3125 6.203125 41.140625 7.25 \n", "Q 44.96875 8.296875 48.78125 10.40625 \n", "L 48.78125 2.09375 \n", "Q 45.015625 0.34375 40.984375 -0.53125 \n", "Q 36.96875 -1.421875 32.421875 -1.421875 \n", "Q 20.0625 -1.421875 12.78125 6.34375 \n", "Q 5.515625 14.109375 5.515625 27.296875 \n", "Q 5.515625 40.671875 12.859375 48.328125 \n", "Q 20.21875 56 33.015625 56 \n", "Q 37.15625 56 41.109375 55.140625 \n", "Q 45.0625 54.296875 48.78125 52.59375 \n", "z\n", "\" id=\"DejaVuSans-99\"/>\n", "      <path d=\"M 18.3125 70.21875 \n", "L 18.3125 54.6875 \n", "L 36.8125 54.6875 \n", "L 36.8125 47.703125 \n", "L 18.3125 47.703125 \n", "L 18.3125 18.015625 \n", "Q 18.3125 11.328125 20.140625 9.421875 \n", "Q 21.96875 7.515625 27.59375 7.515625 \n", "L 36.8125 7.515625 \n", "L 36.8125 0 \n", "L 27.59375 0 \n", "Q 17.1875 0 13.234375 3.875 \n", "Q 9.28125 7.765625 9.28125 18.015625 \n", "L 9.28125 47.703125 \n", "L 2.6875 47.703125 \n", "L 2.6875 54.6875 \n", "L 9.28125 54.6875 \n", "L 9.28125 70.21875 \n", "z\n", "\" id=\"DejaVuSans-116\"/>\n", "      <path d=\"M 8.5 21.578125 \n", "L 8.5 54.6875 \n", "L 17.484375 54.6875 \n", "L 17.484375 21.921875 \n", "Q 17.484375 14.15625 20.5 10.265625 \n", "Q 23.53125 6.390625 29.59375 6.390625 \n", "Q 36.859375 6.390625 41.078125 11.03125 \n", "Q 45.3125 15.671875 45.3125 23.6875 \n", "L 45.3125 54.6875 \n", "L 54.296875 54.6875 \n", "L 54.296875 0 \n", "L 45.3125 0 \n", "L 45.3125 8.40625 \n", "Q 42.046875 3.421875 37.71875 1 \n", "Q 33.40625 -1.421875 27.6875 -1.421875 \n", "Q 18.265625 -1.421875 13.375 4.4375 \n", "Q 8.5 10.296875 8.5 21.578125 \n", "z\n", "M 31.109375 56 \n", "z\n", "\" id=\"DejaVuSans-117\"/>\n", "      <path d=\"M 34.28125 27.484375 \n", "Q 23.390625 27.484375 19.1875 25 \n", "Q 14.984375 22.515625 14.984375 16.5 \n", "Q 14.984375 11.71875 18.140625 8.90625 \n", "Q 21.296875 6.109375 26.703125 6.109375 \n", "Q 34.1875 6.109375 38.703125 11.40625 \n", "Q 43.21875 16.703125 43.21875 25.484375 \n", "L 43.21875 27.484375 \n", "z\n", "M 52.203125 31.203125 \n", "L 52.203125 0 \n", "L 43.21875 0 \n", "L 43.21875 8.296875 \n", "Q 40.140625 3.328125 35.546875 0.953125 \n", "Q 30.953125 -1.421875 24.3125 -1.421875 \n", "Q 15.921875 -1.421875 10.953125 3.296875 \n", "Q 6 8.015625 6 15.921875 \n", "Q 6 25.140625 12.171875 29.828125 \n", "Q 18.359375 34.515625 30.609375 34.515625 \n", "L 43.21875 34.515625 \n", "L 43.21875 35.40625 \n", "Q 43.21875 41.609375 39.140625 45 \n", "Q 35.0625 48.390625 27.6875 48.390625 \n", "Q 23 48.390625 18.546875 47.265625 \n", "Q 14.109375 46.140625 10.015625 43.890625 \n", "L 10.015625 52.203125 \n", "Q 14.9375 54.109375 19.578125 55.046875 \n", "Q 24.21875 56 28.609375 56 \n", "Q 40.484375 56 46.34375 49.84375 \n", "Q 52.203125 43.703125 52.203125 31.203125 \n", "z\n", "\" id=\"DejaVuSans-97\"/>\n", "      <path d=\"M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "\" id=\"DejaVuSans-108\"/>\n", "     </defs>\n", "     <g transform=\"translate(73.465625 23.798438)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-71\"/>\n", "      <use x=\"77.490234\" xlink:href=\"#DejaVuSans-66\"/>\n", "      <use x=\"146.09375\" xlink:href=\"#DejaVuSans-80\"/>\n", "      <use x=\"206.396484\" xlink:href=\"#DejaVuSans-32\"/>\n", "      <use x=\"238.183594\" xlink:href=\"#DejaVuSans-65\"/>\n", "      <use x=\"304.841797\" xlink:href=\"#DejaVuSans-99\"/>\n", "      <use x=\"359.822266\" xlink:href=\"#DejaVuSans-116\"/>\n", "      <use x=\"399.03125\" xlink:href=\"#DejaVuSans-117\"/>\n", "      <use x=\"462.410156\" xlink:href=\"#DejaVuSans-97\"/>\n", "      <use x=\"523.689453\" xlink:href=\"#DejaVuSans-108\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_115\">\n", "     <path d=\"M 45.465625 34.976563 \n", "L 65.465625 34.976563 \n", "\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;stroke-width:1.5;\"/>\n", "    </g>\n", "    <g id=\"line2d_116\"/>\n", "    <g id=\"text_57\">\n", "     <!-- GBP Predictions -->\n", "     <defs>\n", "      <path d=\"M 41.109375 46.296875 \n", "Q 39.59375 47.171875 37.8125 47.578125 \n", "Q 36.03125 48 33.890625 48 \n", "Q 26.265625 48 22.1875 43.046875 \n", "Q 18.109375 38.09375 18.109375 28.8125 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 20.953125 51.171875 25.484375 53.578125 \n", "Q 30.03125 56 36.53125 56 \n", "Q 37.453125 56 38.578125 55.875 \n", "Q 39.703125 55.765625 41.0625 55.515625 \n", "z\n", "\" id=\"DejaVuSans-114\"/>\n", "      <path d=\"M 56.203125 29.59375 \n", "L 56.203125 25.203125 \n", "L 14.890625 25.203125 \n", "Q 15.484375 15.921875 20.484375 11.0625 \n", "Q 25.484375 6.203125 34.421875 6.203125 \n", "Q 39.59375 6.203125 44.453125 7.46875 \n", "Q 49.3125 8.734375 54.109375 11.28125 \n", "L 54.109375 2.78125 \n", "Q 49.265625 0.734375 44.1875 -0.34375 \n", "Q 39.109375 -1.421875 33.890625 -1.421875 \n", "Q 20.796875 -1.421875 13.15625 6.1875 \n", "Q 5.515625 13.8125 5.515625 26.8125 \n", "Q 5.515625 40.234375 12.765625 48.109375 \n", "Q 20.015625 56 32.328125 56 \n", "Q 43.359375 56 49.78125 48.890625 \n", "Q 56.203125 41.796875 56.203125 29.59375 \n", "z\n", "M 47.21875 32.234375 \n", "Q 47.125 39.59375 43.09375 43.984375 \n", "Q 39.0625 48.390625 32.421875 48.390625 \n", "Q 24.90625 48.390625 20.390625 44.140625 \n", "Q 15.875 39.890625 15.1875 32.171875 \n", "z\n", "\" id=\"DejaVuSans-101\"/>\n", "      <path d=\"M 45.40625 46.390625 \n", "L 45.40625 75.984375 \n", "L 54.390625 75.984375 \n", "L 54.390625 0 \n", "L 45.40625 0 \n", "L 45.40625 8.203125 \n", "Q 42.578125 3.328125 38.25 0.953125 \n", "Q 33.9375 -1.421875 27.875 -1.421875 \n", "Q 17.96875 -1.421875 11.734375 6.484375 \n", "Q 5.515625 14.40625 5.515625 27.296875 \n", "Q 5.515625 40.1875 11.734375 48.09375 \n", "Q 17.96875 56 27.875 56 \n", "Q 33.9375 56 38.25 53.625 \n", "Q 42.578125 51.265625 45.40625 46.390625 \n", "z\n", "M 14.796875 27.296875 \n", "Q 14.796875 17.390625 18.875 11.75 \n", "Q 22.953125 6.109375 30.078125 6.109375 \n", "Q 37.203125 6.109375 41.296875 11.75 \n", "Q 45.40625 17.390625 45.40625 27.296875 \n", "Q 45.40625 37.203125 41.296875 42.84375 \n", "Q 37.203125 48.484375 30.078125 48.484375 \n", "Q 22.953125 48.484375 18.875 42.84375 \n", "Q 14.796875 37.203125 14.796875 27.296875 \n", "z\n", "\" id=\"DejaVuSans-100\"/>\n", "      <path d=\"M 9.421875 54.6875 \n", "L 18.40625 54.6875 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 64.59375 \n", "L 9.421875 64.59375 \n", "z\n", "\" id=\"DejaVuSans-105\"/>\n", "      <path d=\"M 30.609375 48.390625 \n", "Q 23.390625 48.390625 19.1875 42.75 \n", "Q 14.984375 37.109375 14.984375 27.296875 \n", "Q 14.984375 17.484375 19.15625 11.84375 \n", "Q 23.34375 6.203125 30.609375 6.203125 \n", "Q 37.796875 6.203125 41.984375 11.859375 \n", "Q 46.1875 17.53125 46.1875 27.296875 \n", "Q 46.1875 37.015625 41.984375 42.703125 \n", "Q 37.796875 48.390625 30.609375 48.390625 \n", "z\n", "M 30.609375 56 \n", "Q 42.328125 56 49.015625 48.375 \n", "Q 55.71875 40.765625 55.71875 27.296875 \n", "Q 55.71875 13.875 49.015625 6.21875 \n", "Q 42.328125 -1.421875 30.609375 -1.421875 \n", "Q 18.84375 -1.421875 12.171875 6.21875 \n", "Q 5.515625 13.875 5.515625 27.296875 \n", "Q 5.515625 40.765625 12.171875 48.375 \n", "Q 18.84375 56 30.609375 56 \n", "z\n", "\" id=\"DejaVuSans-111\"/>\n", "      <path d=\"M 54.890625 33.015625 \n", "L 54.890625 0 \n", "L 45.90625 0 \n", "L 45.90625 32.71875 \n", "Q 45.90625 40.484375 42.875 44.328125 \n", "Q 39.84375 48.1875 33.796875 48.1875 \n", "Q 26.515625 48.1875 22.3125 43.546875 \n", "Q 18.109375 38.921875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.34375 51.125 25.703125 53.5625 \n", "Q 30.078125 56 35.796875 56 \n", "Q 45.21875 56 50.046875 50.171875 \n", "Q 54.890625 44.34375 54.890625 33.015625 \n", "z\n", "\" id=\"DejaVuSans-110\"/>\n", "      <path d=\"M 44.28125 53.078125 \n", "L 44.28125 44.578125 \n", "Q 40.484375 46.53125 36.375 47.5 \n", "Q 32.28125 48.484375 27.875 48.484375 \n", "Q 21.1875 48.484375 17.84375 46.4375 \n", "Q 14.5 44.390625 14.5 40.28125 \n", "Q 14.5 37.15625 16.890625 35.375 \n", "Q 19.28125 33.59375 26.515625 31.984375 \n", "L 29.59375 31.296875 \n", "Q 39.15625 29.25 43.1875 25.515625 \n", "Q 47.21875 21.78125 47.21875 15.09375 \n", "Q 47.21875 7.46875 41.1875 3.015625 \n", "Q 35.15625 -1.421875 24.609375 -1.421875 \n", "Q 20.21875 -1.421875 15.453125 -0.5625 \n", "Q 10.6875 0.296875 5.421875 2 \n", "L 5.421875 11.28125 \n", "Q 10.40625 8.6875 15.234375 7.390625 \n", "Q 20.0625 6.109375 24.8125 6.109375 \n", "Q 31.15625 6.109375 34.5625 8.28125 \n", "Q 37.984375 10.453125 37.984375 14.40625 \n", "Q 37.984375 18.0625 35.515625 20.015625 \n", "Q 33.0625 21.96875 24.703125 23.78125 \n", "L 21.578125 24.515625 \n", "Q 13.234375 26.265625 9.515625 29.90625 \n", "Q 5.8125 33.546875 5.8125 39.890625 \n", "Q 5.8125 47.609375 11.28125 51.796875 \n", "Q 16.75 56 26.8125 56 \n", "Q 31.78125 56 36.171875 55.265625 \n", "Q 40.578125 54.546875 44.28125 53.078125 \n", "z\n", "\" id=\"DejaVuSans-115\"/>\n", "     </defs>\n", "     <g transform=\"translate(73.465625 38.476563)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-71\"/>\n", "      <use x=\"77.490234\" xlink:href=\"#DejaVuSans-66\"/>\n", "      <use x=\"146.09375\" xlink:href=\"#DejaVuSans-80\"/>\n", "      <use x=\"206.396484\" xlink:href=\"#DejaVuSans-32\"/>\n", "      <use x=\"238.183594\" xlink:href=\"#DejaVuSans-80\"/>\n", "      <use x=\"296.736328\" xlink:href=\"#DejaVuSans-114\"/>\n", "      <use x=\"335.599609\" xlink:href=\"#DejaVuSans-101\"/>\n", "      <use x=\"397.123047\" xlink:href=\"#DejaVuSans-100\"/>\n", "      <use x=\"460.599609\" xlink:href=\"#DejaVuSans-105\"/>\n", "      <use x=\"488.382812\" xlink:href=\"#DejaVuSans-99\"/>\n", "      <use x=\"543.363281\" xlink:href=\"#DejaVuSans-116\"/>\n", "      <use x=\"582.572266\" xlink:href=\"#DejaVuSans-105\"/>\n", "      <use x=\"610.355469\" xlink:href=\"#DejaVuSans-111\"/>\n", "      <use x=\"671.537109\" xlink:href=\"#DejaVuSans-110\"/>\n", "      <use x=\"734.916016\" xlink:href=\"#DejaVuSans-115\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"paec105fa77\">\n", "   <rect height=\"271.8\" width=\"1116\" x=\"36.465625\" y=\"7.2\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1440x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Test RMSE: 0.01295\n"]}], "source": ["# evaluate forecasts and plot them together\n", "plt.figure(figsize=(20,5))\n", "plt.plot(actuals.index, actuals, color='blue')\n", "plt.plot(actuals.index, predictions, color='red')\n", "plt.legend(('GBP Actual', 'GBP Predictions'))\n", "plt.xticks(actuals.index, rotation=90)\n", "plt.grid(True)\n", "plt.show()\n", "\n", "rmse = sqrt(mean_squared_error(actuals, predictions))\n", "print('Test RMSE: %.5f' % rmse)"]}], "metadata": {"kernelspec": {"display_name": "exchange", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "orig_nbformat": 2}, "nbformat": 4, "nbformat_minor": 2}