{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PSO-Optimized CNN Univariate Model"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import tensorflow as tf\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import mean_squared_error\n", "import tensorflow as tf\n", "import keras\n", "from tensorflow.keras.backend import sigmoid\n", "from keras.models import Sequential\n", "from keras import models, layers, backend, optimizers\n", "from keras.layers import Dropout, BatchNormalization, Conv1D, MaxPooling1D, Flatten\n", "from keras.callbacks import EarlyStopping\n", "import time\n", "from math import sqrt\n", "\n", "# Import our PSO optimizer\n", "from pso_optimizer import PSOOptimizer, create_cnn_model"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "Date", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "AUD", "rawType": "float64", "type": "float"}, {"name": "EUR", "rawType": "float64", "type": "float"}, {"name": "NZD", "rawType": "float64", "type": "float"}, {"name": "GBP", "rawType": "float64", "type": "float"}, {"name": "BRL", "rawType": "float64", "type": "float"}, {"name": "CAD", "rawType": "float64", "type": "float"}, {"name": "CNY", "rawType": "float64", "type": "float"}, {"name": "DKK", "rawType": "float64", "type": "float"}, {"name": "HKD", "rawType": "float64", "type": "float"}, {"name": "INR", "rawType": "float64", "type": "float"}, {"name": "JPY", "rawType": "float64", "type": "float"}, {"name": "MYR", "rawType": "float64", "type": "float"}, {"name": "MXN", "rawType": "float64", "type": "float"}, {"name": "NOK", "rawType": "float64", "type": "float"}, {"name": "ZAR", "rawType": "float64", "type": "float"}, {"name": "SGD", "rawType": "float64", "type": "float"}, {"name": "KRW", "rawType": "float64", "type": "float"}, {"name": "LKR", "rawType": "float64", "type": "float"}, {"name": "SEK", "rawType": "float64", "type": "float"}, {"name": "CHF", "rawType": "float64", "type": "float"}, {"name": "TWD", "rawType": "float64", "type": "float"}, {"name": "THB", "rawType": "float64", "type": "float"}, {"name": "VEB", "rawType": "float64", "type": "float"}, {"name": "gdpGBP", "rawType": "float64", "type": "float"}, {"name": "gdpUSD", "rawType": "float64", "type": "float"}, {"name": "GBR_Value", "rawType": "float64", "type": "float"}, {"name": "USA_Value", "rawType": "float64", "type": "float"}, {"name": "liborGBP", "rawType": "float64", "type": "float"}, {"name": "liborUSD", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "765453a0-979a-4893-a756-f9698145903c", "rows": [["2001-01-02 00:00:00", "0.5592", "0.9465", "0.4432", "1.4977", "1.938", "1.4963", "8.2779", "7.8845", "7.8", "46.74", "114.73", "3.8", "9.714", "8.76", "7.569", "1.737", "1279.0", "82.6", "9.415", "1.6075", "33.0", "43.79", "0.7008", "99.97764281514756", "100.81527694707992", "-1.815904030756699", "-4.084297260226566", "5.81094", "6.65125"], ["2001-01-03 00:00:00", "0.5635", "0.9473", "0.4463", "1.5045", "1.946", "1.4982", "8.2773", "7.875", "7.8", "46.75", "114.26", "3.8", "9.836", "8.76", "7.5025", "1.7325", "1271.5", "82.7", "9.445", "1.6025", "33.078", "43.7", "0.7002", "99.98086597817554", "100.80719091285982", "-1.824859470775798", "-4.076653803865071", "6.0975", "6.65375"], ["2001-01-04 00:00:00", "0.5655", "0.9448", "0.4457", "1.493", "1.938", "1.4985", "8.2781", "7.8991", "7.7998", "46.78", "115.47", "3.8", "9.698", "8.7628", "7.542999999999999", "1.737", "1265.0", "82.75", "9.422", "1.6115", "33.0", "43.53", "0.6994", "99.98408914120353", "100.79910487863974", "-1.833814910794897", "-4.069010347503577", "5.57125", "6.09625"], ["2001-01-05 00:00:00", "0.5712", "0.9535", "0.4518", "1.499", "1.953", "1.5003", "8.2775", "7.826", "7.7993", "46.76", "116.19", "3.8", "9.753", "8.701", "7.56", "1.7322", "1264.7", "83.15", "9.338", "1.6025", "32.927", "43.26", "0.6988", "99.9873123042315", "100.79101884441964", "-1.8427703508139963", "-4.0613668911420815", "5.3781300000000005", "6.01625"], ["2001-01-08 00:00:00", "0.5660000000000001", "0.9486", "0.4505", "1.4969", "1.954", "1.4944", "8.2778", "7.8705", "7.7998", "46.73", "115.97", "3.8", "9.691", "8.7435", "7.595", "1.7275", "1263.6", "82.95", "9.418", "1.6076", "32.85", "42.95", "0.6990000000000001", "99.99053546725946", "100.78293281019955", "-1.8517257908330955", "-4.053723434780587", "5.5", "6.015"]], "shape": {"columns": 29, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>AUD</th>\n", "      <th>EUR</th>\n", "      <th>NZD</th>\n", "      <th>GBP</th>\n", "      <th>BRL</th>\n", "      <th>CAD</th>\n", "      <th>CNY</th>\n", "      <th>DKK</th>\n", "      <th>HKD</th>\n", "      <th>INR</th>\n", "      <th>...</th>\n", "      <th>CHF</th>\n", "      <th>TWD</th>\n", "      <th>THB</th>\n", "      <th>VEB</th>\n", "      <th>gdpGBP</th>\n", "      <th>gdpUSD</th>\n", "      <th>GBR_Value</th>\n", "      <th>USA_Value</th>\n", "      <th>liborGBP</th>\n", "      <th>liborUSD</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2001-01-02</th>\n", "      <td>0.5592</td>\n", "      <td>0.9465</td>\n", "      <td>0.4432</td>\n", "      <td>1.4977</td>\n", "      <td>1.938</td>\n", "      <td>1.4963</td>\n", "      <td>8.2779</td>\n", "      <td>7.8845</td>\n", "      <td>7.8000</td>\n", "      <td>46.74</td>\n", "      <td>...</td>\n", "      <td>1.6075</td>\n", "      <td>33.000</td>\n", "      <td>43.79</td>\n", "      <td>0.7008</td>\n", "      <td>99.977643</td>\n", "      <td>100.815277</td>\n", "      <td>-1.815904</td>\n", "      <td>-4.084297</td>\n", "      <td>5.81094</td>\n", "      <td>6.65125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2001-01-03</th>\n", "      <td>0.5635</td>\n", "      <td>0.9473</td>\n", "      <td>0.4463</td>\n", "      <td>1.5045</td>\n", "      <td>1.946</td>\n", "      <td>1.4982</td>\n", "      <td>8.2773</td>\n", "      <td>7.8750</td>\n", "      <td>7.8000</td>\n", "      <td>46.75</td>\n", "      <td>...</td>\n", "      <td>1.6025</td>\n", "      <td>33.078</td>\n", "      <td>43.70</td>\n", "      <td>0.7002</td>\n", "      <td>99.980866</td>\n", "      <td>100.807191</td>\n", "      <td>-1.824859</td>\n", "      <td>-4.076654</td>\n", "      <td>6.09750</td>\n", "      <td>6.65375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2001-01-04</th>\n", "      <td>0.5655</td>\n", "      <td>0.9448</td>\n", "      <td>0.4457</td>\n", "      <td>1.4930</td>\n", "      <td>1.938</td>\n", "      <td>1.4985</td>\n", "      <td>8.2781</td>\n", "      <td>7.8991</td>\n", "      <td>7.7998</td>\n", "      <td>46.78</td>\n", "      <td>...</td>\n", "      <td>1.6115</td>\n", "      <td>33.000</td>\n", "      <td>43.53</td>\n", "      <td>0.6994</td>\n", "      <td>99.984089</td>\n", "      <td>100.799105</td>\n", "      <td>-1.833815</td>\n", "      <td>-4.069010</td>\n", "      <td>5.57125</td>\n", "      <td>6.09625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2001-01-05</th>\n", "      <td>0.5712</td>\n", "      <td>0.9535</td>\n", "      <td>0.4518</td>\n", "      <td>1.4990</td>\n", "      <td>1.953</td>\n", "      <td>1.5003</td>\n", "      <td>8.2775</td>\n", "      <td>7.8260</td>\n", "      <td>7.7993</td>\n", "      <td>46.76</td>\n", "      <td>...</td>\n", "      <td>1.6025</td>\n", "      <td>32.927</td>\n", "      <td>43.26</td>\n", "      <td>0.6988</td>\n", "      <td>99.987312</td>\n", "      <td>100.791019</td>\n", "      <td>-1.842770</td>\n", "      <td>-4.061367</td>\n", "      <td>5.37813</td>\n", "      <td>6.01625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2001-01-08</th>\n", "      <td>0.5660</td>\n", "      <td>0.9486</td>\n", "      <td>0.4505</td>\n", "      <td>1.4969</td>\n", "      <td>1.954</td>\n", "      <td>1.4944</td>\n", "      <td>8.2778</td>\n", "      <td>7.8705</td>\n", "      <td>7.7998</td>\n", "      <td>46.73</td>\n", "      <td>...</td>\n", "      <td>1.6076</td>\n", "      <td>32.850</td>\n", "      <td>42.95</td>\n", "      <td>0.6990</td>\n", "      <td>99.990535</td>\n", "      <td>100.782933</td>\n", "      <td>-1.851726</td>\n", "      <td>-4.053723</td>\n", "      <td>5.50000</td>\n", "      <td>6.01500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 29 columns</p>\n", "</div>"], "text/plain": ["               AUD     EUR     NZD     GBP    BRL     CAD     CNY     DKK  \\\n", "Date                                                                        \n", "2001-01-02  0.5592  0.9465  0.4432  1.4977  1.938  1.4963  8.2779  7.8845   \n", "2001-01-03  0.5635  0.9473  0.4463  1.5045  1.946  1.4982  8.2773  7.8750   \n", "2001-01-04  0.5655  0.9448  0.4457  1.4930  1.938  1.4985  8.2781  7.8991   \n", "2001-01-05  0.5712  0.9535  0.4518  1.4990  1.953  1.5003  8.2775  7.8260   \n", "2001-01-08  0.5660  0.9486  0.4505  1.4969  1.954  1.4944  8.2778  7.8705   \n", "\n", "               HKD    INR  ...     CHF     TWD    THB     VEB     gdpGBP  \\\n", "Date                       ...                                             \n", "2001-01-02  7.8000  46.74  ...  1.6075  33.000  43.79  0.7008  99.977643   \n", "2001-01-03  7.8000  46.75  ...  1.6025  33.078  43.70  0.7002  99.980866   \n", "2001-01-04  7.7998  46.78  ...  1.6115  33.000  43.53  0.6994  99.984089   \n", "2001-01-05  7.7993  46.76  ...  1.6025  32.927  43.26  0.6988  99.987312   \n", "2001-01-08  7.7998  46.73  ...  1.6076  32.850  42.95  0.6990  99.990535   \n", "\n", "                gdpUSD  GBR_Value  USA_Value  liborGBP  liborUSD  \n", "Date                                                              \n", "2001-01-02  100.815277  -1.815904  -4.084297   5.81094   6.65125  \n", "2001-01-03  100.807191  -1.824859  -4.076654   6.09750   6.65375  \n", "2001-01-04  100.799105  -1.833815  -4.069010   5.57125   6.09625  \n", "2001-01-05  100.791019  -1.842770  -4.061367   5.37813   6.01625  \n", "2001-01-08  100.782933  -1.851726  -4.053723   5.50000   6.01500  \n", "\n", "[5 rows x 29 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# we read our dataframe and assign the time column as the index values of the dataframe\n", "file = \"./DATA/Merged.csv\"\n", "df = pd.read_csv(file, index_col='Date', parse_dates=True)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def CreateLaggedSequence(data, lag):\n", "    # make two lists for PredictorSequences and ResponseVariables\n", "    PredictorSequences, ResponseVariables = list(), list()\n", "    for i in range(len(data)):\n", "        # mark the range of the sequence\n", "        end_i = i + lag\n", "        # check when the data ends\n", "        if end_i+1 > len(data):\n", "            # stop sequence creation\n", "            break\n", "        # get the predictors and responses\n", "        PredictorSequence = data[i:end_i]\n", "        ResponseVariable = data[end_i]\n", "        # append them to the lists\n", "        PredictorSequences.append(PredictorSequence)\n", "        ResponseVariables.append(ResponseVariable)\n", "        # print(end_i)\n", "    return np.array(PredictorSequences), np.array(ResponseVariables)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["((4870, 50), (4870,), 4870, 4870)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the univariate series\n", "X = df['GBP']\n", "np_X = np.array(X)\n", "# Create the lagged values for the series (50 lags)\n", "X, y = CreateLaggedSequence(np_X, 50)\n", "lag = X.shape[1]\n", "# Reshape it for the process\n", "X = X.reshape(X.shape[0], X.shape[1]) \n", "X.shape, y.shape, len(X), len(y)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["((4820, 50), (50, 50), (4820,), (50,))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# split the train and test sets (last 50 observation spared for the test)\n", "x_train, x_test = X[:-50], X[-50:]\n", "y_train, y_test = y[:-50], y[-50:]\n", "x_train.shape, x_test.shape, y_train.shape, y_test.shape"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Define a new activation function\n", "# <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2018, August 19). Swish Activation Function by Google. Retrieved from https://medium.com/@neuralnets/swish-activation-function-by-google-53e1ea86f820\n", "\n", "def swish(x, beta = 1):\n", "    return (x * sigmoid(beta * x))\n", "\n", "from tensorflow.keras.utils import get_custom_objects\n", "from keras.layers import Activation\n", "get_custom_objects().update({'swish': Activation(swish)})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Define the hyperparameter search space for PSO\n", "hyperparameter_bounds = {\n", "    'num_conv_layers': (1, 3),           # Number of convolutional layers\n", "    'filters_layer_1': (16, 128),        # Filters in first conv layer\n", "    'filters_layer_2': (32, 256),        # Filters in second conv layer\n", "    'filters_layer_3': (64, 512),        # Filters in third conv layer\n", "    'kernel_size_layer_1': (2, 5),       # Kernel size in first conv layer\n", "    'kernel_size_layer_2': (2, 5),       # Kernel size in second conv layer\n", "    'kernel_size_layer_3': (2, 5),       # Kernel size in third conv layer\n", "    'use_pooling': (0, 1),               # Whether to use pooling (0=No, 1=Yes)\n", "    'pool_size': (2, 3),                 # Pooling size\n", "    'num_dense_layers': (1, 3),          # Number of dense layers after conv\n", "    'dense_neurons_layer_1': (32, 256),  # Neurons in first dense layer\n", "    'dense_neurons_layer_2': (16, 128),  # Neurons in second dense layer\n", "    'dense_neurons_layer_3': (8, 64),    # Neurons in third dense layer\n", "    'learning_rate': (0.0001, 0.01),     # Learning rate\n", "    'batch_size': (16, 128),             # Batch size\n", "    'use_dropout': (0, 1),               # Whether to use dropout (0=No, 1=Yes)\n", "    'dropout_rate': (0.1, 0.5),          # Dropout rate\n", "    'activation_choice': (0, 2)          # 0=swish, 1=relu, 2=tanh\n", "}"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Function to map activation choice to actual activation function\n", "def get_activation(choice):\n", "    activations = ['swish', 'relu', 'tanh']\n", "    return activations[int(choice)]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Define the fitness function for PSO\n", "def fitness_function(hyperparams):\n", "    # Map the activation choice to an actual activation function\n", "    hyperparams['activation'] = get_activation(hyperparams['activation_choice'])\n", "    hyperparams['use_dropout'] = bool(int(hyperparams['use_dropout']))\n", "    hyperparams['use_pooling'] = bool(int(hyperparams['use_pooling']))\n", "    \n", "    # Convert integer parameters\n", "    for param in ['batch_size', 'num_conv_layers', 'num_dense_layers', \n", "                  'kernel_size_layer_1', 'kernel_size_layer_2', 'kernel_size_layer_3',\n", "                  'pool_size']:\n", "        if param in hyperparams:\n", "            hyperparams[param] = int(hyperparams[param])\n", "    \n", "    # Create and compile the model\n", "    model = create_cnn_model(hyperparams, (x_train.shape[1],))\n", "    \n", "    # Use a validation split for early stopping\n", "    early_stopping = EarlyStopping(monitor='val_loss', patience=20, restore_best_weights=True)\n", "    \n", "    # Train the model\n", "    history = model.fit(\n", "        x_train, y_train,\n", "        epochs=100,\n", "        batch_size=hyperparams['batch_size'],\n", "        validation_split=0.2,\n", "        callbacks=[early_stopping],\n", "        verbose=0\n", "    )\n", "    \n", "    # Evaluate on the validation set\n", "    val_predictions = model.predict(x_test)\n", "    val_rmse = sqrt(mean_squared_error(y_test, val_predictions))\n", "    \n", "    return val_rmse"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Initialize the PSO optimizer\n", "pso = PSOOptimizer(\n", "    bounds=hyperparameter_bounds,\n", "    num_particles=10,\n", "    max_iterations=20,\n", "    inertia_weight=0.5,\n", "    cognitive_weight=1.5,\n", "    social_weight=1.5\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:From c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\backend\\common\\global_state.py:82: The name tf.reset_default_graph is deprecated. Please use tf.compat.v1.reset_default_graph instead.\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 69ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 99ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:5 out of the last 5 calls to <function TensorFlowTrainer.make_predict_function.<locals>.one_step_on_data_distributed at 0x00000209471C1080> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.\n", "\u001b[1m1/2\u001b[0m \u001b[32m━━━━━━━━━━\u001b[0m\u001b[37m━━━━━━━━━━\u001b[0m \u001b[1m0s\u001b[0m 46ms/stepWARNING:tensorflow:6 out of the last 6 calls to <function TensorFlowTrainer.make_predict_function.<locals>.one_step_on_data_distributed at 0x00000209471C1080> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 61ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 64ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 78ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 62ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 83ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 68ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 81ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 59ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 69ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 75ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n", "Iteration 1/20, Best Fitness: 0.007456, Time: 1020.21s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 93ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 65ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 60ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 59ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n", "Iteration 2/20, Best Fitness: 0.006520, Time: 1398.42s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 61ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 59ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 58ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n", "Iteration 3/20, Best Fitness: 0.006520, Time: 1653.76s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 58ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n", "Iteration 4/20, Best Fitness: 0.006461, Time: 1918.89s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n", "Iteration 5/20, Best Fitness: 0.006461, Time: 2174.61s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 67ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step\n", "Iteration 6/20, Best Fitness: 0.006461, Time: 2436.92s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 59ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 58ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n", "Iteration 7/20, Best Fitness: 0.006460, Time: 2710.23s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n", "Iteration 8/20, Best Fitness: 0.006460, Time: 3004.51s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 58ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 58ms/step\n", "Iteration 9/20, Best Fitness: 0.006460, Time: 3311.47s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n", "Iteration 10/20, Best Fitness: 0.006427, Time: 3596.61s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n", "Iteration 11/20, Best Fitness: 0.006365, Time: 3900.47s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 58ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n", "Iteration 12/20, Best Fitness: 0.006365, Time: 4185.64s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 63ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n", "Iteration 13/20, Best Fitness: 0.006365, Time: 4490.73s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 62ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n", "Iteration 14/20, Best Fitness: 0.006337, Time: 4778.25s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 86ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 73ms/step\n", "Iteration 15/20, Best Fitness: 0.006337, Time: 5092.67s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 58ms/step\n", "Iteration 16/20, Best Fitness: 0.006337, Time: 5393.91s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 65ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n", "Iteration 17/20, Best Fitness: 0.006337, Time: 5695.85s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 59ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n", "Iteration 18/20, Best Fitness: 0.006337, Time: 5994.47s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 64ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 74ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n", "Iteration 19/20, Best Fitness: 0.006335, Time: 6303.52s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 61ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n", "Iteration 20/20, Best Fitness: 0.006335, Time: 6613.25s\n"]}], "source": ["# Run the PSO optimization\n", "best_hyperparams, best_fitness, fitness_history = pso.optimize(fitness_function)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best Hyperparameters:\n", "num_conv_layers: 1\n", "filters_layer_1: 16\n", "filters_layer_2: 177\n", "filters_layer_3: 396\n", "kernel_size_layer_1: 4\n", "kernel_size_layer_2: 2\n", "kernel_size_layer_3: 2\n", "use_pooling: 0\n", "pool_size: 2\n", "num_dense_layers: 1\n", "dense_neurons_layer_1: 201\n", "dense_neurons_layer_2: 61\n", "dense_neurons_layer_3: 37\n", "learning_rate: 0.0026133680333501456\n", "batch_size: 26\n", "use_dropout: 0\n", "dropout_rate: 0.13453309939928465\n", "activation_choice: 0\n", "\n", "Best Fitness (RMSE): 0.006335\n"]}], "source": ["# Print the best hyperparameters and fitness\n", "print(\"Best Hyperparameters:\")\n", "for param, value in best_hyperparams.items():\n", "    print(f\"{param}: {value}\")\n", "print(f\"\\nBest Fitness (RMSE): {best_fitness:.6f}\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot the optimization progress\n", "pso.plot_progress(\"PSO Optimization Progress for CNN Univariate Model\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\reshaping\\reshape.py:39: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n", "c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"sequential\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"sequential\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                    </span>┃<span style=\"font-weight: bold\"> Output Shape           </span>┃<span style=\"font-weight: bold\">       Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ reshape (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Reshape</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">50</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1</span>)          │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv1d (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv1D</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">50</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">16</span>)         │            <span style=\"color: #00af00; text-decoration-color: #00af00\">80</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Flatten</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">800</span>)            │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">201</span>)            │       <span style=\"color: #00af00; text-decoration-color: #00af00\">161,001</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1</span>)              │           <span style=\"color: #00af00; text-decoration-color: #00af00\">202</span> │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape          \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ reshape (\u001b[38;5;33m<PERSON><PERSON><PERSON><PERSON>\u001b[0m)               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m50\u001b[0m, \u001b[38;5;34m1\u001b[0m)          │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv1d (\u001b[38;5;33mConv1D\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m50\u001b[0m, \u001b[38;5;34m16\u001b[0m)         │            \u001b[38;5;34m80\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten (\u001b[38;5;33m<PERSON><PERSON><PERSON>\u001b[0m)               │ (\u001b[38;5;45m<PERSON>one\u001b[0m, \u001b[38;5;34m800\u001b[0m)            │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (\u001b[38;5;33mDense\u001b[0m)                   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m201\u001b[0m)            │       \u001b[38;5;34m161,001\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m1\u001b[0m)              │           \u001b[38;5;34m202\u001b[0m │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">161,283</span> (630.01 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m161,283\u001b[0m (630.01 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">161,283</span> (630.01 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m161,283\u001b[0m (630.01 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Train the final model with the best hyperparameters\n", "best_hyperparams['activation'] = get_activation(best_hyperparams['activation_choice'])\n", "best_hyperparams['use_dropout'] = bool(int(best_hyperparams['use_dropout']))\n", "best_hyperparams['use_pooling'] = bool(int(best_hyperparams['use_pooling']))\n", "\n", "# Convert integer parameters\n", "for param in ['batch_size', 'num_conv_layers', 'num_dense_layers', \n", "              'kernel_size_layer_1', 'kernel_size_layer_2', 'kernel_size_layer_3',\n", "              'pool_size']:\n", "    if param in best_hyperparams:\n", "        best_hyperparams[param] = int(best_hyperparams[param])\n", "\n", "# Create and compile the model\n", "final_model = create_cnn_model(best_hyperparams, (x_train.shape[1],))\n", "final_model.summary()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 3ms/step - loss: 0.8390 - mae: 0.5031 - val_loss: 0.0042 - val_mae: 0.0592\n", "Epoch 2/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 0.0017 - mae: 0.0309 - val_loss: 0.0014 - val_mae: 0.0314\n", "Epoch 3/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 0.0013 - mae: 0.0272 - val_loss: 0.0023 - val_mae: 0.0429\n", "Epoch 4/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 0.0012 - mae: 0.0255 - val_loss: 5.0568e-04 - val_mae: 0.0170\n", "Epoch 5/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 0.0011 - mae: 0.0258 - val_loss: 5.4812e-04 - val_mae: 0.0183\n", "Epoch 6/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 8.2561e-04 - mae: 0.0219 - val_loss: 7.5230e-04 - val_mae: 0.0228\n", "Epoch 7/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 8.0376e-04 - mae: 0.0224 - val_loss: 0.0012 - val_mae: 0.0309\n", "Epoch 8/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.6125e-04 - mae: 0.0203 - val_loss: 6.4209e-04 - val_mae: 0.0211\n", "Epoch 9/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.2307e-04 - mae: 0.0192 - val_loss: 0.0019 - val_mae: 0.0407\n", "Epoch 10/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.0545e-04 - mae: 0.0189 - val_loss: 3.0134e-04 - val_mae: 0.0132\n", "Epoch 11/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.4267e-04 - mae: 0.0198 - val_loss: 6.5331e-04 - val_mae: 0.0218\n", "Epoch 12/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.1444e-04 - mae: 0.0173 - val_loss: 2.2366e-04 - val_mae: 0.0113\n", "Epoch 13/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.5827e-04 - mae: 0.0188 - val_loss: 5.7813e-04 - val_mae: 0.0209\n", "Epoch 14/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.8825e-04 - mae: 0.0189 - val_loss: 1.8657e-04 - val_mae: 0.0102\n", "Epoch 15/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.9175e-04 - mae: 0.0151 - val_loss: 2.0200e-04 - val_mae: 0.0107\n", "Epoch 16/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.4355e-04 - mae: 0.0201 - val_loss: 4.1841e-04 - val_mae: 0.0170\n", "Epoch 17/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0601e-04 - mae: 0.0158 - val_loss: 1.7218e-04 - val_mae: 0.0097\n", "Epoch 18/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.1709e-04 - mae: 0.0201 - val_loss: 1.8623e-04 - val_mae: 0.0103\n", "Epoch 19/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.0834e-04 - mae: 0.0177 - val_loss: 6.8717e-04 - val_mae: 0.0237\n", "Epoch 20/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.0821e-04 - mae: 0.0192 - val_loss: 1.6483e-04 - val_mae: 0.0095\n", "Epoch 21/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.7904e-04 - mae: 0.0175 - val_loss: 9.0297e-04 - val_mae: 0.0276\n", "Epoch 22/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.9407e-04 - mae: 0.0193 - val_loss: 2.6846e-04 - val_mae: 0.0131\n", "Epoch 23/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.5066e-04 - mae: 0.0162 - val_loss: 3.6397e-04 - val_mae: 0.0158\n", "Epoch 24/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.6360e-04 - mae: 0.0205 - val_loss: 2.9918e-04 - val_mae: 0.0141\n", "Epoch 25/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.5770e-04 - mae: 0.0146 - val_loss: 1.4430e-04 - val_mae: 0.0090\n", "Epoch 26/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.9864e-04 - mae: 0.0172 - val_loss: 1.3862e-04 - val_mae: 0.0088\n", "Epoch 27/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.7488e-04 - mae: 0.0168 - val_loss: 0.0013 - val_mae: 0.0350\n", "Epoch 28/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 9.4606e-04 - mae: 0.0240 - val_loss: 1.3704e-04 - val_mae: 0.0087\n", "Epoch 29/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.9796e-04 - mae: 0.0177 - val_loss: 1.2822e-04 - val_mae: 0.0084\n", "Epoch 30/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.8649e-04 - mae: 0.0172 - val_loss: 3.4251e-04 - val_mae: 0.0158\n", "Epoch 31/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.3915e-04 - mae: 0.0162 - val_loss: 1.3175e-04 - val_mae: 0.0085\n", "Epoch 32/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.7195e-04 - mae: 0.0193 - val_loss: 5.2569e-04 - val_mae: 0.0203\n", "Epoch 33/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.5972e-04 - mae: 0.0189 - val_loss: 4.5842e-04 - val_mae: 0.0191\n", "Epoch 34/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.7914e-04 - mae: 0.0131 - val_loss: 1.1541e-04 - val_mae: 0.0080\n", "Epoch 35/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.7668e-04 - mae: 0.0151 - val_loss: 3.1274e-04 - val_mae: 0.0151\n", "Epoch 36/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.9085e-04 - mae: 0.0177 - val_loss: 2.9212e-04 - val_mae: 0.0147\n", "Epoch 37/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.5733e-04 - mae: 0.0189 - val_loss: 0.0012 - val_mae: 0.0326\n", "Epoch 38/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.5962e-04 - mae: 0.0149 - val_loss: 1.0704e-04 - val_mae: 0.0078\n", "Epoch 39/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.0470e-04 - mae: 0.0136 - val_loss: 0.0011 - val_mae: 0.0323\n", "Epoch 40/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.8559e-04 - mae: 0.0177 - val_loss: 1.0999e-04 - val_mae: 0.0079\n", "Epoch 41/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.7953e-04 - mae: 0.0130 - val_loss: 3.0388e-04 - val_mae: 0.0150\n", "Epoch 42/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.5738e-04 - mae: 0.0169 - val_loss: 1.9746e-04 - val_mae: 0.0117\n", "Epoch 43/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.9359e-04 - mae: 0.0157 - val_loss: 4.9570e-04 - val_mae: 0.0203\n", "Epoch 44/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.8558e-04 - mae: 0.0193 - val_loss: 2.5323e-04 - val_mae: 0.0135\n", "Epoch 45/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.1121e-04 - mae: 0.0138 - val_loss: 8.6970e-05 - val_mae: 0.0070\n", "Epoch 46/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.7146e-04 - mae: 0.0151 - val_loss: 8.2699e-04 - val_mae: 0.0275\n", "Epoch 47/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.6381e-04 - mae: 0.0170 - val_loss: 2.9051e-04 - val_mae: 0.0151\n", "Epoch 48/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.9864e-04 - mae: 0.0135 - val_loss: 5.6496e-04 - val_mae: 0.0222\n", "Epoch 49/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.4748e-04 - mae: 0.0151 - val_loss: 7.7591e-05 - val_mae: 0.0066\n", "Epoch 50/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.0343e-04 - mae: 0.0136 - val_loss: 0.0012 - val_mae: 0.0334\n", "Epoch 51/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.9823e-04 - mae: 0.0180 - val_loss: 7.3025e-05 - val_mae: 0.0064\n", "Epoch 52/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8099e-04 - mae: 0.0131 - val_loss: 2.5614e-04 - val_mae: 0.0141\n", "Epoch 53/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.2158e-04 - mae: 0.0142 - val_loss: 2.3239e-04 - val_mae: 0.0133\n", "Epoch 54/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.4904e-04 - mae: 0.0150 - val_loss: 1.2391e-04 - val_mae: 0.0091\n", "Epoch 55/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.9911e-04 - mae: 0.0109 - val_loss: 7.9773e-05 - val_mae: 0.0068\n", "Epoch 56/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.6835e-04 - mae: 0.0154 - val_loss: 1.0641e-04 - val_mae: 0.0082\n", "Epoch 57/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.2451e-04 - mae: 0.0118 - val_loss: 4.1832e-04 - val_mae: 0.0189\n", "Epoch 58/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8227e-04 - mae: 0.0131 - val_loss: 1.5579e-04 - val_mae: 0.0105\n", "Epoch 59/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4792e-04 - mae: 0.0120 - val_loss: 7.9801e-05 - val_mae: 0.0069\n", "Epoch 60/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8888e-04 - mae: 0.0106 - val_loss: 9.1302e-05 - val_mae: 0.0075\n", "Epoch 61/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.6744e-04 - mae: 0.0147 - val_loss: 1.6242e-04 - val_mae: 0.0108\n", "Epoch 62/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8682e-04 - mae: 0.0133 - val_loss: 1.2975e-04 - val_mae: 0.0094\n", "Epoch 63/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8255e-04 - mae: 0.0136 - val_loss: 9.1672e-05 - val_mae: 0.0076\n", "Epoch 64/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.0923e-04 - mae: 0.0136 - val_loss: 4.8801e-04 - val_mae: 0.0208\n", "Epoch 65/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.6553e-04 - mae: 0.0126 - val_loss: 2.2139e-04 - val_mae: 0.0132\n", "Epoch 66/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.6562e-04 - mae: 0.0154 - val_loss: 1.0759e-04 - val_mae: 0.0084\n", "Epoch 67/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4333e-04 - mae: 0.0120 - val_loss: 1.0221e-04 - val_mae: 0.0082\n", "Epoch 68/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0545e-04 - mae: 0.0166 - val_loss: 1.2825e-04 - val_mae: 0.0094\n", "Epoch 69/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7863e-04 - mae: 0.0101 - val_loss: 1.7498e-04 - val_mae: 0.0113\n", "Epoch 70/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.7824e-04 - mae: 0.0131 - val_loss: 3.2739e-04 - val_mae: 0.0166\n", "Epoch 71/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0052e-04 - mae: 0.0164 - val_loss: 1.6058e-04 - val_mae: 0.0107\n", "Epoch 72/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4188e-04 - mae: 0.0121 - val_loss: 1.0720e-04 - val_mae: 0.0084\n", "Epoch 73/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.1731e-04 - mae: 0.0114 - val_loss: 9.7205e-05 - val_mae: 0.0079\n", "Epoch 74/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8946e-04 - mae: 0.0108 - val_loss: 1.0694e-04 - val_mae: 0.0084\n", "Epoch 75/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4173e-04 - mae: 0.0122 - val_loss: 2.1047e-04 - val_mae: 0.0128\n", "Epoch 76/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.5558e-04 - mae: 0.0151 - val_loss: 1.0211e-04 - val_mae: 0.0082\n", "Epoch 77/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.5191e-04 - mae: 0.0095 - val_loss: 1.7253e-04 - val_mae: 0.0113\n", "Epoch 78/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.1795e-04 - mae: 0.0114 - val_loss: 2.2131e-04 - val_mae: 0.0130\n", "Epoch 79/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.9670e-04 - mae: 0.0111 - val_loss: 3.9754e-04 - val_mae: 0.0185\n", "Epoch 80/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.3008e-04 - mae: 0.0118 - val_loss: 3.7808e-04 - val_mae: 0.0178\n", "Epoch 81/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.0951e-04 - mae: 0.0110 - val_loss: 7.7182e-05 - val_mae: 0.0068\n", "Epoch 82/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.1010e-04 - mae: 0.0113 - val_loss: 4.8677e-04 - val_mae: 0.0208\n", "Epoch 83/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4928e-04 - mae: 0.0122 - val_loss: 1.6609e-04 - val_mae: 0.0111\n", "Epoch 84/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7436e-04 - mae: 0.0103 - val_loss: 1.9511e-04 - val_mae: 0.0121\n", "Epoch 85/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.6933e-04 - mae: 0.0102 - val_loss: 1.1910e-04 - val_mae: 0.0090\n", "Epoch 86/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.9375e-04 - mae: 0.0137 - val_loss: 7.5594e-05 - val_mae: 0.0068\n", "Epoch 87/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.3495e-04 - mae: 0.0117 - val_loss: 5.0384e-04 - val_mae: 0.0212\n", "Epoch 88/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.3259e-04 - mae: 0.0122 - val_loss: 6.2189e-05 - val_mae: 0.0060\n", "Epoch 89/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.9303e-04 - mae: 0.0136 - val_loss: 3.1900e-04 - val_mae: 0.0163\n", "Epoch 90/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.6911e-04 - mae: 0.0103 - val_loss: 7.7162e-05 - val_mae: 0.0069\n", "Epoch 91/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7264e-04 - mae: 0.0104 - val_loss: 1.1387e-04 - val_mae: 0.0088\n", "Epoch 92/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8474e-04 - mae: 0.0107 - val_loss: 8.5496e-05 - val_mae: 0.0073\n", "Epoch 93/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7451e-04 - mae: 0.0103 - val_loss: 4.0385e-04 - val_mae: 0.0187\n", "Epoch 94/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.6454e-04 - mae: 0.0130 - val_loss: 8.0919e-05 - val_mae: 0.0071\n", "Epoch 95/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.1808e-04 - mae: 0.0115 - val_loss: 6.8234e-05 - val_mae: 0.0064\n", "Epoch 96/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8643e-04 - mae: 0.0107 - val_loss: 2.9794e-04 - val_mae: 0.0158\n", "Epoch 97/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7980e-04 - mae: 0.0104 - val_loss: 5.9601e-05 - val_mae: 0.0058\n", "Epoch 98/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7.1453e-04 - mae: 0.0204 - val_loss: 7.3108e-05 - val_mae: 0.0067\n", "Epoch 99/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7917e-04 - mae: 0.0104 - val_loss: 2.1985e-04 - val_mae: 0.0131\n", "Epoch 100/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.5684e-04 - mae: 0.0169 - val_loss: 2.7117e-04 - val_mae: 0.0147\n", "Epoch 101/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.6299e-04 - mae: 0.0097 - val_loss: 1.2606e-04 - val_mae: 0.0093\n", "Epoch 102/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.4213e-04 - mae: 0.0092 - val_loss: 8.1664e-04 - val_mae: 0.0276\n", "Epoch 103/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.2454e-04 - mae: 0.0119 - val_loss: 3.0346e-04 - val_mae: 0.0159\n", "Epoch 104/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.4732e-04 - mae: 0.0096 - val_loss: 2.0790e-04 - val_mae: 0.0126\n", "Epoch 105/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.4547e-04 - mae: 0.0093 - val_loss: 1.4608e-04 - val_mae: 0.0102\n", "Epoch 106/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.0102e-04 - mae: 0.0111 - val_loss: 3.8556e-04 - val_mae: 0.0182\n", "Epoch 107/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.7551e-04 - mae: 0.0154 - val_loss: 5.7024e-05 - val_mae: 0.0057\n", "Epoch 108/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.9432e-04 - mae: 0.0107 - val_loss: 2.4885e-04 - val_mae: 0.0142\n", "Epoch 109/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.5738e-04 - mae: 0.0095 - val_loss: 2.6406e-04 - val_mae: 0.0145\n", "Epoch 110/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.9049e-04 - mae: 0.0106 - val_loss: 1.2058e-04 - val_mae: 0.0091\n", "Epoch 111/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.9502e-04 - mae: 0.0108 - val_loss: 1.5335e-04 - val_mae: 0.0106\n", "Epoch 112/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.6825e-04 - mae: 0.0099 - val_loss: 5.1303e-04 - val_mae: 0.0215\n", "Epoch 113/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.3142e-04 - mae: 0.0149 - val_loss: 1.9014e-04 - val_mae: 0.0120\n", "Epoch 114/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.9105e-04 - mae: 0.0106 - val_loss: 5.8611e-05 - val_mae: 0.0058\n", "Epoch 115/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7287e-04 - mae: 0.0102 - val_loss: 3.3371e-04 - val_mae: 0.0168\n", "Epoch 116/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.0794e-04 - mae: 0.0114 - val_loss: 2.5409e-04 - val_mae: 0.0143\n", "Epoch 117/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.3677e-04 - mae: 0.0121 - val_loss: 5.8792e-05 - val_mae: 0.0058\n", "Epoch 118/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7046e-04 - mae: 0.0103 - val_loss: 6.9355e-05 - val_mae: 0.0064\n", "Epoch 119/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.1566e-04 - mae: 0.0119 - val_loss: 7.0275e-05 - val_mae: 0.0065\n", "Epoch 120/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8919e-04 - mae: 0.0107 - val_loss: 1.9874e-04 - val_mae: 0.0123\n", "Epoch 121/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.5066e-04 - mae: 0.0093 - val_loss: 6.2803e-05 - val_mae: 0.0060\n", "Epoch 122/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7243e-04 - mae: 0.0103 - val_loss: 7.6568e-05 - val_mae: 0.0069\n", "Epoch 123/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.6514e-04 - mae: 0.0101 - val_loss: 6.4906e-04 - val_mae: 0.0243\n", "Epoch 124/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8674e-04 - mae: 0.0136 - val_loss: 7.4477e-05 - val_mae: 0.0067\n", "Epoch 125/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.5600e-04 - mae: 0.0130 - val_loss: 1.9339e-04 - val_mae: 0.0122\n", "Epoch 126/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.9082e-04 - mae: 0.0112 - val_loss: 4.9287e-04 - val_mae: 0.0208\n", "Epoch 127/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.0314e-04 - mae: 0.0113 - val_loss: 2.1065e-04 - val_mae: 0.0128\n", "Epoch 128/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.1556e-04 - mae: 0.0118 - val_loss: 1.4482e-04 - val_mae: 0.0101\n", "Epoch 129/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.4832e-04 - mae: 0.0094 - val_loss: 1.3901e-04 - val_mae: 0.0099\n", "Epoch 130/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.3820e-04 - mae: 0.0088 - val_loss: 2.4319e-04 - val_mae: 0.0139\n", "Epoch 131/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7142e-04 - mae: 0.0103 - val_loss: 6.3617e-05 - val_mae: 0.0061\n", "Epoch 132/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7398e-04 - mae: 0.0102 - val_loss: 7.2043e-05 - val_mae: 0.0066\n", "Epoch 133/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8156e-04 - mae: 0.0103 - val_loss: 7.1124e-05 - val_mae: 0.0065\n", "Epoch 134/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7311e-04 - mae: 0.0102 - val_loss: 4.1610e-04 - val_mae: 0.0191\n", "Epoch 135/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4321e-04 - mae: 0.0122 - val_loss: 3.0190e-04 - val_mae: 0.0158\n", "Epoch 136/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.6253e-04 - mae: 0.0098 - val_loss: 7.1492e-05 - val_mae: 0.0065\n", "Epoch 137/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7283e-04 - mae: 0.0100 - val_loss: 1.7492e-04 - val_mae: 0.0114\n", "Epoch 138/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8751e-04 - mae: 0.0107 - val_loss: 5.6215e-04 - val_mae: 0.0225\n", "Epoch 139/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8210e-04 - mae: 0.0106 - val_loss: 6.7990e-05 - val_mae: 0.0063\n", "Epoch 140/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.4815e-04 - mae: 0.0094 - val_loss: 2.2057e-04 - val_mae: 0.0131\n", "Epoch 141/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.0605e-04 - mae: 0.0116 - val_loss: 2.7095e-04 - val_mae: 0.0149\n", "Epoch 142/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4992e-04 - mae: 0.0127 - val_loss: 1.3877e-04 - val_mae: 0.0098\n", "Epoch 143/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.2854e-04 - mae: 0.0085 - val_loss: 5.6983e-04 - val_mae: 0.0227\n", "Epoch 144/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4611e-04 - mae: 0.0125 - val_loss: 7.7949e-05 - val_mae: 0.0069\n", "Epoch 145/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.3381e-04 - mae: 0.0087 - val_loss: 5.9131e-05 - val_mae: 0.0058\n", "Epoch 146/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8814e-04 - mae: 0.0105 - val_loss: 1.5450e-04 - val_mae: 0.0105\n", "Epoch 147/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.5102e-04 - mae: 0.0096 - val_loss: 8.8105e-05 - val_mae: 0.0075\n", "Epoch 148/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.6608e-04 - mae: 0.0098 - val_loss: 8.1376e-05 - val_mae: 0.0071\n", "Epoch 149/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.2988e-04 - mae: 0.0119 - val_loss: 1.9004e-04 - val_mae: 0.0119\n", "Epoch 150/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.3558e-04 - mae: 0.0089 - val_loss: 1.7005e-04 - val_mae: 0.0112\n", "Epoch 151/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.6750e-04 - mae: 0.0100 - val_loss: 1.2151e-04 - val_mae: 0.0091\n", "Epoch 152/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.5712e-04 - mae: 0.0097 - val_loss: 7.8895e-05 - val_mae: 0.0070\n", "Epoch 153/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7028e-04 - mae: 0.0101 - val_loss: 9.0275e-05 - val_mae: 0.0075\n", "Epoch 154/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.5422e-04 - mae: 0.0095 - val_loss: 2.6282e-04 - val_mae: 0.0146\n", "Epoch 155/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7183e-04 - mae: 0.0101 - val_loss: 4.9745e-04 - val_mae: 0.0209\n", "Epoch 156/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7520e-04 - mae: 0.0103 - val_loss: 1.1059e-04 - val_mae: 0.0085\n", "Epoch 157/1000\n", "\u001b[1m149/149\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.4804e-04 - mae: 0.0092 - val_loss: 1.3888e-04 - val_mae: 0.0098\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 13ms/step - loss: 4.4358e-05 - mae: 0.0056\n", "Test MAE: 0.005441992077976465\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step\n", "Test RMSE: 0.006540170802477071\n", "Total time: 54.12981295585632 seconds\n"]}], "source": ["# Train the final model\n", "start_time = time.time()\n", "\n", "early_stopping = EarlyStopping(monitor='val_loss', patience=50, restore_best_weights=True)\n", "\n", "history = final_model.fit(\n", "    x_train, y_train,\n", "    epochs=1000,\n", "    batch_size=best_hyperparams['batch_size'],\n", "    validation_split=0.2,\n", "    callbacks=[early_stopping],\n", "    verbose=1\n", ")\n", "\n", "# Evaluate the model\n", "test_loss, test_mae = final_model.evaluate(x_test, y_test)\n", "print('Test MAE:', test_mae)\n", "\n", "# Calculate RMSE\n", "predictions = final_model.predict(x_test)\n", "rmse = sqrt(mean_squared_error(y_test, predictions))\n", "print('Test RMSE:', rmse)\n", "\n", "# End the timer and print the total time passed\n", "end_time = time.time()\n", "print(\"Total time:\", end_time - start_time, \"seconds\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot the training and validation loss\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(history.history['loss'], label='Training Loss')\n", "plt.plot(history.history['val_loss'], label='Validation Loss')\n", "plt.title('PSO-Optimized CNN Univariate Model: Training and Validation Loss')\n", "plt.xlabel('Epochs')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot the actual vs predicted values\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(y_test, label='Actual')\n", "plt.plot(predictions, label='Predicted')\n", "plt.title(f'PSO-Optimized CNN Univariate Model: Actual vs Predicted (RMSE: {rmse:.5f})')\n", "plt.xlabel('Time Steps')\n", "plt.ylabel('GBP/USD Exchange Rate')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.savefig('PLOTS/PSO_CNN_U.png')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PSO-Optimized CNN Univariate RMSE: 0.006540170802477071\n", "Original CNN Univariate RMSE: 0.03028 (from the paper)\n", "Improvement: 78.40%\n"]}], "source": ["# Compare with the original CNN Univariate model\n", "print(\"PSO-Optimized CNN Univariate RMSE:\", rmse)\n", "print(\"Original CNN Univariate RMSE: 0.03028 (from the paper)\")\n", "improvement = (0.03028 - rmse) / 0.03028 * 100\n", "print(f\"Improvement: {improvement:.2f}%\")"]}], "metadata": {"kernelspec": {"display_name": "exchange", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "orig_nbformat": 2}, "nbformat": 4, "nbformat_minor": 2}