{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PSO-Optimized MLP Univariate Model"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import tensorflow as tf\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import mean_squared_error\n", "import tensorflow as tf\n", "import keras\n", "from tensorflow.keras.backend import sigmoid\n", "from keras.models import Sequential\n", "from keras import models, layers, backend, optimizers\n", "from keras.layers import Dropout, BatchNormalization\n", "from keras.callbacks import EarlyStopping\n", "import time\n", "from math import sqrt\n", "\n", "# Import our PSO optimizer\n", "from pso_optimizer import PSOOptimizer, create_mlp_model"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "Date", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "AUD", "rawType": "float64", "type": "float"}, {"name": "EUR", "rawType": "float64", "type": "float"}, {"name": "NZD", "rawType": "float64", "type": "float"}, {"name": "GBP", "rawType": "float64", "type": "float"}, {"name": "BRL", "rawType": "float64", "type": "float"}, {"name": "CAD", "rawType": "float64", "type": "float"}, {"name": "CNY", "rawType": "float64", "type": "float"}, {"name": "DKK", "rawType": "float64", "type": "float"}, {"name": "HKD", "rawType": "float64", "type": "float"}, {"name": "INR", "rawType": "float64", "type": "float"}, {"name": "JPY", "rawType": "float64", "type": "float"}, {"name": "MYR", "rawType": "float64", "type": "float"}, {"name": "MXN", "rawType": "float64", "type": "float"}, {"name": "NOK", "rawType": "float64", "type": "float"}, {"name": "ZAR", "rawType": "float64", "type": "float"}, {"name": "SGD", "rawType": "float64", "type": "float"}, {"name": "KRW", "rawType": "float64", "type": "float"}, {"name": "LKR", "rawType": "float64", "type": "float"}, {"name": "SEK", "rawType": "float64", "type": "float"}, {"name": "CHF", "rawType": "float64", "type": "float"}, {"name": "TWD", "rawType": "float64", "type": "float"}, {"name": "THB", "rawType": "float64", "type": "float"}, {"name": "VEB", "rawType": "float64", "type": "float"}, {"name": "gdpGBP", "rawType": "float64", "type": "float"}, {"name": "gdpUSD", "rawType": "float64", "type": "float"}, {"name": "GBR_Value", "rawType": "float64", "type": "float"}, {"name": "USA_Value", "rawType": "float64", "type": "float"}, {"name": "liborGBP", "rawType": "float64", "type": "float"}, {"name": "liborUSD", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "76ca6958-5406-4368-a6fa-9a5edde8bd4c", "rows": [["2001-01-02 00:00:00", "0.5592", "0.9465", "0.4432", "1.4977", "1.938", "1.4963", "8.2779", "7.8845", "7.8", "46.74", "114.73", "3.8", "9.714", "8.76", "7.569", "1.737", "1279.0", "82.6", "9.415", "1.6075", "33.0", "43.79", "0.7008", "99.97764281514756", "100.81527694707992", "-1.815904030756699", "-4.084297260226566", "5.81094", "6.65125"], ["2001-01-03 00:00:00", "0.5635", "0.9473", "0.4463", "1.5045", "1.946", "1.4982", "8.2773", "7.875", "7.8", "46.75", "114.26", "3.8", "9.836", "8.76", "7.5025", "1.7325", "1271.5", "82.7", "9.445", "1.6025", "33.078", "43.7", "0.7002", "99.98086597817554", "100.80719091285982", "-1.824859470775798", "-4.076653803865071", "6.0975", "6.65375"], ["2001-01-04 00:00:00", "0.5655", "0.9448", "0.4457", "1.493", "1.938", "1.4985", "8.2781", "7.8991", "7.7998", "46.78", "115.47", "3.8", "9.698", "8.7628", "7.542999999999999", "1.737", "1265.0", "82.75", "9.422", "1.6115", "33.0", "43.53", "0.6994", "99.98408914120353", "100.79910487863974", "-1.833814910794897", "-4.069010347503577", "5.57125", "6.09625"], ["2001-01-05 00:00:00", "0.5712", "0.9535", "0.4518", "1.499", "1.953", "1.5003", "8.2775", "7.826", "7.7993", "46.76", "116.19", "3.8", "9.753", "8.701", "7.56", "1.7322", "1264.7", "83.15", "9.338", "1.6025", "32.927", "43.26", "0.6988", "99.9873123042315", "100.79101884441964", "-1.8427703508139963", "-4.0613668911420815", "5.3781300000000005", "6.01625"], ["2001-01-08 00:00:00", "0.5660000000000001", "0.9486", "0.4505", "1.4969", "1.954", "1.4944", "8.2778", "7.8705", "7.7998", "46.73", "115.97", "3.8", "9.691", "8.7435", "7.595", "1.7275", "1263.6", "82.95", "9.418", "1.6076", "32.85", "42.95", "0.6990000000000001", "99.99053546725946", "100.78293281019955", "-1.8517257908330955", "-4.053723434780587", "5.5", "6.015"]], "shape": {"columns": 29, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>AUD</th>\n", "      <th>EUR</th>\n", "      <th>NZD</th>\n", "      <th>GBP</th>\n", "      <th>BRL</th>\n", "      <th>CAD</th>\n", "      <th>CNY</th>\n", "      <th>DKK</th>\n", "      <th>HKD</th>\n", "      <th>INR</th>\n", "      <th>...</th>\n", "      <th>CHF</th>\n", "      <th>TWD</th>\n", "      <th>THB</th>\n", "      <th>VEB</th>\n", "      <th>gdpGBP</th>\n", "      <th>gdpUSD</th>\n", "      <th>GBR_Value</th>\n", "      <th>USA_Value</th>\n", "      <th>liborGBP</th>\n", "      <th>liborUSD</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2001-01-02</th>\n", "      <td>0.5592</td>\n", "      <td>0.9465</td>\n", "      <td>0.4432</td>\n", "      <td>1.4977</td>\n", "      <td>1.938</td>\n", "      <td>1.4963</td>\n", "      <td>8.2779</td>\n", "      <td>7.8845</td>\n", "      <td>7.8000</td>\n", "      <td>46.74</td>\n", "      <td>...</td>\n", "      <td>1.6075</td>\n", "      <td>33.000</td>\n", "      <td>43.79</td>\n", "      <td>0.7008</td>\n", "      <td>99.977643</td>\n", "      <td>100.815277</td>\n", "      <td>-1.815904</td>\n", "      <td>-4.084297</td>\n", "      <td>5.81094</td>\n", "      <td>6.65125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2001-01-03</th>\n", "      <td>0.5635</td>\n", "      <td>0.9473</td>\n", "      <td>0.4463</td>\n", "      <td>1.5045</td>\n", "      <td>1.946</td>\n", "      <td>1.4982</td>\n", "      <td>8.2773</td>\n", "      <td>7.8750</td>\n", "      <td>7.8000</td>\n", "      <td>46.75</td>\n", "      <td>...</td>\n", "      <td>1.6025</td>\n", "      <td>33.078</td>\n", "      <td>43.70</td>\n", "      <td>0.7002</td>\n", "      <td>99.980866</td>\n", "      <td>100.807191</td>\n", "      <td>-1.824859</td>\n", "      <td>-4.076654</td>\n", "      <td>6.09750</td>\n", "      <td>6.65375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2001-01-04</th>\n", "      <td>0.5655</td>\n", "      <td>0.9448</td>\n", "      <td>0.4457</td>\n", "      <td>1.4930</td>\n", "      <td>1.938</td>\n", "      <td>1.4985</td>\n", "      <td>8.2781</td>\n", "      <td>7.8991</td>\n", "      <td>7.7998</td>\n", "      <td>46.78</td>\n", "      <td>...</td>\n", "      <td>1.6115</td>\n", "      <td>33.000</td>\n", "      <td>43.53</td>\n", "      <td>0.6994</td>\n", "      <td>99.984089</td>\n", "      <td>100.799105</td>\n", "      <td>-1.833815</td>\n", "      <td>-4.069010</td>\n", "      <td>5.57125</td>\n", "      <td>6.09625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2001-01-05</th>\n", "      <td>0.5712</td>\n", "      <td>0.9535</td>\n", "      <td>0.4518</td>\n", "      <td>1.4990</td>\n", "      <td>1.953</td>\n", "      <td>1.5003</td>\n", "      <td>8.2775</td>\n", "      <td>7.8260</td>\n", "      <td>7.7993</td>\n", "      <td>46.76</td>\n", "      <td>...</td>\n", "      <td>1.6025</td>\n", "      <td>32.927</td>\n", "      <td>43.26</td>\n", "      <td>0.6988</td>\n", "      <td>99.987312</td>\n", "      <td>100.791019</td>\n", "      <td>-1.842770</td>\n", "      <td>-4.061367</td>\n", "      <td>5.37813</td>\n", "      <td>6.01625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2001-01-08</th>\n", "      <td>0.5660</td>\n", "      <td>0.9486</td>\n", "      <td>0.4505</td>\n", "      <td>1.4969</td>\n", "      <td>1.954</td>\n", "      <td>1.4944</td>\n", "      <td>8.2778</td>\n", "      <td>7.8705</td>\n", "      <td>7.7998</td>\n", "      <td>46.73</td>\n", "      <td>...</td>\n", "      <td>1.6076</td>\n", "      <td>32.850</td>\n", "      <td>42.95</td>\n", "      <td>0.6990</td>\n", "      <td>99.990535</td>\n", "      <td>100.782933</td>\n", "      <td>-1.851726</td>\n", "      <td>-4.053723</td>\n", "      <td>5.50000</td>\n", "      <td>6.01500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 29 columns</p>\n", "</div>"], "text/plain": ["               AUD     EUR     NZD     GBP    BRL     CAD     CNY     DKK  \\\n", "Date                                                                        \n", "2001-01-02  0.5592  0.9465  0.4432  1.4977  1.938  1.4963  8.2779  7.8845   \n", "2001-01-03  0.5635  0.9473  0.4463  1.5045  1.946  1.4982  8.2773  7.8750   \n", "2001-01-04  0.5655  0.9448  0.4457  1.4930  1.938  1.4985  8.2781  7.8991   \n", "2001-01-05  0.5712  0.9535  0.4518  1.4990  1.953  1.5003  8.2775  7.8260   \n", "2001-01-08  0.5660  0.9486  0.4505  1.4969  1.954  1.4944  8.2778  7.8705   \n", "\n", "               HKD    INR  ...     CHF     TWD    THB     VEB     gdpGBP  \\\n", "Date                       ...                                             \n", "2001-01-02  7.8000  46.74  ...  1.6075  33.000  43.79  0.7008  99.977643   \n", "2001-01-03  7.8000  46.75  ...  1.6025  33.078  43.70  0.7002  99.980866   \n", "2001-01-04  7.7998  46.78  ...  1.6115  33.000  43.53  0.6994  99.984089   \n", "2001-01-05  7.7993  46.76  ...  1.6025  32.927  43.26  0.6988  99.987312   \n", "2001-01-08  7.7998  46.73  ...  1.6076  32.850  42.95  0.6990  99.990535   \n", "\n", "                gdpUSD  GBR_Value  USA_Value  liborGBP  liborUSD  \n", "Date                                                              \n", "2001-01-02  100.815277  -1.815904  -4.084297   5.81094   6.65125  \n", "2001-01-03  100.807191  -1.824859  -4.076654   6.09750   6.65375  \n", "2001-01-04  100.799105  -1.833815  -4.069010   5.57125   6.09625  \n", "2001-01-05  100.791019  -1.842770  -4.061367   5.37813   6.01625  \n", "2001-01-08  100.782933  -1.851726  -4.053723   5.50000   6.01500  \n", "\n", "[5 rows x 29 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# we read our dataframe and assign the time column as the index values of the dataframe\n", "file = \"./DATA/Merged.csv\"\n", "df = pd.read_csv(file, index_col='Date', parse_dates=True)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def CreateLaggedSequence(data, lag):\n", "    # make two lists for PredictorSequences and ResponseVariables\n", "    PredictorSequences, ResponseVariables = list(), list()\n", "    for i in range(len(data)):\n", "        # mark the range of the sequence\n", "        end_i = i + lag\n", "        # check when the data ends\n", "        if end_i+1 > len(data):\n", "            # stop sequence creation\n", "            break\n", "        # get the predictors and responses\n", "        PredictorSequence = data[i:end_i]\n", "        ResponseVariable = data[end_i]\n", "        # append them to the lists\n", "        PredictorSequences.append(PredictorSequence)\n", "        ResponseVariables.append(ResponseVariable)\n", "        # print(end_i)\n", "    return np.array(PredictorSequences), np.array(ResponseVariables)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["((4870, 50), (4870,), 4870, 4870)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the univariate series\n", "X = df['GBP']\n", "np_X = np.array(X)\n", "# Create the lagged values for the series (50 lags)\n", "X, y = CreateLaggedSequence(np_X, 50)\n", "lag = X.shape[1]\n", "# Reshape it for the process\n", "X = X.reshape(X.shape[0], X.shape[1]) \n", "X.shape, y.shape, len(X), len(y)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["((4820, 50), (50, 50), (4820,), (50,))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# split the train and test sets (last 50 observation spared for the test)\n", "x_train, x_test = X[:-50], X[-50:]\n", "y_train, y_test = y[:-50], y[-50:]\n", "x_train.shape, x_test.shape, y_train.shape, y_test.shape"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Define a new activation function\n", "# <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2018, August 19). Swish Activation Function by Google. Retrieved from https://medium.com/@neuralnets/swish-activation-function-by-google-53e1ea86f820\n", "\n", "def swish(x, beta = 1):\n", "    return (x * sigmoid(beta * x))\n", "\n", "from tensorflow.keras.utils import get_custom_objects\n", "from keras.layers import Activation\n", "get_custom_objects().update({'swish': Activation(swish)})"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Define the hyperparameter search space for PSO\n", "hyperparameter_bounds = {\n", "    'num_layers': (2, 5),                # Number of hidden layers\n", "    'neurons_layer_1': (32, 768),        # Neurons in first hidden layer\n", "    'neurons_layer_2': (32, 384),        # Neurons in second hidden layer\n", "    'neurons_layer_3': (16, 192),        # Neurons in third hidden layer\n", "    'neurons_layer_4': (8, 96),          # Neurons in fourth hidden layer\n", "    'neurons_layer_5': (4, 48),          # Neurons in fifth hidden layer\n", "    'learning_rate': (0.0001, 0.01),     # Learning rate\n", "    'batch_size': (16, 128),             # Batch size\n", "    'use_dropout': (0, 1),               # Whether to use dropout (0=No, 1=Yes)\n", "    'dropout_rate': (0.1, 0.5),          # Dropout rate\n", "    'activation_choice': (0, 2)          # 0=swish, 1=relu, 2=tanh\n", "}"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Note: Activation function mapping is now handled directly in the model creation functions"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Define the fitness function for PSO\n", "def fitness_function(hyperparams):\n", "    # Convert use_dropout to boolean\n", "    hyperparams['use_dropout'] = bool(int(hyperparams['use_dropout']))\n", "    \n", "    # Convert batch_size to integer\n", "    batch_size = int(hyperparams['batch_size'])\n", "    \n", "    # Create and compile the model\n", "    model = create_mlp_model(hyperparams, (x_train.shape[1],))\n", "    \n", "    # Use a validation split for early stopping\n", "    early_stopping = EarlyStopping(monitor='val_loss', patience=20, restore_best_weights=True)\n", "    \n", "    # Train the model\n", "    history = model.fit(\n", "        x_train, y_train,\n", "        epochs=100,\n", "        batch_size=batch_size,\n", "        validation_split=0.2,\n", "        callbacks=[early_stopping],\n", "        verbose=0\n", "    )\n", "    \n", "    # Evaluate on the validation set\n", "    val_predictions = model.predict(x_test)\n", "    val_rmse = sqrt(mean_squared_error(y_test, val_predictions))\n", "    \n", "    return val_rmse"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Initialize the PSO optimizer\n", "pso = PSOOptimizer(\n", "    bounds=hyperparameter_bounds,\n", "    num_particles=10,\n", "    max_iterations=20,\n", "    inertia_weight=0.5,\n", "    cognitive_weight=1.5,\n", "    social_weight=1.5\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:From c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\backend\\common\\global_state.py:82: The name tf.reset_default_graph is deprecated. Please use tf.compat.v1.reset_default_graph instead.\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 38ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:5 out of the last 5 calls to <function TensorFlowTrainer.make_predict_function.<locals>.one_step_on_data_distributed at 0x000001592BC7CCC0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.\n", "\u001b[1m1/2\u001b[0m \u001b[32m━━━━━━━━━━\u001b[0m\u001b[37m━━━━━━━━━━\u001b[0m \u001b[1m0s\u001b[0m 30ms/stepWARNING:tensorflow:6 out of the last 6 calls to <function TensorFlowTrainer.make_predict_function.<locals>.one_step_on_data_distributed at 0x000001592BC7CCC0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 32ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 31ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 37ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 75ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 38ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 38ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 36ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 35ms/step\n", "Iteration 1/20, Best Fitness: 0.008325, Time: 265.06s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 38ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 34ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 35ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 38ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n", "Iteration 2/20, Best Fitness: 0.007917, Time: 531.54s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 38ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 35ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 37ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 38ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 35ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n", "Iteration 3/20, Best Fitness: 0.007050, Time: 857.77s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 36ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 37ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 38ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 36ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 37ms/step\n", "Iteration 4/20, Best Fitness: 0.007050, Time: 1211.49s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 38ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 34ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 35ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 35ms/step\n", "Iteration 5/20, Best Fitness: 0.006852, Time: 1599.93s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 36ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 37ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 34ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n", "Iteration 6/20, Best Fitness: 0.006852, Time: 1945.16s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 58ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n", "Iteration 7/20, Best Fitness: 0.006698, Time: 2394.77s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 37ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 36ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step\n", "Iteration 8/20, Best Fitness: 0.006698, Time: 2817.20s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n", "Iteration 9/20, Best Fitness: 0.006698, Time: 3273.82s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 59ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n", "Iteration 10/20, Best Fitness: 0.006698, Time: 3661.65s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n", "Iteration 11/20, Best Fitness: 0.006698, Time: 4139.89s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n", "Iteration 12/20, Best Fitness: 0.006698, Time: 4594.67s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 67ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n", "Iteration 13/20, Best Fitness: 0.006698, Time: 5042.06s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 38ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n", "Iteration 14/20, Best Fitness: 0.006698, Time: 5413.94s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 36ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 36ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 70ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step\n", "Iteration 15/20, Best Fitness: 0.006698, Time: 5829.21s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n", "Iteration 16/20, Best Fitness: 0.006698, Time: 6342.09s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 64ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n", "Iteration 17/20, Best Fitness: 0.006698, Time: 6786.34s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 73ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n", "Iteration 18/20, Best Fitness: 0.006657, Time: 7220.17s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 58ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 37ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step\n", "Iteration 19/20, Best Fitness: 0.006657, Time: 7597.90s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 35ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 37ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 35ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 37ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step\n", "Iteration 20/20, Best Fitness: 0.006657, Time: 7962.41s\n"]}], "source": ["# Run the PSO optimization\n", "best_hyperparams, best_fitness, fitness_history = pso.optimize(fitness_function)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best Hyperparameters:\n", "num_layers: 2\n", "neurons_layer_1: 380\n", "neurons_layer_2: 345\n", "neurons_layer_3: 16\n", "neurons_layer_4: 85\n", "neurons_layer_5: 38\n", "learning_rate: 0.0001\n", "batch_size: 16\n", "use_dropout: False\n", "dropout_rate: 0.2626594903692827\n", "activation_choice: 0\n", "\n", "Best Fitness (RMSE): 0.006657\n"]}], "source": ["# Print the best hyperparameters and fitness\n", "print(\"Best Hyperparameters:\")\n", "for param, value in best_hyperparams.items():\n", "    print(f\"{param}: {value}\")\n", "print(f\"\\nBest Fitness (RMSE): {best_fitness:.6f}\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot the optimization progress\n", "pso.plot_progress(\"PSO Optimization Progress for MLP Univariate Model\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\keras\\src\\layers\\core\\dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"sequential\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"sequential\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                    </span>┃<span style=\"font-weight: bold\"> Output Shape           </span>┃<span style=\"font-weight: bold\">       Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ dense (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">380</span>)            │        <span style=\"color: #00af00; text-decoration-color: #00af00\">19,380</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">345</span>)            │       <span style=\"color: #00af00; text-decoration-color: #00af00\">131,445</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_2 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1</span>)              │           <span style=\"color: #00af00; text-decoration-color: #00af00\">346</span> │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape          \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ dense (\u001b[38;5;33mDense\u001b[0m)                   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m380\u001b[0m)            │        \u001b[38;5;34m19,380\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m345\u001b[0m)            │       \u001b[38;5;34m131,445\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_2 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m1\u001b[0m)              │           \u001b[38;5;34m346\u001b[0m │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">151,171</span> (590.51 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m151,171\u001b[0m (590.51 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">151,171</span> (590.51 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m151,171\u001b[0m (590.51 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Train the final model with the best hyperparameters\n", "best_hyperparams['use_dropout'] = bool(int(best_hyperparams['use_dropout']))\n", "batch_size = int(best_hyperparams['batch_size'])\n", "\n", "# Create and compile the model\n", "final_model = create_mlp_model(best_hyperparams, (x_train.shape[1],))\n", "final_model.summary()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 0.1832 - mae: 0.2371 - val_loss: 0.0013 - val_mae: 0.0287\n", "Epoch 2/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 0.0015 - mae: 0.0300 - val_loss: 7.3758e-04 - val_mae: 0.0206\n", "Epoch 3/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 0.0012 - mae: 0.0273 - val_loss: 6.4533e-04 - val_mae: 0.0195\n", "Epoch 4/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 0.0011 - mae: 0.0255 - val_loss: 6.0356e-04 - val_mae: 0.0193\n", "Epoch 5/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 7.2875e-04 - mae: 0.0208 - val_loss: 3.4652e-04 - val_mae: 0.0141\n", "Epoch 6/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.9259e-04 - mae: 0.0190 - val_loss: 2.7946e-04 - val_mae: 0.0127\n", "Epoch 7/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.3516e-04 - mae: 0.0160 - val_loss: 2.3787e-04 - val_mae: 0.0117\n", "Epoch 8/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.6850e-04 - mae: 0.0166 - val_loss: 3.3880e-04 - val_mae: 0.0149\n", "Epoch 9/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.6658e-04 - mae: 0.0171 - val_loss: 1.8505e-04 - val_mae: 0.0103\n", "Epoch 10/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.7018e-04 - mae: 0.0193 - val_loss: 4.9144e-04 - val_mae: 0.0192\n", "Epoch 11/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.9703e-04 - mae: 0.0210 - val_loss: 6.2238e-04 - val_mae: 0.0223\n", "Epoch 12/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.6427e-04 - mae: 0.0170 - val_loss: 3.7448e-04 - val_mae: 0.0167\n", "Epoch 13/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.4665e-04 - mae: 0.0182 - val_loss: 1.4961e-04 - val_mae: 0.0093\n", "Epoch 14/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7.8253e-04 - mae: 0.0223 - val_loss: 1.3155e-04 - val_mae: 0.0086\n", "Epoch 15/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.4394e-04 - mae: 0.0166 - val_loss: 8.3313e-04 - val_mae: 0.0268\n", "Epoch 16/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.2698e-04 - mae: 0.0200 - val_loss: 5.4174e-04 - val_mae: 0.0209\n", "Epoch 17/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0118e-04 - mae: 0.0157 - val_loss: 1.4846e-04 - val_mae: 0.0094\n", "Epoch 18/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.7170e-04 - mae: 0.0201 - val_loss: 2.3481e-04 - val_mae: 0.0126\n", "Epoch 19/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.0846e-04 - mae: 0.0198 - val_loss: 1.5106e-04 - val_mae: 0.0096\n", "Epoch 20/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.9949e-04 - mae: 0.0190 - val_loss: 1.6962e-04 - val_mae: 0.0103\n", "Epoch 21/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0167e-04 - mae: 0.0155 - val_loss: 1.5988e-04 - val_mae: 0.0099\n", "Epoch 22/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.6021e-04 - mae: 0.0191 - val_loss: 2.5426e-04 - val_mae: 0.0133\n", "Epoch 23/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.4655e-04 - mae: 0.0166 - val_loss: 3.9086e-04 - val_mae: 0.0175\n", "Epoch 24/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.5732e-04 - mae: 0.0201 - val_loss: 3.3795e-04 - val_mae: 0.0159\n", "Epoch 25/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.5209e-04 - mae: 0.0190 - val_loss: 1.0958e-04 - val_mae: 0.0080\n", "Epoch 26/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.9495e-04 - mae: 0.0195 - val_loss: 1.4341e-04 - val_mae: 0.0094\n", "Epoch 27/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.2787e-04 - mae: 0.0201 - val_loss: 2.1169e-04 - val_mae: 0.0120\n", "Epoch 28/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.0589e-04 - mae: 0.0175 - val_loss: 1.1109e-04 - val_mae: 0.0081\n", "Epoch 29/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.3947e-04 - mae: 0.0185 - val_loss: 3.2862e-04 - val_mae: 0.0159\n", "Epoch 30/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.2521e-04 - mae: 0.0203 - val_loss: 1.8083e-04 - val_mae: 0.0109\n", "Epoch 31/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.9685e-04 - mae: 0.0216 - val_loss: 1.0067e-04 - val_mae: 0.0076\n", "Epoch 32/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.0052e-04 - mae: 0.0173 - val_loss: 1.2786e-04 - val_mae: 0.0088\n", "Epoch 33/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0744e-04 - mae: 0.0160 - val_loss: 1.0385e-04 - val_mae: 0.0078\n", "Epoch 34/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.7550e-04 - mae: 0.0173 - val_loss: 3.8436e-04 - val_mae: 0.0173\n", "Epoch 35/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.2351e-04 - mae: 0.0162 - val_loss: 1.1449e-04 - val_mae: 0.0083\n", "Epoch 36/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0678e-04 - mae: 0.0162 - val_loss: 7.6050e-04 - val_mae: 0.0259\n", "Epoch 37/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.6932e-04 - mae: 0.0190 - val_loss: 2.6838e-04 - val_mae: 0.0142\n", "Epoch 38/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.8736e-04 - mae: 0.0212 - val_loss: 1.1798e-04 - val_mae: 0.0085\n", "Epoch 39/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.7090e-04 - mae: 0.0173 - val_loss: 5.8419e-04 - val_mae: 0.0223\n", "Epoch 40/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6.0022e-04 - mae: 0.0200 - val_loss: 1.7028e-04 - val_mae: 0.0106\n", "Epoch 41/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.4910e-04 - mae: 0.0185 - val_loss: 1.4663e-04 - val_mae: 0.0097\n", "Epoch 42/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.9699e-04 - mae: 0.0177 - val_loss: 9.0311e-05 - val_mae: 0.0072\n", "Epoch 43/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.9129e-04 - mae: 0.0157 - val_loss: 3.5269e-04 - val_mae: 0.0166\n", "Epoch 44/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.3758e-04 - mae: 0.0165 - val_loss: 1.1879e-04 - val_mae: 0.0085\n", "Epoch 45/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.7306e-04 - mae: 0.0192 - val_loss: 9.2172e-05 - val_mae: 0.0073\n", "Epoch 46/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.4595e-04 - mae: 0.0167 - val_loss: 2.6382e-04 - val_mae: 0.0142\n", "Epoch 47/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.2875e-04 - mae: 0.0141 - val_loss: 5.8366e-04 - val_mae: 0.0223\n", "Epoch 48/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.4831e-04 - mae: 0.0185 - val_loss: 0.0027 - val_mae: 0.0511\n", "Epoch 49/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.5213e-04 - mae: 0.0183 - val_loss: 9.3814e-05 - val_mae: 0.0074\n", "Epoch 50/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.6174e-04 - mae: 0.0126 - val_loss: 9.2369e-05 - val_mae: 0.0073\n", "Epoch 51/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.6849e-04 - mae: 0.0151 - val_loss: 1.0430e-04 - val_mae: 0.0079\n", "Epoch 52/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.3306e-04 - mae: 0.0142 - val_loss: 1.0023e-04 - val_mae: 0.0077\n", "Epoch 53/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.8789e-04 - mae: 0.0194 - val_loss: 6.1666e-04 - val_mae: 0.0230\n", "Epoch 54/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.2092e-04 - mae: 0.0158 - val_loss: 4.2188e-04 - val_mae: 0.0186\n", "Epoch 55/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.7486e-04 - mae: 0.0176 - val_loss: 9.9318e-05 - val_mae: 0.0077\n", "Epoch 56/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.1480e-04 - mae: 0.0161 - val_loss: 6.5117e-04 - val_mae: 0.0239\n", "Epoch 57/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 9.4373e-04 - mae: 0.0247 - val_loss: 1.2135e-04 - val_mae: 0.0086\n", "Epoch 58/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.2635e-04 - mae: 0.0144 - val_loss: 8.3926e-05 - val_mae: 0.0069\n", "Epoch 59/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.7918e-04 - mae: 0.0127 - val_loss: 1.0484e-04 - val_mae: 0.0080\n", "Epoch 60/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.7773e-04 - mae: 0.0177 - val_loss: 1.3954e-04 - val_mae: 0.0095\n", "Epoch 61/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.2730e-04 - mae: 0.0181 - val_loss: 1.8153e-04 - val_mae: 0.0112\n", "Epoch 62/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.5830e-04 - mae: 0.0169 - val_loss: 1.7598e-04 - val_mae: 0.0110\n", "Epoch 63/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0834e-04 - mae: 0.0160 - val_loss: 1.2239e-04 - val_mae: 0.0087\n", "Epoch 64/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.2841e-04 - mae: 0.0143 - val_loss: 5.9155e-04 - val_mae: 0.0227\n", "Epoch 65/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.3114e-04 - mae: 0.0164 - val_loss: 1.0302e-04 - val_mae: 0.0078\n", "Epoch 66/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.1598e-04 - mae: 0.0136 - val_loss: 8.1484e-05 - val_mae: 0.0068\n", "Epoch 67/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.4296e-04 - mae: 0.0162 - val_loss: 2.5745e-04 - val_mae: 0.0140\n", "Epoch 68/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.5563e-04 - mae: 0.0149 - val_loss: 0.0011 - val_mae: 0.0313\n", "Epoch 69/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.1796e-04 - mae: 0.0163 - val_loss: 6.9637e-04 - val_mae: 0.0250\n", "Epoch 70/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.1752e-04 - mae: 0.0163 - val_loss: 1.0810e-04 - val_mae: 0.0082\n", "Epoch 71/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0089e-04 - mae: 0.0155 - val_loss: 1.0688e-04 - val_mae: 0.0081\n", "Epoch 72/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.6207e-04 - mae: 0.0150 - val_loss: 1.2656e-04 - val_mae: 0.0089\n", "Epoch 73/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.4249e-04 - mae: 0.0184 - val_loss: 1.6406e-04 - val_mae: 0.0106\n", "Epoch 74/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.1338e-04 - mae: 0.0162 - val_loss: 4.8520e-04 - val_mae: 0.0205\n", "Epoch 75/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.3325e-04 - mae: 0.0181 - val_loss: 1.0843e-04 - val_mae: 0.0082\n", "Epoch 76/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.2570e-04 - mae: 0.0159 - val_loss: 8.1920e-05 - val_mae: 0.0069\n", "Epoch 77/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 2.9787e-04 - mae: 0.0135 - val_loss: 1.5269e-04 - val_mae: 0.0102\n", "Epoch 78/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.4700e-04 - mae: 0.0148 - val_loss: 6.5924e-04 - val_mae: 0.0242\n", "Epoch 79/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.7215e-04 - mae: 0.0196 - val_loss: 4.3991e-04 - val_mae: 0.0192\n", "Epoch 80/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.7719e-04 - mae: 0.0152 - val_loss: 7.9514e-05 - val_mae: 0.0067\n", "Epoch 81/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.6393e-04 - mae: 0.0168 - val_loss: 1.4290e-04 - val_mae: 0.0097\n", "Epoch 82/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8452e-04 - mae: 0.0128 - val_loss: 9.7736e-04 - val_mae: 0.0300\n", "Epoch 83/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.5203e-04 - mae: 0.0193 - val_loss: 7.8567e-05 - val_mae: 0.0067\n", "Epoch 84/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.7008e-04 - mae: 0.0128 - val_loss: 6.7986e-04 - val_mae: 0.0245\n", "Epoch 85/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.7095e-04 - mae: 0.0173 - val_loss: 2.5187e-04 - val_mae: 0.0139\n", "Epoch 86/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0462e-04 - mae: 0.0159 - val_loss: 7.5824e-05 - val_mae: 0.0066\n", "Epoch 87/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.7630e-04 - mae: 0.0129 - val_loss: 7.4364e-05 - val_mae: 0.0065\n", "Epoch 88/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 4.7860e-04 - mae: 0.0172 - val_loss: 1.2420e-04 - val_mae: 0.0089\n", "Epoch 89/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8832e-04 - mae: 0.0135 - val_loss: 9.7600e-05 - val_mae: 0.0076\n", "Epoch 90/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.7688e-04 - mae: 0.0155 - val_loss: 1.7923e-04 - val_mae: 0.0113\n", "Epoch 91/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4594e-04 - mae: 0.0123 - val_loss: 7.5429e-05 - val_mae: 0.0065\n", "Epoch 92/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.0077e-04 - mae: 0.0133 - val_loss: 9.1287e-05 - val_mae: 0.0074\n", "Epoch 93/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.1353e-04 - mae: 0.0161 - val_loss: 2.0344e-04 - val_mae: 0.0123\n", "Epoch 94/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.5881e-04 - mae: 0.0124 - val_loss: 2.2746e-04 - val_mae: 0.0129\n", "Epoch 95/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.8846e-04 - mae: 0.0180 - val_loss: 9.7531e-05 - val_mae: 0.0077\n", "Epoch 96/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.6161e-04 - mae: 0.0125 - val_loss: 8.2327e-05 - val_mae: 0.0069\n", "Epoch 97/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.9408e-04 - mae: 0.0135 - val_loss: 2.8793e-04 - val_mae: 0.0153\n", "Epoch 98/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0392e-04 - mae: 0.0157 - val_loss: 2.3403e-04 - val_mae: 0.0133\n", "Epoch 99/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.3754e-04 - mae: 0.0166 - val_loss: 1.0313e-04 - val_mae: 0.0079\n", "Epoch 100/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.0067e-04 - mae: 0.0133 - val_loss: 1.1743e-04 - val_mae: 0.0087\n", "Epoch 101/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8239e-04 - mae: 0.0134 - val_loss: 1.6918e-04 - val_mae: 0.0109\n", "Epoch 102/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.6551e-04 - mae: 0.0153 - val_loss: 1.5458e-04 - val_mae: 0.0103\n", "Epoch 103/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.4110e-04 - mae: 0.0145 - val_loss: 2.0546e-04 - val_mae: 0.0124\n", "Epoch 104/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4620e-04 - mae: 0.0122 - val_loss: 8.3033e-05 - val_mae: 0.0070\n", "Epoch 105/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0241e-04 - mae: 0.0158 - val_loss: 1.1933e-04 - val_mae: 0.0088\n", "Epoch 106/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.4199e-04 - mae: 0.0171 - val_loss: 1.6238e-04 - val_mae: 0.0106\n", "Epoch 107/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.4401e-04 - mae: 0.0149 - val_loss: 8.7318e-05 - val_mae: 0.0073\n", "Epoch 108/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.1675e-04 - mae: 0.0140 - val_loss: 2.3949e-04 - val_mae: 0.0135\n", "Epoch 109/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.0928e-04 - mae: 0.0112 - val_loss: 8.0337e-05 - val_mae: 0.0069\n", "Epoch 110/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.0473e-04 - mae: 0.0136 - val_loss: 1.3857e-04 - val_mae: 0.0097\n", "Epoch 111/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.7669e-04 - mae: 0.0177 - val_loss: 8.3069e-05 - val_mae: 0.0070\n", "Epoch 112/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.1355e-04 - mae: 0.0139 - val_loss: 1.4809e-04 - val_mae: 0.0101\n", "Epoch 113/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.7122e-04 - mae: 0.0151 - val_loss: 1.1580e-04 - val_mae: 0.0087\n", "Epoch 114/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8872e-04 - mae: 0.0131 - val_loss: 1.0588e-04 - val_mae: 0.0082\n", "Epoch 115/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.9403e-04 - mae: 0.0135 - val_loss: 7.3192e-05 - val_mae: 0.0065\n", "Epoch 116/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.8066e-04 - mae: 0.0157 - val_loss: 1.2668e-04 - val_mae: 0.0091\n", "Epoch 117/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.3080e-04 - mae: 0.0145 - val_loss: 7.0859e-05 - val_mae: 0.0064\n", "Epoch 118/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.6514e-04 - mae: 0.0125 - val_loss: 1.0584e-04 - val_mae: 0.0082\n", "Epoch 119/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.1565e-04 - mae: 0.0142 - val_loss: 1.1805e-04 - val_mae: 0.0088\n", "Epoch 120/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.8299e-04 - mae: 0.0155 - val_loss: 1.1069e-04 - val_mae: 0.0084\n", "Epoch 121/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.7783e-04 - mae: 0.0130 - val_loss: 6.9162e-05 - val_mae: 0.0063\n", "Epoch 122/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 3.4373e-04 - mae: 0.0145 - val_loss: 1.3096e-04 - val_mae: 0.0093\n", "Epoch 123/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.1927e-04 - mae: 0.0144 - val_loss: 2.1914e-04 - val_mae: 0.0128\n", "Epoch 124/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.5702e-04 - mae: 0.0180 - val_loss: 6.9222e-05 - val_mae: 0.0063\n", "Epoch 125/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.0449e-04 - mae: 0.0136 - val_loss: 3.3720e-04 - val_mae: 0.0167\n", "Epoch 126/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.1581e-04 - mae: 0.0143 - val_loss: 7.1190e-05 - val_mae: 0.0064\n", "Epoch 127/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.1085e-04 - mae: 0.0137 - val_loss: 8.6304e-05 - val_mae: 0.0072\n", "Epoch 128/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4947e-04 - mae: 0.0125 - val_loss: 2.4032e-04 - val_mae: 0.0138\n", "Epoch 129/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.8011e-04 - mae: 0.0158 - val_loss: 9.1524e-05 - val_mae: 0.0074\n", "Epoch 130/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 5.0046e-04 - mae: 0.0177 - val_loss: 1.2313e-04 - val_mae: 0.0090\n", "Epoch 131/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.1664e-04 - mae: 0.0143 - val_loss: 7.4568e-05 - val_mae: 0.0066\n", "Epoch 132/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.9574e-04 - mae: 0.0110 - val_loss: 1.3408e-04 - val_mae: 0.0095\n", "Epoch 133/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.3290e-04 - mae: 0.0144 - val_loss: 7.3678e-05 - val_mae: 0.0065\n", "Epoch 134/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 2.0820e-04 - mae: 0.0110 - val_loss: 7.3773e-05 - val_mae: 0.0066\n", "Epoch 135/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4314e-04 - mae: 0.0117 - val_loss: 8.8408e-05 - val_mae: 0.0073\n", "Epoch 136/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.1607e-04 - mae: 0.0140 - val_loss: 7.9576e-05 - val_mae: 0.0069\n", "Epoch 137/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.3353e-04 - mae: 0.0120 - val_loss: 1.2403e-04 - val_mae: 0.0091\n", "Epoch 138/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8227e-04 - mae: 0.0134 - val_loss: 3.8052e-04 - val_mae: 0.0178\n", "Epoch 139/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.5641e-04 - mae: 0.0153 - val_loss: 5.2938e-04 - val_mae: 0.0217\n", "Epoch 140/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8165e-04 - mae: 0.0129 - val_loss: 2.0356e-04 - val_mae: 0.0124\n", "Epoch 141/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.0687e-04 - mae: 0.0110 - val_loss: 1.3281e-04 - val_mae: 0.0095\n", "Epoch 142/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.0331e-04 - mae: 0.0135 - val_loss: 6.6292e-05 - val_mae: 0.0062\n", "Epoch 143/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.1456e-04 - mae: 0.0156 - val_loss: 2.6656e-04 - val_mae: 0.0144\n", "Epoch 144/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.6269e-04 - mae: 0.0151 - val_loss: 7.5906e-05 - val_mae: 0.0067\n", "Epoch 145/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7452e-04 - mae: 0.0103 - val_loss: 5.2846e-04 - val_mae: 0.0217\n", "Epoch 146/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4662e-04 - mae: 0.0125 - val_loss: 1.4002e-04 - val_mae: 0.0098\n", "Epoch 147/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.0558e-04 - mae: 0.0140 - val_loss: 6.7168e-05 - val_mae: 0.0062\n", "Epoch 148/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.7640e-04 - mae: 0.0128 - val_loss: 6.4384e-05 - val_mae: 0.0060\n", "Epoch 149/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.0793e-04 - mae: 0.0113 - val_loss: 2.0050e-04 - val_mae: 0.0122\n", "Epoch 150/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.7010e-04 - mae: 0.0148 - val_loss: 7.0467e-05 - val_mae: 0.0063\n", "Epoch 151/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8378e-04 - mae: 0.0133 - val_loss: 9.4289e-05 - val_mae: 0.0076\n", "Epoch 152/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4094e-04 - mae: 0.0123 - val_loss: 3.0451e-04 - val_mae: 0.0157\n", "Epoch 153/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.2583e-04 - mae: 0.0164 - val_loss: 6.6791e-05 - val_mae: 0.0062\n", "Epoch 154/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.2820e-04 - mae: 0.0116 - val_loss: 8.0630e-05 - val_mae: 0.0069\n", "Epoch 155/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.1769e-04 - mae: 0.0114 - val_loss: 1.7816e-04 - val_mae: 0.0113\n", "Epoch 156/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.3561e-04 - mae: 0.0144 - val_loss: 3.4762e-04 - val_mae: 0.0171\n", "Epoch 157/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8825e-04 - mae: 0.0131 - val_loss: 5.7054e-04 - val_mae: 0.0224\n", "Epoch 158/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.2546e-04 - mae: 0.0162 - val_loss: 1.6503e-04 - val_mae: 0.0108\n", "Epoch 159/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0739e-04 - mae: 0.0165 - val_loss: 8.7469e-05 - val_mae: 0.0073\n", "Epoch 160/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.2336e-04 - mae: 0.0143 - val_loss: 1.5482e-04 - val_mae: 0.0105\n", "Epoch 161/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.5927e-04 - mae: 0.0125 - val_loss: 9.8564e-05 - val_mae: 0.0079\n", "Epoch 162/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.0298e-04 - mae: 0.0112 - val_loss: 6.2355e-05 - val_mae: 0.0059\n", "Epoch 163/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.9446e-04 - mae: 0.0135 - val_loss: 6.4844e-05 - val_mae: 0.0061\n", "Epoch 164/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.2535e-04 - mae: 0.0117 - val_loss: 5.0943e-04 - val_mae: 0.0210\n", "Epoch 165/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.0255e-04 - mae: 0.0157 - val_loss: 7.7531e-05 - val_mae: 0.0068\n", "Epoch 166/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.2374e-04 - mae: 0.0113 - val_loss: 2.2365e-04 - val_mae: 0.0131\n", "Epoch 167/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.6416e-04 - mae: 0.0127 - val_loss: 1.2049e-04 - val_mae: 0.0089\n", "Epoch 168/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 3.0058e-04 - mae: 0.0138 - val_loss: 8.2825e-05 - val_mae: 0.0070\n", "Epoch 169/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.2319e-04 - mae: 0.0117 - val_loss: 2.3471e-04 - val_mae: 0.0135\n", "Epoch 170/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 3.4604e-04 - mae: 0.0149 - val_loss: 7.2124e-05 - val_mae: 0.0065\n", "Epoch 171/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8223e-04 - mae: 0.0102 - val_loss: 1.0621e-04 - val_mae: 0.0083\n", "Epoch 172/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 2.2073e-04 - mae: 0.0117 - val_loss: 3.8319e-04 - val_mae: 0.0180\n", "Epoch 173/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 3.1386e-04 - mae: 0.0135 - val_loss: 6.9562e-05 - val_mae: 0.0063\n", "Epoch 174/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.0363e-04 - mae: 0.0111 - val_loss: 7.5911e-05 - val_mae: 0.0067\n", "Epoch 175/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 2.3623e-04 - mae: 0.0122 - val_loss: 1.4349e-04 - val_mae: 0.0100\n", "Epoch 176/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 2.0220e-04 - mae: 0.0113 - val_loss: 6.3630e-05 - val_mae: 0.0060\n", "Epoch 177/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8198e-04 - mae: 0.0136 - val_loss: 2.1122e-04 - val_mae: 0.0126\n", "Epoch 178/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 2.2751e-04 - mae: 0.0117 - val_loss: 2.2617e-04 - val_mae: 0.0132\n", "Epoch 179/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8352e-04 - mae: 0.0135 - val_loss: 8.1607e-05 - val_mae: 0.0070\n", "Epoch 180/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.9738e-04 - mae: 0.0109 - val_loss: 6.7211e-05 - val_mae: 0.0062\n", "Epoch 181/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8103e-04 - mae: 0.0100 - val_loss: 7.2020e-05 - val_mae: 0.0065\n", "Epoch 182/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.6294e-04 - mae: 0.0127 - val_loss: 7.7683e-05 - val_mae: 0.0068\n", "Epoch 183/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 4.5101e-04 - mae: 0.0167 - val_loss: 7.1739e-05 - val_mae: 0.0064\n", "Epoch 184/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4378e-04 - mae: 0.0122 - val_loss: 7.7490e-05 - val_mae: 0.0068\n", "Epoch 185/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.1946e-04 - mae: 0.0113 - val_loss: 1.8635e-04 - val_mae: 0.0117\n", "Epoch 186/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.1543e-04 - mae: 0.0113 - val_loss: 7.1266e-05 - val_mae: 0.0064\n", "Epoch 187/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 2.4751e-04 - mae: 0.0122 - val_loss: 6.5164e-05 - val_mae: 0.0061\n", "Epoch 188/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.9176e-04 - mae: 0.0107 - val_loss: 3.1423e-04 - val_mae: 0.0161\n", "Epoch 189/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8319e-04 - mae: 0.0106 - val_loss: 6.6263e-05 - val_mae: 0.0062\n", "Epoch 190/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 3.0127e-04 - mae: 0.0138 - val_loss: 9.6900e-05 - val_mae: 0.0078\n", "Epoch 191/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8000e-04 - mae: 0.0129 - val_loss: 1.7771e-04 - val_mae: 0.0114\n", "Epoch 192/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 3ms/step - loss: 1.8190e-04 - mae: 0.0103 - val_loss: 1.0718e-04 - val_mae: 0.0083\n", "Epoch 193/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 3ms/step - loss: 2.0055e-04 - mae: 0.0112 - val_loss: 2.3834e-04 - val_mae: 0.0137\n", "Epoch 194/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 1.8796e-04 - mae: 0.0107 - val_loss: 1.8540e-04 - val_mae: 0.0118\n", "Epoch 195/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 2.2067e-04 - mae: 0.0114 - val_loss: 6.9714e-05 - val_mae: 0.0063\n", "Epoch 196/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.9413e-04 - mae: 0.0108 - val_loss: 9.4277e-05 - val_mae: 0.0077\n", "Epoch 197/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7734e-04 - mae: 0.0104 - val_loss: 4.6624e-04 - val_mae: 0.0200\n", "Epoch 198/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 4.4534e-04 - mae: 0.0164 - val_loss: 9.9711e-05 - val_mae: 0.0080\n", "Epoch 199/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.2786e-04 - mae: 0.0118 - val_loss: 6.3379e-05 - val_mae: 0.0060\n", "Epoch 200/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.6476e-04 - mae: 0.0124 - val_loss: 6.6572e-05 - val_mae: 0.0062\n", "Epoch 201/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.7263e-04 - mae: 0.0102 - val_loss: 7.4567e-05 - val_mae: 0.0066\n", "Epoch 202/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.6327e-04 - mae: 0.0128 - val_loss: 1.5272e-04 - val_mae: 0.0104\n", "Epoch 203/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.9985e-04 - mae: 0.0136 - val_loss: 9.7836e-05 - val_mae: 0.0079\n", "Epoch 204/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.1529e-04 - mae: 0.0114 - val_loss: 1.2276e-04 - val_mae: 0.0091\n", "Epoch 205/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 2ms/step - loss: 2.0433e-04 - mae: 0.0112 - val_loss: 1.9844e-04 - val_mae: 0.0123\n", "Epoch 206/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.1932e-04 - mae: 0.0113 - val_loss: 2.1797e-04 - val_mae: 0.0129\n", "Epoch 207/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.1989e-04 - mae: 0.0114 - val_loss: 9.6815e-05 - val_mae: 0.0078\n", "Epoch 208/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.6738e-04 - mae: 0.0100 - val_loss: 2.6857e-04 - val_mae: 0.0146\n", "Epoch 209/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.8594e-04 - mae: 0.0106 - val_loss: 8.5151e-05 - val_mae: 0.0072\n", "Epoch 210/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.4308e-04 - mae: 0.0123 - val_loss: 7.1882e-05 - val_mae: 0.0065\n", "Epoch 211/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 1.9205e-04 - mae: 0.0107 - val_loss: 1.8427e-04 - val_mae: 0.0117\n", "Epoch 212/1000\n", "\u001b[1m241/241\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 2.8665e-04 - mae: 0.0134 - val_loss: 1.0424e-04 - val_mae: 0.0082\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 15ms/step - loss: 5.1869e-05 - mae: 0.0061\n", "Test MAE: 0.005926466081291437\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 35ms/step\n", "Test RMSE: 0.007080159041607781\n", "Total time: 96.47222065925598 seconds\n"]}], "source": ["# Train the final model\n", "start_time = time.time()\n", "\n", "early_stopping = EarlyStopping(monitor='val_loss', patience=50, restore_best_weights=True)\n", "\n", "history = final_model.fit(\n", "    x_train, y_train,\n", "    epochs=1000,\n", "    batch_size=batch_size,\n", "    validation_split=0.2,\n", "    callbacks=[early_stopping],\n", "    verbose=1\n", ")\n", "\n", "# Evaluate the model\n", "test_loss, test_mae = final_model.evaluate(x_test, y_test)\n", "print('Test MAE:', test_mae)\n", "\n", "# Calculate RMSE\n", "predictions = final_model.predict(x_test)\n", "rmse = sqrt(mean_squared_error(y_test, predictions))\n", "print('Test RMSE:', rmse)\n", "\n", "# End the timer and print the total time passed\n", "end_time = time.time()\n", "print(\"Total time:\", end_time - start_time, \"seconds\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot the training and validation loss\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(history.history['loss'], label='Training Loss')\n", "plt.plot(history.history['val_loss'], label='Validation Loss')\n", "plt.title('PSO-Optimized MLP Univariate Model: Training and Validation Loss')\n", "plt.xlabel('Epochs')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot the actual vs predicted values\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(y_test, label='Actual')\n", "plt.plot(predictions, label='Predicted')\n", "plt.title(f'PSO-Optimized MLP Univariate Model: Actual vs Predicted (RMSE: {rmse:.5f})')\n", "plt.xlabel('Time Steps')\n", "plt.ylabel('GBP/USD Exchange Rate')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.savefig('PLOTS/PSO_MLP_U.png')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PSO-Optimized MLP Univariate RMSE: 0.007080159041607781\n", "Original MLP Univariate RMSE: 0.01295 (from the paper)\n", "Improvement: 45.33%\n"]}], "source": ["# Compare with the original MLP Univariate model\n", "print(\"PSO-Optimized MLP Univariate RMSE:\", rmse)\n", "print(\"Original MLP Univariate RMSE: 0.01295 (from the paper)\")\n", "improvement = (0.01295 - rmse) / 0.01295 * 100\n", "print(f\"Improvement: {improvement:.2f}%\")"]}], "metadata": {"kernelspec": {"display_name": "exchange", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "orig_nbformat": 2}, "nbformat": 4, "nbformat_minor": 2}