{"metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9-final"}, "orig_nbformat": 2, "kernelspec": {"name": "Python 3.6.9 64-bit", "display_name": "Python 3.6.9 64-bit", "metadata": {"interpreter": {"hash": "31f2aee4e71d21fbe5cf8b01ff0e069b9275f58929596ceb00d14d90e3e16cd6"}}}}, "nbformat": 4, "nbformat_minor": 2, "cells": [{"source": ["# Data Preparation"], "cell_type": "markdown", "metadata": {}}, {"source": ["### Sources\n", "\n", "* Normalized GDP (monthly)\n", "\t\thttps://fred.stlouisfed.org/  \n", "\t\tfrom 2000-01-01  \n", "\t\tto 2020-05-01\n", "\n", "* Libor Rates (daily)\n", "\t\thttps://fred.stlouisfed.org/  \n", "\t\tfrom 2001-01-02  \n", "\t\tto 2020-09-18\n", "\n", "* Current Account to GDP (quarterly)\n", "\t\thttps://stats.oecd.org/  \n", "\t\tfrom Q1-2000  \n", "\t\tto Q1-2020\n", "\n", "* Forex (daily)\n", "\t\thttps://www.federalreserve.gov/  \n", "\t\tfrom 2000-01-03  \n", "\t\tto 2020-08-21  \n", "\t\tAUD\tEUR\tNZD\tGBP\tBRL\tCAD\tCNY\tDKK\tHKD\tINR\tJPY\tMYR\tMXN\tNOK\tZAR\tSGD\tKRW\tLKR\tSEK\tCHF\tTWD\tTHB\tVEB"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"source": ["## a. Foreign Exchange Rates\n", "\n", "Forex or Parity. Showing the Exchange Rate prices of different currencies to US Dollar"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["               AUD     EUR     NZD     GBP     BRL     CAD     CNY     DKK  \\\n", "Date                                                                         \n", "2000-01-03  0.6591  1.0155  0.5254   1.627   1.805  1.4465  8.2798   7.329   \n", "2000-01-04  0.6562  1.0309  0.5198   1.637  1.8405  1.4518  8.2799   7.218   \n", "2000-01-05   0.655  1.0335  0.5171  1.6415   1.856  1.4518  8.2798   7.208   \n", "2000-01-06   0.654  1.0324  0.5145  1.6475    1.84  1.4571  8.2797  7.2125   \n", "2000-01-07  0.6548  1.0294   0.516  1.6384   1.831  1.4505  8.2794  7.2285   \n", "...            ...     ...     ...     ...     ...     ...     ...     ...   \n", "2020-08-17  0.7211  1.1869  0.6547  1.3105  5.4734  1.3202  6.9318  6.2727   \n", "2020-08-18  0.7235  1.1928  0.6588  1.3228  5.4845  1.3181  6.9215  6.2406   \n", "2020-08-19  0.7234  1.1898  0.6607  1.3191  5.5045  1.3173  6.9192   6.258   \n", "2020-08-20  0.7178  1.1862  0.6519   1.319   5.637  1.3177  6.9143  6.2783   \n", "2020-08-21  0.7156  1.1775  0.6533  1.3098  5.5949  1.3201  6.9179  6.3235   \n", "\n", "               HKD    INR  ...     NOK      ZAR     SGD      KRW     LKR  \\\n", "Date                       ...                                             \n", "2000-01-03  7.7765  43.55  ...   7.964    6.126  1.6563     1128    72.3   \n", "2000-01-04  7.7775  43.55  ...   7.934    6.085  1.6535   1122.5   72.65   \n", "2000-01-05   7.778  43.55  ...   7.935     6.07   1.656     1135   72.95   \n", "2000-01-06  7.7785  43.55  ...    7.94     6.08  1.6655   1146.5   72.95   \n", "2000-01-07  7.7783  43.55  ...   7.966    6.057  1.6625     1138   73.15   \n", "...            ...    ...  ...     ...      ...     ...      ...     ...   \n", "2020-08-17  7.7503  74.74  ...  8.8633    17.46  1.3679  1184.17  183.13   \n", "2020-08-18  7.7501  74.62  ...  8.8387  17.3525  1.3647  1184.74  184.23   \n", "2020-08-19    7.75  74.85  ...  8.8504    17.18   1.366  1181.06   186.1   \n", "2020-08-20    7.75  75.01  ...  8.9437   17.275  1.3676  1187.66  184.35   \n", "2020-08-21  7.7502  74.92  ...  9.0457  17.1275  1.3723  1191.84   184.5   \n", "\n", "               SEK     CHF    TWD    THB          VEB  \n", "Date                                                   \n", "2000-01-03   8.443  1.5808  31.38  36.97       0.6498  \n", "2000-01-04    8.36  1.5565   30.6  37.13       0.6503  \n", "2000-01-05   8.353  1.5526   30.8   37.1       0.6515  \n", "2000-01-06  8.3675   1.554  31.75  37.62       0.6503  \n", "2000-01-07   8.415  1.5623  30.85   37.3       0.6508  \n", "...            ...     ...    ...    ...          ...  \n", "2020-08-17  8.6918  0.9058  29.38  31.15  293920.7455  \n", "2020-08-18  8.6481  0.9029  29.39  31.16  289550.7506  \n", "2020-08-19  8.6574  0.9114  29.37  31.25  291796.8124  \n", "2020-08-20   8.706  0.9082  29.42  31.41   296027.271  \n", "2020-08-21  8.7988  0.9131   29.4  31.56  302779.1537  \n", "\n", "[5385 rows x 23 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>AUD</th>\n      <th>EUR</th>\n      <th>NZD</th>\n      <th>GBP</th>\n      <th>BRL</th>\n      <th>CAD</th>\n      <th>CNY</th>\n      <th>DKK</th>\n      <th>HKD</th>\n      <th>INR</th>\n      <th>...</th>\n      <th>NOK</th>\n      <th>ZAR</th>\n      <th>SGD</th>\n      <th>KRW</th>\n      <th>LKR</th>\n      <th>SEK</th>\n      <th>CHF</th>\n      <th>TWD</th>\n      <th>THB</th>\n      <th>VEB</th>\n    </tr>\n    <tr>\n      <th>Date</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2000-01-03</th>\n      <td>0.6591</td>\n      <td>1.0155</td>\n      <td>0.5254</td>\n      <td>1.627</td>\n      <td>1.805</td>\n      <td>1.4465</td>\n      <td>8.2798</td>\n      <td>7.329</td>\n      <td>7.7765</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>7.964</td>\n      <td>6.126</td>\n      <td>1.6563</td>\n      <td>1128</td>\n      <td>72.3</td>\n      <td>8.443</td>\n      <td>1.5808</td>\n      <td>31.38</td>\n      <td>36.97</td>\n      <td>0.6498</td>\n    </tr>\n    <tr>\n      <th>2000-01-04</th>\n      <td>0.6562</td>\n      <td>1.0309</td>\n      <td>0.5198</td>\n      <td>1.637</td>\n      <td>1.8405</td>\n      <td>1.4518</td>\n      <td>8.2799</td>\n      <td>7.218</td>\n      <td>7.7775</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>7.934</td>\n      <td>6.085</td>\n      <td>1.6535</td>\n      <td>1122.5</td>\n      <td>72.65</td>\n      <td>8.36</td>\n      <td>1.5565</td>\n      <td>30.6</td>\n      <td>37.13</td>\n      <td>0.6503</td>\n    </tr>\n    <tr>\n      <th>2000-01-05</th>\n      <td>0.655</td>\n      <td>1.0335</td>\n      <td>0.5171</td>\n      <td>1.6415</td>\n      <td>1.856</td>\n      <td>1.4518</td>\n      <td>8.2798</td>\n      <td>7.208</td>\n      <td>7.778</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>7.935</td>\n      <td>6.07</td>\n      <td>1.656</td>\n      <td>1135</td>\n      <td>72.95</td>\n      <td>8.353</td>\n      <td>1.5526</td>\n      <td>30.8</td>\n      <td>37.1</td>\n      <td>0.6515</td>\n    </tr>\n    <tr>\n      <th>2000-01-06</th>\n      <td>0.654</td>\n      <td>1.0324</td>\n      <td>0.5145</td>\n      <td>1.6475</td>\n      <td>1.84</td>\n      <td>1.4571</td>\n      <td>8.2797</td>\n      <td>7.2125</td>\n      <td>7.7785</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>7.94</td>\n      <td>6.08</td>\n      <td>1.6655</td>\n      <td>1146.5</td>\n      <td>72.95</td>\n      <td>8.3675</td>\n      <td>1.554</td>\n      <td>31.75</td>\n      <td>37.62</td>\n      <td>0.6503</td>\n    </tr>\n    <tr>\n      <th>2000-01-07</th>\n      <td>0.6548</td>\n      <td>1.0294</td>\n      <td>0.516</td>\n      <td>1.6384</td>\n      <td>1.831</td>\n      <td>1.4505</td>\n      <td>8.2794</td>\n      <td>7.2285</td>\n      <td>7.7783</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>7.966</td>\n      <td>6.057</td>\n      <td>1.6625</td>\n      <td>1138</td>\n      <td>73.15</td>\n      <td>8.415</td>\n      <td>1.5623</td>\n      <td>30.85</td>\n      <td>37.3</td>\n      <td>0.6508</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2020-08-17</th>\n      <td>0.7211</td>\n      <td>1.1869</td>\n      <td>0.6547</td>\n      <td>1.3105</td>\n      <td>5.4734</td>\n      <td>1.3202</td>\n      <td>6.9318</td>\n      <td>6.2727</td>\n      <td>7.7503</td>\n      <td>74.74</td>\n      <td>...</td>\n      <td>8.8633</td>\n      <td>17.46</td>\n      <td>1.3679</td>\n      <td>1184.17</td>\n      <td>183.13</td>\n      <td>8.6918</td>\n      <td>0.9058</td>\n      <td>29.38</td>\n      <td>31.15</td>\n      <td>293920.7455</td>\n    </tr>\n    <tr>\n      <th>2020-08-18</th>\n      <td>0.7235</td>\n      <td>1.1928</td>\n      <td>0.6588</td>\n      <td>1.3228</td>\n      <td>5.4845</td>\n      <td>1.3181</td>\n      <td>6.9215</td>\n      <td>6.2406</td>\n      <td>7.7501</td>\n      <td>74.62</td>\n      <td>...</td>\n      <td>8.8387</td>\n      <td>17.3525</td>\n      <td>1.3647</td>\n      <td>1184.74</td>\n      <td>184.23</td>\n      <td>8.6481</td>\n      <td>0.9029</td>\n      <td>29.39</td>\n      <td>31.16</td>\n      <td>289550.7506</td>\n    </tr>\n    <tr>\n      <th>2020-08-19</th>\n      <td>0.7234</td>\n      <td>1.1898</td>\n      <td>0.6607</td>\n      <td>1.3191</td>\n      <td>5.5045</td>\n      <td>1.3173</td>\n      <td>6.9192</td>\n      <td>6.258</td>\n      <td>7.75</td>\n      <td>74.85</td>\n      <td>...</td>\n      <td>8.8504</td>\n      <td>17.18</td>\n      <td>1.366</td>\n      <td>1181.06</td>\n      <td>186.1</td>\n      <td>8.6574</td>\n      <td>0.9114</td>\n      <td>29.37</td>\n      <td>31.25</td>\n      <td>291796.8124</td>\n    </tr>\n    <tr>\n      <th>2020-08-20</th>\n      <td>0.7178</td>\n      <td>1.1862</td>\n      <td>0.6519</td>\n      <td>1.319</td>\n      <td>5.637</td>\n      <td>1.3177</td>\n      <td>6.9143</td>\n      <td>6.2783</td>\n      <td>7.75</td>\n      <td>75.01</td>\n      <td>...</td>\n      <td>8.9437</td>\n      <td>17.275</td>\n      <td>1.3676</td>\n      <td>1187.66</td>\n      <td>184.35</td>\n      <td>8.706</td>\n      <td>0.9082</td>\n      <td>29.42</td>\n      <td>31.41</td>\n      <td>296027.271</td>\n    </tr>\n    <tr>\n      <th>2020-08-21</th>\n      <td>0.7156</td>\n      <td>1.1775</td>\n      <td>0.6533</td>\n      <td>1.3098</td>\n      <td>5.5949</td>\n      <td>1.3201</td>\n      <td>6.9179</td>\n      <td>6.3235</td>\n      <td>7.7502</td>\n      <td>74.92</td>\n      <td>...</td>\n      <td>9.0457</td>\n      <td>17.1275</td>\n      <td>1.3723</td>\n      <td>1191.84</td>\n      <td>184.5</td>\n      <td>8.7988</td>\n      <td>0.9131</td>\n      <td>29.4</td>\n      <td>31.56</td>\n      <td>302779.1537</td>\n    </tr>\n  </tbody>\n</table>\n<p>5385 rows × 23 columns</p>\n</div>"}, "metadata": {}, "execution_count": 2}], "source": ["# read the dataframe and assign the time column as the index values of the dataframe\n", "file = \"./DATA/forex.csv\"\n", "forex = pd.read_csv(file, index_col='Date', parse_dates=True)\n", "forex"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2000-01-03', '2000-01-04', '2000-01-05', '2000-01-06',\n", "               '2000-01-07', '2000-01-10', '2000-01-11', '2000-01-12',\n", "               '2000-01-13', '2000-01-14',\n", "               ...\n", "               '2020-08-10', '2020-08-11', '2020-08-12', '2020-08-13',\n", "               '2020-08-14', '2020-08-17', '2020-08-18', '2020-08-19',\n", "               '2020-08-20', '2020-08-21'],\n", "              dtype='datetime64[ns]', name='Date', length=5385, freq=None)"]}, "metadata": {}, "execution_count": 3}], "source": ["# the index is marked as datetime object\n", "forex.index"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timestamp('2000-01-04 00:00:00')"]}, "metadata": {}, "execution_count": 4}], "source": ["# if checked the elements in the index we see that they are timestamps\n", "forex.index[1]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"tags": []}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\nDatetimeIndex: 5385 entries, 2000-01-03 to 2020-08-21\nData columns (total 23 columns):\n #   Column  Non-Null Count  Dtype \n---  ------  --------------  ----- \n 0   AUD     5385 non-null   object\n 1   EUR     5385 non-null   object\n 2   NZD     5385 non-null   object\n 3   GBP     5385 non-null   object\n 4   BRL     5385 non-null   object\n 5   CAD     5385 non-null   object\n 6   CNY     5385 non-null   object\n 7   DKK     5385 non-null   object\n 8   HKD     5385 non-null   object\n 9   INR     5385 non-null   object\n 10  JPY     5385 non-null   object\n 11  MYR     5385 non-null   object\n 12  MXN     5385 non-null   object\n 13  NOK     5385 non-null   object\n 14  ZAR     5385 non-null   object\n 15  SGD     5385 non-null   object\n 16  KRW     5385 non-null   object\n 17  LKR     5385 non-null   object\n 18  SEK     5385 non-null   object\n 19  CHF     5385 non-null   object\n 20  TWD     5385 non-null   object\n 21  THB     5385 non-null   object\n 22  VEB     5385 non-null   object\ndtypes: object(23)\nmemory usage: 1009.7+ KB\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["         AUD   EUR   NZD   GBP   BRL   CAD     CNY   DKK   HKD   INR  ...  \\\n", "count   5385  5385  5385  5385  5385  5385    5385  5385  5385  5385  ...   \n", "unique  3049  3279  2729  3551  4040  3222    2844  4594   893  2106  ...   \n", "top       ND    ND    ND    ND    ND    ND  8.2765    ND    ND    ND  ...   \n", "freq     203   203   203   203   203   203     208   203   203   204  ...   \n", "\n", "         NOK   ZAR   SGD   KRW   LKR   SEK   CHF   TWD   THB     VEB  \n", "count   5385  5385  5385  5385  5385  5385  5385  5385  5385    5385  \n", "unique  4668  3907  2957  3572  1853  4728  3219   714  1382    1016  \n", "top       ND    ND    ND    ND    ND    ND    ND    ND    ND  2.1446  \n", "freq     203   203   203   203   203   203   203   206   203    1208  \n", "\n", "[4 rows x 23 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>AUD</th>\n      <th>EUR</th>\n      <th>NZD</th>\n      <th>GBP</th>\n      <th>BRL</th>\n      <th>CAD</th>\n      <th>CNY</th>\n      <th>DKK</th>\n      <th>HKD</th>\n      <th>INR</th>\n      <th>...</th>\n      <th>NOK</th>\n      <th>ZAR</th>\n      <th>SGD</th>\n      <th>KRW</th>\n      <th>LKR</th>\n      <th>SEK</th>\n      <th>CHF</th>\n      <th>TWD</th>\n      <th>THB</th>\n      <th>VEB</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>count</th>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>...</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n      <td>5385</td>\n    </tr>\n    <tr>\n      <th>unique</th>\n      <td>3049</td>\n      <td>3279</td>\n      <td>2729</td>\n      <td>3551</td>\n      <td>4040</td>\n      <td>3222</td>\n      <td>2844</td>\n      <td>4594</td>\n      <td>893</td>\n      <td>2106</td>\n      <td>...</td>\n      <td>4668</td>\n      <td>3907</td>\n      <td>2957</td>\n      <td>3572</td>\n      <td>1853</td>\n      <td>4728</td>\n      <td>3219</td>\n      <td>714</td>\n      <td>1382</td>\n      <td>1016</td>\n    </tr>\n    <tr>\n      <th>top</th>\n      <td>ND</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>8.2765</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>...</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>ND</td>\n      <td>2.1446</td>\n    </tr>\n    <tr>\n      <th>freq</th>\n      <td>203</td>\n      <td>203</td>\n      <td>203</td>\n      <td>203</td>\n      <td>203</td>\n      <td>203</td>\n      <td>208</td>\n      <td>203</td>\n      <td>203</td>\n      <td>204</td>\n      <td>...</td>\n      <td>203</td>\n      <td>203</td>\n      <td>203</td>\n      <td>203</td>\n      <td>203</td>\n      <td>203</td>\n      <td>203</td>\n      <td>206</td>\n      <td>203</td>\n      <td>1208</td>\n    </tr>\n  </tbody>\n</table>\n<p>4 rows × 23 columns</p>\n</div>"}, "metadata": {}, "execution_count": 5}], "source": ["# let's gather some information about our dataframe\n", "forex.info()\n", "forex.describe()\n", "# we can see that our series are not in numbers, but objects.\n", "# as well, there are some observations marked with 'ND'\n", "# (I assume they are a referrence for No Data)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# drop rows with 'ND's\n", "# https://stackoverflow.com/a/14661768/********\n", "forex = forex.drop(forex[forex.values == 'ND'].index)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"tags": []}, "outputs": [], "source": ["# convert the values numeric\n", "forex = forex.apply(pd.to_numeric)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"tags": []}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\nDatetimeIndex: 5172 entries, 2000-01-03 to 2020-08-21\nData columns (total 23 columns):\n #   Column  Non-Null Count  Dtype  \n---  ------  --------------  -----  \n 0   AUD     5172 non-null   float64\n 1   EUR     5172 non-null   float64\n 2   NZD     5172 non-null   float64\n 3   GBP     5172 non-null   float64\n 4   BRL     5172 non-null   float64\n 5   CAD     5172 non-null   float64\n 6   CNY     5172 non-null   float64\n 7   DKK     5172 non-null   float64\n 8   HKD     5172 non-null   float64\n 9   INR     5172 non-null   float64\n 10  JPY     5172 non-null   float64\n 11  MYR     5172 non-null   float64\n 12  MXN     5172 non-null   float64\n 13  NOK     5172 non-null   float64\n 14  ZAR     5172 non-null   float64\n 15  SGD     5172 non-null   float64\n 16  KRW     5172 non-null   float64\n 17  LKR     5172 non-null   float64\n 18  SEK     5172 non-null   float64\n 19  CHF     5172 non-null   float64\n 20  TWD     5172 non-null   float64\n 21  THB     5172 non-null   float64\n 22  VEB     5172 non-null   float64\ndtypes: float64(23)\nmemory usage: 969.8 KB\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["               AUD          EUR          NZD          GBP          BRL  \\\n", "count  5172.000000  5172.000000  5172.000000  5172.000000  5172.000000   \n", "mean      0.776401     1.206302     0.671975     1.577529     2.627318   \n", "std       0.147869     0.165008     0.117817     0.211503     0.838444   \n", "min       0.482800     0.827000     0.392000     1.149200     1.537500   \n", "25%       0.691175     1.107600     0.626175     1.431700     1.959000   \n", "50%       0.760000     1.217150     0.690150     1.567000     2.352150   \n", "75%       0.892725     1.328200     0.752625     1.687025     3.175000   \n", "max       1.102600     1.601000     0.881400     2.110400     5.920400   \n", "\n", "               CAD          CNY          DKK          HKD          INR  ...  \\\n", "count  5172.000000  5172.000000  5172.000000  5172.000000  5172.000000  ...   \n", "mean      1.234779     7.195353     6.300841     7.781914    53.413941  ...   \n", "std       0.180804     0.808431     0.931572     0.027518    10.256636  ...   \n", "min       0.916800     6.040200     4.660500     7.708500    38.480000  ...   \n", "25%       1.060500     6.490375     5.607250     7.756000    45.320000  ...   \n", "50%       1.246100     6.878050     6.115700     7.778250    48.365000  ...   \n", "75%       1.339625     8.276500     6.734700     7.799700    63.670000  ...   \n", "max       1.612800     8.280000     9.005000     7.849900    76.950000  ...   \n", "\n", "               NOK          ZAR          SGD          KRW          LKR  \\\n", "count  5172.000000  5172.000000  5172.000000  5172.000000  5172.000000   \n", "mean      7.157744     9.655624     1.477798  1127.919503   121.210506   \n", "std       1.291734     3.078815     0.186737   102.758636    27.522978   \n", "min       4.946700     5.615000     1.200700   903.200000    72.300000   \n", "25%       6.015850     7.136100     1.346175  1070.640000   101.250000   \n", "50%       6.856850     8.274750     1.405850  1129.760000   113.400000   \n", "75%       8.368400    12.041125     1.672325  1184.000000   133.725000   \n", "max      11.684200    19.040000     1.854000  1570.100000   193.000000   \n", "\n", "               SEK          CHF          TWD          THB            VEB  \n", "count  5172.000000  5172.000000  5172.000000  5172.000000    5172.000000  \n", "mean      7.951365     1.144487    31.704238    35.400152    7538.583913  \n", "std       1.218071     0.247661     1.679128     4.548094   33066.299454  \n", "min       5.834600     0.729600    28.500000    28.600000       0.649800  \n", "25%       6.872275     0.964600    30.260000    31.670000       1.923500  \n", "50%       7.809450     1.026900    31.600000    33.705000       4.289300  \n", "75%       8.830225     1.262425    32.950000    39.250000       6.284200  \n", "max      11.027000     1.825000    35.210000    45.820000  302779.153700  \n", "\n", "[8 rows x 23 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>AUD</th>\n      <th>EUR</th>\n      <th>NZD</th>\n      <th>GBP</th>\n      <th>BRL</th>\n      <th>CAD</th>\n      <th>CNY</th>\n      <th>DKK</th>\n      <th>HKD</th>\n      <th>INR</th>\n      <th>...</th>\n      <th>NOK</th>\n      <th>ZAR</th>\n      <th>SGD</th>\n      <th>KRW</th>\n      <th>LKR</th>\n      <th>SEK</th>\n      <th>CHF</th>\n      <th>TWD</th>\n      <th>THB</th>\n      <th>VEB</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>count</th>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>...</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n      <td>5172.000000</td>\n    </tr>\n    <tr>\n      <th>mean</th>\n      <td>0.776401</td>\n      <td>1.206302</td>\n      <td>0.671975</td>\n      <td>1.577529</td>\n      <td>2.627318</td>\n      <td>1.234779</td>\n      <td>7.195353</td>\n      <td>6.300841</td>\n      <td>7.781914</td>\n      <td>53.413941</td>\n      <td>...</td>\n      <td>7.157744</td>\n      <td>9.655624</td>\n      <td>1.477798</td>\n      <td>1127.919503</td>\n      <td>121.210506</td>\n      <td>7.951365</td>\n      <td>1.144487</td>\n      <td>31.704238</td>\n      <td>35.400152</td>\n      <td>7538.583913</td>\n    </tr>\n    <tr>\n      <th>std</th>\n      <td>0.147869</td>\n      <td>0.165008</td>\n      <td>0.117817</td>\n      <td>0.211503</td>\n      <td>0.838444</td>\n      <td>0.180804</td>\n      <td>0.808431</td>\n      <td>0.931572</td>\n      <td>0.027518</td>\n      <td>10.256636</td>\n      <td>...</td>\n      <td>1.291734</td>\n      <td>3.078815</td>\n      <td>0.186737</td>\n      <td>102.758636</td>\n      <td>27.522978</td>\n      <td>1.218071</td>\n      <td>0.247661</td>\n      <td>1.679128</td>\n      <td>4.548094</td>\n      <td>33066.299454</td>\n    </tr>\n    <tr>\n      <th>min</th>\n      <td>0.482800</td>\n      <td>0.827000</td>\n      <td>0.392000</td>\n      <td>1.149200</td>\n      <td>1.537500</td>\n      <td>0.916800</td>\n      <td>6.040200</td>\n      <td>4.660500</td>\n      <td>7.708500</td>\n      <td>38.480000</td>\n      <td>...</td>\n      <td>4.946700</td>\n      <td>5.615000</td>\n      <td>1.200700</td>\n      <td>903.200000</td>\n      <td>72.300000</td>\n      <td>5.834600</td>\n      <td>0.729600</td>\n      <td>28.500000</td>\n      <td>28.600000</td>\n      <td>0.649800</td>\n    </tr>\n    <tr>\n      <th>25%</th>\n      <td>0.691175</td>\n      <td>1.107600</td>\n      <td>0.626175</td>\n      <td>1.431700</td>\n      <td>1.959000</td>\n      <td>1.060500</td>\n      <td>6.490375</td>\n      <td>5.607250</td>\n      <td>7.756000</td>\n      <td>45.320000</td>\n      <td>...</td>\n      <td>6.015850</td>\n      <td>7.136100</td>\n      <td>1.346175</td>\n      <td>1070.640000</td>\n      <td>101.250000</td>\n      <td>6.872275</td>\n      <td>0.964600</td>\n      <td>30.260000</td>\n      <td>31.670000</td>\n      <td>1.923500</td>\n    </tr>\n    <tr>\n      <th>50%</th>\n      <td>0.760000</td>\n      <td>1.217150</td>\n      <td>0.690150</td>\n      <td>1.567000</td>\n      <td>2.352150</td>\n      <td>1.246100</td>\n      <td>6.878050</td>\n      <td>6.115700</td>\n      <td>7.778250</td>\n      <td>48.365000</td>\n      <td>...</td>\n      <td>6.856850</td>\n      <td>8.274750</td>\n      <td>1.405850</td>\n      <td>1129.760000</td>\n      <td>113.400000</td>\n      <td>7.809450</td>\n      <td>1.026900</td>\n      <td>31.600000</td>\n      <td>33.705000</td>\n      <td>4.289300</td>\n    </tr>\n    <tr>\n      <th>75%</th>\n      <td>0.892725</td>\n      <td>1.328200</td>\n      <td>0.752625</td>\n      <td>1.687025</td>\n      <td>3.175000</td>\n      <td>1.339625</td>\n      <td>8.276500</td>\n      <td>6.734700</td>\n      <td>7.799700</td>\n      <td>63.670000</td>\n      <td>...</td>\n      <td>8.368400</td>\n      <td>12.041125</td>\n      <td>1.672325</td>\n      <td>1184.000000</td>\n      <td>133.725000</td>\n      <td>8.830225</td>\n      <td>1.262425</td>\n      <td>32.950000</td>\n      <td>39.250000</td>\n      <td>6.284200</td>\n    </tr>\n    <tr>\n      <th>max</th>\n      <td>1.102600</td>\n      <td>1.601000</td>\n      <td>0.881400</td>\n      <td>2.110400</td>\n      <td>5.920400</td>\n      <td>1.612800</td>\n      <td>8.280000</td>\n      <td>9.005000</td>\n      <td>7.849900</td>\n      <td>76.950000</td>\n      <td>...</td>\n      <td>11.684200</td>\n      <td>19.040000</td>\n      <td>1.854000</td>\n      <td>1570.100000</td>\n      <td>193.000000</td>\n      <td>11.027000</td>\n      <td>1.825000</td>\n      <td>35.210000</td>\n      <td>45.820000</td>\n      <td>302779.153700</td>\n    </tr>\n  </tbody>\n</table>\n<p>8 rows × 23 columns</p>\n</div>"}, "metadata": {}, "execution_count": 8}], "source": ["# final check to see if the series are OK\n", "forex.info()\n", "forex.describe()"]}, {"source": [], "cell_type": "markdown", "metadata": {}}, {"source": ["## b. GDP"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["'''\n", "I learned from the source documentation that if I use 'squeeze=True' argument, I can acquire the data as a Series, instead of a Dataframe.\n", "'''\n", "# read the data\n", "file = \"./DATA/gdp_GBR.csv\"\n", "gdpGBP = pd.read_csv(file, index_col='Date', parse_dates=True, squeeze=True)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Date\n", "2000-01-01    100.905789\n", "2000-02-01    100.885350\n", "2000-03-01    100.825291\n", "2000-04-01    100.731762\n", "2000-05-01    100.610625\n", "                 ...    \n", "2020-01-01     99.596489\n", "2020-02-01     99.559074\n", "2020-03-01     93.090995\n", "2020-04-01     84.111275\n", "2020-05-01     79.588906\n", "Name: GDP, Length: 245, dtype: float64"]}, "metadata": {}, "execution_count": 10}], "source": ["gdpGBP"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# read the data\n", "file = \"./DATA/gdp_USA.csv\"\n", "gdpUSD = pd.read_csv(file, index_col='Date', parse_dates=True, squeeze=True)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Date\n", "2000-01-01    101.451842\n", "2000-02-01    101.516006\n", "2000-03-01    101.593712\n", "2000-04-01    101.668688\n", "2000-05-01    101.714968\n", "                 ...    \n", "2020-01-01     99.951155\n", "2020-02-01     99.970682\n", "2020-03-01     95.038084\n", "2020-04-01     91.904561\n", "2020-05-01     90.264283\n", "Name: GDP, Length: 245, dtype: float64"]}, "metadata": {}, "execution_count": 12}], "source": ["gdpUSD"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                gdpGBP      gdpUSD\n", "Date                              \n", "2000-01-01  100.905789  101.451842\n", "2000-02-01  100.885350  101.516006\n", "2000-03-01  100.825291  101.593712\n", "2000-04-01  100.731762  101.668688\n", "2000-05-01  100.610625  101.714968\n", "...                ...         ...\n", "2020-01-01   99.596489   99.951155\n", "2020-02-01   99.559074   99.970682\n", "2020-03-01   93.090995   95.038084\n", "2020-04-01   84.111275   91.904561\n", "2020-05-01   79.588906   90.264283\n", "\n", "[245 rows x 2 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>gdpGBP</th>\n      <th>gdpUSD</th>\n    </tr>\n    <tr>\n      <th>Date</th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2000-01-01</th>\n      <td>100.905789</td>\n      <td>101.451842</td>\n    </tr>\n    <tr>\n      <th>2000-02-01</th>\n      <td>100.885350</td>\n      <td>101.516006</td>\n    </tr>\n    <tr>\n      <th>2000-03-01</th>\n      <td>100.825291</td>\n      <td>101.593712</td>\n    </tr>\n    <tr>\n      <th>2000-04-01</th>\n      <td>100.731762</td>\n      <td>101.668688</td>\n    </tr>\n    <tr>\n      <th>2000-05-01</th>\n      <td>100.610625</td>\n      <td>101.714968</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2020-01-01</th>\n      <td>99.596489</td>\n      <td>99.951155</td>\n    </tr>\n    <tr>\n      <th>2020-02-01</th>\n      <td>99.559074</td>\n      <td>99.970682</td>\n    </tr>\n    <tr>\n      <th>2020-03-01</th>\n      <td>93.090995</td>\n      <td>95.038084</td>\n    </tr>\n    <tr>\n      <th>2020-04-01</th>\n      <td>84.111275</td>\n      <td>91.904561</td>\n    </tr>\n    <tr>\n      <th>2020-05-01</th>\n      <td>79.588906</td>\n      <td>90.264283</td>\n    </tr>\n  </tbody>\n</table>\n<p>245 rows × 2 columns</p>\n</div>"}, "metadata": {}, "execution_count": 13}], "source": ["# concatanate two series and make them a dataframe to be merged with the previous one\n", "gdp = pd.concat([gdpGBP, gdpUSD], axis=1, keys=['gdpGBP', 'gdpUSD'])\n", "gdp"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["               AUD     EUR     NZD     GBP     BRL     CAD     CNY     DKK  \\\n", "Date                                                                         \n", "2000-01-01     NaN     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "2000-01-03  0.6591  1.0155  0.5254  1.6270  1.8050  1.4465  8.2798  7.3290   \n", "2000-01-04  0.6562  1.0309  0.5198  1.6370  1.8405  1.4518  8.2799  7.2180   \n", "2000-01-05  0.6550  1.0335  0.5171  1.6415  1.8560  1.4518  8.2798  7.2080   \n", "2000-01-06  0.6540  1.0324  0.5145  1.6475  1.8400  1.4571  8.2797  7.2125   \n", "...            ...     ...     ...     ...     ...     ...     ...     ...   \n", "2020-08-17  0.7211  1.1869  0.6547  1.3105  5.4734  1.3202  6.9318  6.2727   \n", "2020-08-18  0.7235  1.1928  0.6588  1.3228  5.4845  1.3181  6.9215  6.2406   \n", "2020-08-19  0.7234  1.1898  0.6607  1.3191  5.5045  1.3173  6.9192  6.2580   \n", "2020-08-20  0.7178  1.1862  0.6519  1.3190  5.6370  1.3177  6.9143  6.2783   \n", "2020-08-21  0.7156  1.1775  0.6533  1.3098  5.5949  1.3201  6.9179  6.3235   \n", "\n", "               HKD    INR  ...     SGD      KRW     LKR     SEK     CHF  \\\n", "Date                       ...                                            \n", "2000-01-01     NaN    NaN  ...     NaN      NaN     NaN     NaN     NaN   \n", "2000-01-03  7.7765  43.55  ...  1.6563  1128.00   72.30  8.4430  1.5808   \n", "2000-01-04  7.7775  43.55  ...  1.6535  1122.50   72.65  8.3600  1.5565   \n", "2000-01-05  7.7780  43.55  ...  1.6560  1135.00   72.95  8.3530  1.5526   \n", "2000-01-06  7.7785  43.55  ...  1.6655  1146.50   72.95  8.3675  1.5540   \n", "...            ...    ...  ...     ...      ...     ...     ...     ...   \n", "2020-08-17  7.7503  74.74  ...  1.3679  1184.17  183.13  8.6918  0.9058   \n", "2020-08-18  7.7501  74.62  ...  1.3647  1184.74  184.23  8.6481  0.9029   \n", "2020-08-19  7.7500  74.85  ...  1.3660  1181.06  186.10  8.6574  0.9114   \n", "2020-08-20  7.7500  75.01  ...  1.3676  1187.66  184.35  8.7060  0.9082   \n", "2020-08-21  7.7502  74.92  ...  1.3723  1191.84  184.50  8.7988  0.9131   \n", "\n", "              TWD    THB          VEB      gdpGBP      gdpUSD  \n", "Date                                                           \n", "2000-01-01    NaN    NaN          NaN  100.905789  101.451842  \n", "2000-01-03  31.38  36.97       0.6498         NaN         NaN  \n", "2000-01-04  30.60  37.13       0.6503         NaN         NaN  \n", "2000-01-05  30.80  37.10       0.6515         NaN         NaN  \n", "2000-01-06  31.75  37.62       0.6503         NaN         NaN  \n", "...           ...    ...          ...         ...         ...  \n", "2020-08-17  29.38  31.15  293920.7455         NaN         NaN  \n", "2020-08-18  29.39  31.16  289550.7506         NaN         NaN  \n", "2020-08-19  29.37  31.25  291796.8124         NaN         NaN  \n", "2020-08-20  29.42  31.41  296027.2710         NaN         NaN  \n", "2020-08-21  29.40  31.56  302779.1537         NaN         NaN  \n", "\n", "[5262 rows x 25 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>AUD</th>\n      <th>EUR</th>\n      <th>NZD</th>\n      <th>GBP</th>\n      <th>BRL</th>\n      <th>CAD</th>\n      <th>CNY</th>\n      <th>DKK</th>\n      <th>HKD</th>\n      <th>INR</th>\n      <th>...</th>\n      <th>SGD</th>\n      <th>KRW</th>\n      <th>LKR</th>\n      <th>SEK</th>\n      <th>CHF</th>\n      <th>TWD</th>\n      <th>THB</th>\n      <th>VEB</th>\n      <th>gdpGBP</th>\n      <th>gdpUSD</th>\n    </tr>\n    <tr>\n      <th>Date</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2000-01-01</th>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>100.905789</td>\n      <td>101.451842</td>\n    </tr>\n    <tr>\n      <th>2000-01-03</th>\n      <td>0.6591</td>\n      <td>1.0155</td>\n      <td>0.5254</td>\n      <td>1.6270</td>\n      <td>1.8050</td>\n      <td>1.4465</td>\n      <td>8.2798</td>\n      <td>7.3290</td>\n      <td>7.7765</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>1.6563</td>\n      <td>1128.00</td>\n      <td>72.30</td>\n      <td>8.4430</td>\n      <td>1.5808</td>\n      <td>31.38</td>\n      <td>36.97</td>\n      <td>0.6498</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2000-01-04</th>\n      <td>0.6562</td>\n      <td>1.0309</td>\n      <td>0.5198</td>\n      <td>1.6370</td>\n      <td>1.8405</td>\n      <td>1.4518</td>\n      <td>8.2799</td>\n      <td>7.2180</td>\n      <td>7.7775</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>1.6535</td>\n      <td>1122.50</td>\n      <td>72.65</td>\n      <td>8.3600</td>\n      <td>1.5565</td>\n      <td>30.60</td>\n      <td>37.13</td>\n      <td>0.6503</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2000-01-05</th>\n      <td>0.6550</td>\n      <td>1.0335</td>\n      <td>0.5171</td>\n      <td>1.6415</td>\n      <td>1.8560</td>\n      <td>1.4518</td>\n      <td>8.2798</td>\n      <td>7.2080</td>\n      <td>7.7780</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>1.6560</td>\n      <td>1135.00</td>\n      <td>72.95</td>\n      <td>8.3530</td>\n      <td>1.5526</td>\n      <td>30.80</td>\n      <td>37.10</td>\n      <td>0.6515</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2000-01-06</th>\n      <td>0.6540</td>\n      <td>1.0324</td>\n      <td>0.5145</td>\n      <td>1.6475</td>\n      <td>1.8400</td>\n      <td>1.4571</td>\n      <td>8.2797</td>\n      <td>7.2125</td>\n      <td>7.7785</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>1.6655</td>\n      <td>1146.50</td>\n      <td>72.95</td>\n      <td>8.3675</td>\n      <td>1.5540</td>\n      <td>31.75</td>\n      <td>37.62</td>\n      <td>0.6503</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2020-08-17</th>\n      <td>0.7211</td>\n      <td>1.1869</td>\n      <td>0.6547</td>\n      <td>1.3105</td>\n      <td>5.4734</td>\n      <td>1.3202</td>\n      <td>6.9318</td>\n      <td>6.2727</td>\n      <td>7.7503</td>\n      <td>74.74</td>\n      <td>...</td>\n      <td>1.3679</td>\n      <td>1184.17</td>\n      <td>183.13</td>\n      <td>8.6918</td>\n      <td>0.9058</td>\n      <td>29.38</td>\n      <td>31.15</td>\n      <td>293920.7455</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2020-08-18</th>\n      <td>0.7235</td>\n      <td>1.1928</td>\n      <td>0.6588</td>\n      <td>1.3228</td>\n      <td>5.4845</td>\n      <td>1.3181</td>\n      <td>6.9215</td>\n      <td>6.2406</td>\n      <td>7.7501</td>\n      <td>74.62</td>\n      <td>...</td>\n      <td>1.3647</td>\n      <td>1184.74</td>\n      <td>184.23</td>\n      <td>8.6481</td>\n      <td>0.9029</td>\n      <td>29.39</td>\n      <td>31.16</td>\n      <td>289550.7506</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2020-08-19</th>\n      <td>0.7234</td>\n      <td>1.1898</td>\n      <td>0.6607</td>\n      <td>1.3191</td>\n      <td>5.5045</td>\n      <td>1.3173</td>\n      <td>6.9192</td>\n      <td>6.2580</td>\n      <td>7.7500</td>\n      <td>74.85</td>\n      <td>...</td>\n      <td>1.3660</td>\n      <td>1181.06</td>\n      <td>186.10</td>\n      <td>8.6574</td>\n      <td>0.9114</td>\n      <td>29.37</td>\n      <td>31.25</td>\n      <td>291796.8124</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2020-08-20</th>\n      <td>0.7178</td>\n      <td>1.1862</td>\n      <td>0.6519</td>\n      <td>1.3190</td>\n      <td>5.6370</td>\n      <td>1.3177</td>\n      <td>6.9143</td>\n      <td>6.2783</td>\n      <td>7.7500</td>\n      <td>75.01</td>\n      <td>...</td>\n      <td>1.3676</td>\n      <td>1187.66</td>\n      <td>184.35</td>\n      <td>8.7060</td>\n      <td>0.9082</td>\n      <td>29.42</td>\n      <td>31.41</td>\n      <td>296027.2710</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2020-08-21</th>\n      <td>0.7156</td>\n      <td>1.1775</td>\n      <td>0.6533</td>\n      <td>1.3098</td>\n      <td>5.5949</td>\n      <td>1.3201</td>\n      <td>6.9179</td>\n      <td>6.3235</td>\n      <td>7.7502</td>\n      <td>74.92</td>\n      <td>...</td>\n      <td>1.3723</td>\n      <td>1191.84</td>\n      <td>184.50</td>\n      <td>8.7988</td>\n      <td>0.9131</td>\n      <td>29.40</td>\n      <td>31.56</td>\n      <td>302779.1537</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n  </tbody>\n</table>\n<p>5262 rows × 25 columns</p>\n</div>"}, "metadata": {}, "execution_count": 14}], "source": ["# I got some help with merging\n", "# https://stackoverflow.com/a/********/********\n", "merged = pd.merge(forex, gdp, left_index=True, right_index=True, how='outer')\n", "merged"]}, {"source": ["## c. Current Account to GDP"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["         GBR_Value  USA_Value\n", "Date                         \n", "Q4-1999  -1.734671  -3.300611\n", "Q1-2000  -2.224836  -3.789797\n", "Q2-2000  -2.542472  -3.762491\n", "Q3-2000  -2.318003  -4.049449\n", "Q4-2000  -1.971086  -4.072373\n", "...            ...        ...\n", "Q1-2019  -6.516039  -2.398563\n", "Q2-2019  -3.895734  -2.394594\n", "Q3-2019  -4.004894  -2.257979\n", "Q4-2019  -1.652522  -1.918832\n", "Q1-2020  -3.837938  -2.068833\n", "\n", "[82 rows x 2 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>GBR_Value</th>\n      <th>USA_Value</th>\n    </tr>\n    <tr>\n      <th>Date</th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>Q4-1999</th>\n      <td>-1.734671</td>\n      <td>-3.300611</td>\n    </tr>\n    <tr>\n      <th>Q1-2000</th>\n      <td>-2.224836</td>\n      <td>-3.789797</td>\n    </tr>\n    <tr>\n      <th>Q2-2000</th>\n      <td>-2.542472</td>\n      <td>-3.762491</td>\n    </tr>\n    <tr>\n      <th>Q3-2000</th>\n      <td>-2.318003</td>\n      <td>-4.049449</td>\n    </tr>\n    <tr>\n      <th>Q4-2000</th>\n      <td>-1.971086</td>\n      <td>-4.072373</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>Q1-2019</th>\n      <td>-6.516039</td>\n      <td>-2.398563</td>\n    </tr>\n    <tr>\n      <th>Q2-2019</th>\n      <td>-3.895734</td>\n      <td>-2.394594</td>\n    </tr>\n    <tr>\n      <th>Q3-2019</th>\n      <td>-4.004894</td>\n      <td>-2.257979</td>\n    </tr>\n    <tr>\n      <th>Q4-2019</th>\n      <td>-1.652522</td>\n      <td>-1.918832</td>\n    </tr>\n    <tr>\n      <th>Q1-2020</th>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n    </tr>\n  </tbody>\n</table>\n<p>82 rows × 2 columns</p>\n</div>"}, "metadata": {}, "execution_count": 15}], "source": ["# read the data\n", "file = \"./DATA/CAtoGDP.csv\"\n", "CAtoGDP = pd.read_csv(file, index_col='Date')\n", "CAtoGDP"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# drop the first row and start the series from 2000\n", "CAtoGDP = CAtoGDP[1:]"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Index(['Q1-2000', 'Q2-2000', 'Q3-2000', 'Q4-2000', 'Q1-2001', 'Q2-2001',\n", "       'Q3-2001', 'Q4-2001', 'Q1-2002', 'Q2-2002', 'Q3-2002', 'Q4-2002',\n", "       'Q1-2003', 'Q2-2003', 'Q3-2003', 'Q4-2003', 'Q1-2004', 'Q2-2004',\n", "       'Q3-2004', 'Q4-2004', 'Q1-2005', 'Q2-2005', 'Q3-2005', 'Q4-2005',\n", "       'Q1-2006', 'Q2-2006', 'Q3-2006', 'Q4-2006', 'Q1-2007', 'Q2-2007',\n", "       'Q3-2007', 'Q4-2007', 'Q1-2008', 'Q2-2008', 'Q3-2008', 'Q4-2008',\n", "       'Q1-2009', 'Q2-2009', 'Q3-2009', 'Q4-2009', 'Q1-2010', 'Q2-2010',\n", "       'Q3-2010', 'Q4-2010', 'Q1-2011', 'Q2-2011', 'Q3-2011', 'Q4-2011',\n", "       'Q1-2012', 'Q2-2012', 'Q3-2012', 'Q4-2012', 'Q1-2013', 'Q2-2013',\n", "       'Q3-2013', 'Q4-2013', 'Q1-2014', 'Q2-2014', 'Q3-2014', 'Q4-2014',\n", "       'Q1-2015', 'Q2-2015', 'Q3-2015', 'Q4-2015', 'Q1-2016', 'Q2-2016',\n", "       'Q3-2016', 'Q4-2016', 'Q1-2017', 'Q2-2017', 'Q3-2017', 'Q4-2017',\n", "       'Q1-2018', 'Q2-2018', 'Q3-2018', 'Q4-2018', 'Q1-2019', 'Q2-2019',\n", "       'Q3-2019', 'Q4-2019', 'Q1-2020'],\n", "      dtype='object', name='Date')"]}, "metadata": {}, "execution_count": 17}], "source": ["# examine the index (not a datetime object)\n", "CAtoGDP.index"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["            GBR_Value  USA_Value\n", "Date                            \n", "2000-01-01  -2.224836  -3.789797\n", "2000-04-01  -2.542472  -3.762491\n", "2000-07-01  -2.318003  -4.049449\n", "2000-10-01  -1.971086  -4.072373\n", "2001-01-01  -1.806949  -4.091941\n", "...               ...        ...\n", "2019-01-01  -6.516039  -2.398563\n", "2019-04-01  -3.895734  -2.394594\n", "2019-07-01  -4.004894  -2.257979\n", "2019-10-01  -1.652522  -1.918832\n", "2020-01-01  -3.837938  -2.068833\n", "\n", "[81 rows x 2 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>GBR_Value</th>\n      <th>USA_Value</th>\n    </tr>\n    <tr>\n      <th>Date</th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2000-01-01</th>\n      <td>-2.224836</td>\n      <td>-3.789797</td>\n    </tr>\n    <tr>\n      <th>2000-04-01</th>\n      <td>-2.542472</td>\n      <td>-3.762491</td>\n    </tr>\n    <tr>\n      <th>2000-07-01</th>\n      <td>-2.318003</td>\n      <td>-4.049449</td>\n    </tr>\n    <tr>\n      <th>2000-10-01</th>\n      <td>-1.971086</td>\n      <td>-4.072373</td>\n    </tr>\n    <tr>\n      <th>2001-01-01</th>\n      <td>-1.806949</td>\n      <td>-4.091941</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2019-01-01</th>\n      <td>-6.516039</td>\n      <td>-2.398563</td>\n    </tr>\n    <tr>\n      <th>2019-04-01</th>\n      <td>-3.895734</td>\n      <td>-2.394594</td>\n    </tr>\n    <tr>\n      <th>2019-07-01</th>\n      <td>-4.004894</td>\n      <td>-2.257979</td>\n    </tr>\n    <tr>\n      <th>2019-10-01</th>\n      <td>-1.652522</td>\n      <td>-1.918832</td>\n    </tr>\n    <tr>\n      <th>2020-01-01</th>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n    </tr>\n  </tbody>\n</table>\n<p>81 rows × 2 columns</p>\n</div>"}, "metadata": {}, "execution_count": 18}], "source": ["# I get the help of this post to convert the dates\n", "# https://stackoverflow.com/questions/53898482/clean-way-to-convert-quarterly-periods-to-datetime-in-pandas/53898522#53898522\n", "CAtoGDP.index = pd.to_datetime(CAtoGDP.index.str.replace(r'(Q\\d)-(\\d+)', r'\\2-\\1'))\n", "CAtoGDP"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2000-01-01', '2000-04-01', '2000-07-01', '2000-10-01',\n", "               '2001-01-01', '2001-04-01', '2001-07-01', '2001-10-01',\n", "               '2002-01-01', '2002-04-01', '2002-07-01', '2002-10-01',\n", "               '2003-01-01', '2003-04-01', '2003-07-01', '2003-10-01',\n", "               '2004-01-01', '2004-04-01', '2004-07-01', '2004-10-01',\n", "               '2005-01-01', '2005-04-01', '2005-07-01', '2005-10-01',\n", "               '2006-01-01', '2006-04-01', '2006-07-01', '2006-10-01',\n", "               '2007-01-01', '2007-04-01', '2007-07-01', '2007-10-01',\n", "               '2008-01-01', '2008-04-01', '2008-07-01', '2008-10-01',\n", "               '2009-01-01', '2009-04-01', '2009-07-01', '2009-10-01',\n", "               '2010-01-01', '2010-04-01', '2010-07-01', '2010-10-01',\n", "               '2011-01-01', '2011-04-01', '2011-07-01', '2011-10-01',\n", "               '2012-01-01', '2012-04-01', '2012-07-01', '2012-10-01',\n", "               '2013-01-01', '2013-04-01', '2013-07-01', '2013-10-01',\n", "               '2014-01-01', '2014-04-01', '2014-07-01', '2014-10-01',\n", "               '2015-01-01', '2015-04-01', '2015-07-01', '2015-10-01',\n", "               '2016-01-01', '2016-04-01', '2016-07-01', '2016-10-01',\n", "               '2017-01-01', '2017-04-01', '2017-07-01', '2017-10-01',\n", "               '2018-01-01', '2018-04-01', '2018-07-01', '2018-10-01',\n", "               '2019-01-01', '2019-04-01', '2019-07-01', '2019-10-01',\n", "               '2020-01-01'],\n", "              dtype='datetime64[ns]', name='Date', freq=None)"]}, "metadata": {}, "execution_count": 19}], "source": ["# Indexes became datetime object\n", "CAtoGDP.index"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["               AUD     EUR     NZD     GBP     BRL     CAD     CNY     DKK  \\\n", "Date                                                                         \n", "2000-01-01     NaN     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "2000-01-03  0.6591  1.0155  0.5254  1.6270  1.8050  1.4465  8.2798  7.3290   \n", "2000-01-04  0.6562  1.0309  0.5198  1.6370  1.8405  1.4518  8.2799  7.2180   \n", "2000-01-05  0.6550  1.0335  0.5171  1.6415  1.8560  1.4518  8.2798  7.2080   \n", "2000-01-06  0.6540  1.0324  0.5145  1.6475  1.8400  1.4571  8.2797  7.2125   \n", "...            ...     ...     ...     ...     ...     ...     ...     ...   \n", "2020-08-17  0.7211  1.1869  0.6547  1.3105  5.4734  1.3202  6.9318  6.2727   \n", "2020-08-18  0.7235  1.1928  0.6588  1.3228  5.4845  1.3181  6.9215  6.2406   \n", "2020-08-19  0.7234  1.1898  0.6607  1.3191  5.5045  1.3173  6.9192  6.2580   \n", "2020-08-20  0.7178  1.1862  0.6519  1.3190  5.6370  1.3177  6.9143  6.2783   \n", "2020-08-21  0.7156  1.1775  0.6533  1.3098  5.5949  1.3201  6.9179  6.3235   \n", "\n", "               HKD    INR  ...     LKR     SEK     CHF    TWD    THB  \\\n", "Date                       ...                                         \n", "2000-01-01     NaN    NaN  ...     NaN     NaN     NaN    NaN    NaN   \n", "2000-01-03  7.7765  43.55  ...   72.30  8.4430  1.5808  31.38  36.97   \n", "2000-01-04  7.7775  43.55  ...   72.65  8.3600  1.5565  30.60  37.13   \n", "2000-01-05  7.7780  43.55  ...   72.95  8.3530  1.5526  30.80  37.10   \n", "2000-01-06  7.7785  43.55  ...   72.95  8.3675  1.5540  31.75  37.62   \n", "...            ...    ...  ...     ...     ...     ...    ...    ...   \n", "2020-08-17  7.7503  74.74  ...  183.13  8.6918  0.9058  29.38  31.15   \n", "2020-08-18  7.7501  74.62  ...  184.23  8.6481  0.9029  29.39  31.16   \n", "2020-08-19  7.7500  74.85  ...  186.10  8.6574  0.9114  29.37  31.25   \n", "2020-08-20  7.7500  75.01  ...  184.35  8.7060  0.9082  29.42  31.41   \n", "2020-08-21  7.7502  74.92  ...  184.50  8.7988  0.9131  29.40  31.56   \n", "\n", "                    VEB      gdpGBP      gdpUSD  GBR_Value  USA_Value  \n", "Date                                                                   \n", "2000-01-01          NaN  100.905789  101.451842  -2.224836  -3.789797  \n", "2000-01-03       0.6498         NaN         NaN        NaN        NaN  \n", "2000-01-04       0.6503         NaN         NaN        NaN        NaN  \n", "2000-01-05       0.6515         NaN         NaN        NaN        NaN  \n", "2000-01-06       0.6503         NaN         NaN        NaN        NaN  \n", "...                 ...         ...         ...        ...        ...  \n", "2020-08-17  293920.7455         NaN         NaN        NaN        NaN  \n", "2020-08-18  289550.7506         NaN         NaN        NaN        NaN  \n", "2020-08-19  291796.8124         NaN         NaN        NaN        NaN  \n", "2020-08-20  296027.2710         NaN         NaN        NaN        NaN  \n", "2020-08-21  302779.1537         NaN         NaN        NaN        NaN  \n", "\n", "[5262 rows x 27 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>AUD</th>\n      <th>EUR</th>\n      <th>NZD</th>\n      <th>GBP</th>\n      <th>BRL</th>\n      <th>CAD</th>\n      <th>CNY</th>\n      <th>DKK</th>\n      <th>HKD</th>\n      <th>INR</th>\n      <th>...</th>\n      <th>LKR</th>\n      <th>SEK</th>\n      <th>CHF</th>\n      <th>TWD</th>\n      <th>THB</th>\n      <th>VEB</th>\n      <th>gdpGBP</th>\n      <th>gdpUSD</th>\n      <th>GBR_Value</th>\n      <th>USA_Value</th>\n    </tr>\n    <tr>\n      <th>Date</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2000-01-01</th>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>100.905789</td>\n      <td>101.451842</td>\n      <td>-2.224836</td>\n      <td>-3.789797</td>\n    </tr>\n    <tr>\n      <th>2000-01-03</th>\n      <td>0.6591</td>\n      <td>1.0155</td>\n      <td>0.5254</td>\n      <td>1.6270</td>\n      <td>1.8050</td>\n      <td>1.4465</td>\n      <td>8.2798</td>\n      <td>7.3290</td>\n      <td>7.7765</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>72.30</td>\n      <td>8.4430</td>\n      <td>1.5808</td>\n      <td>31.38</td>\n      <td>36.97</td>\n      <td>0.6498</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2000-01-04</th>\n      <td>0.6562</td>\n      <td>1.0309</td>\n      <td>0.5198</td>\n      <td>1.6370</td>\n      <td>1.8405</td>\n      <td>1.4518</td>\n      <td>8.2799</td>\n      <td>7.2180</td>\n      <td>7.7775</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>72.65</td>\n      <td>8.3600</td>\n      <td>1.5565</td>\n      <td>30.60</td>\n      <td>37.13</td>\n      <td>0.6503</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2000-01-05</th>\n      <td>0.6550</td>\n      <td>1.0335</td>\n      <td>0.5171</td>\n      <td>1.6415</td>\n      <td>1.8560</td>\n      <td>1.4518</td>\n      <td>8.2798</td>\n      <td>7.2080</td>\n      <td>7.7780</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>72.95</td>\n      <td>8.3530</td>\n      <td>1.5526</td>\n      <td>30.80</td>\n      <td>37.10</td>\n      <td>0.6515</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2000-01-06</th>\n      <td>0.6540</td>\n      <td>1.0324</td>\n      <td>0.5145</td>\n      <td>1.6475</td>\n      <td>1.8400</td>\n      <td>1.4571</td>\n      <td>8.2797</td>\n      <td>7.2125</td>\n      <td>7.7785</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>72.95</td>\n      <td>8.3675</td>\n      <td>1.5540</td>\n      <td>31.75</td>\n      <td>37.62</td>\n      <td>0.6503</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2020-08-17</th>\n      <td>0.7211</td>\n      <td>1.1869</td>\n      <td>0.6547</td>\n      <td>1.3105</td>\n      <td>5.4734</td>\n      <td>1.3202</td>\n      <td>6.9318</td>\n      <td>6.2727</td>\n      <td>7.7503</td>\n      <td>74.74</td>\n      <td>...</td>\n      <td>183.13</td>\n      <td>8.6918</td>\n      <td>0.9058</td>\n      <td>29.38</td>\n      <td>31.15</td>\n      <td>293920.7455</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2020-08-18</th>\n      <td>0.7235</td>\n      <td>1.1928</td>\n      <td>0.6588</td>\n      <td>1.3228</td>\n      <td>5.4845</td>\n      <td>1.3181</td>\n      <td>6.9215</td>\n      <td>6.2406</td>\n      <td>7.7501</td>\n      <td>74.62</td>\n      <td>...</td>\n      <td>184.23</td>\n      <td>8.6481</td>\n      <td>0.9029</td>\n      <td>29.39</td>\n      <td>31.16</td>\n      <td>289550.7506</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2020-08-19</th>\n      <td>0.7234</td>\n      <td>1.1898</td>\n      <td>0.6607</td>\n      <td>1.3191</td>\n      <td>5.5045</td>\n      <td>1.3173</td>\n      <td>6.9192</td>\n      <td>6.2580</td>\n      <td>7.7500</td>\n      <td>74.85</td>\n      <td>...</td>\n      <td>186.10</td>\n      <td>8.6574</td>\n      <td>0.9114</td>\n      <td>29.37</td>\n      <td>31.25</td>\n      <td>291796.8124</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2020-08-20</th>\n      <td>0.7178</td>\n      <td>1.1862</td>\n      <td>0.6519</td>\n      <td>1.3190</td>\n      <td>5.6370</td>\n      <td>1.3177</td>\n      <td>6.9143</td>\n      <td>6.2783</td>\n      <td>7.7500</td>\n      <td>75.01</td>\n      <td>...</td>\n      <td>184.35</td>\n      <td>8.7060</td>\n      <td>0.9082</td>\n      <td>29.42</td>\n      <td>31.41</td>\n      <td>296027.2710</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2020-08-21</th>\n      <td>0.7156</td>\n      <td>1.1775</td>\n      <td>0.6533</td>\n      <td>1.3098</td>\n      <td>5.5949</td>\n      <td>1.3201</td>\n      <td>6.9179</td>\n      <td>6.3235</td>\n      <td>7.7502</td>\n      <td>74.92</td>\n      <td>...</td>\n      <td>184.50</td>\n      <td>8.7988</td>\n      <td>0.9131</td>\n      <td>29.40</td>\n      <td>31.56</td>\n      <td>302779.1537</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n  </tbody>\n</table>\n<p>5262 rows × 27 columns</p>\n</div>"}, "metadata": {}, "execution_count": 20}], "source": ["# merge dataframes\n", "merged2 = pd.merge(merged, CAtoGDP, left_index=True, right_index=True, how='outer')\n", "merged2"]}, {"source": ["## d. Libor Rates (Interest Rates for GBP and USD)"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Date\n", "2001-01-02    5.81094\n", "2001-01-03     6.0975\n", "2001-01-04    5.57125\n", "2001-01-05    5.37813\n", "2001-01-08        5.5\n", "               ...   \n", "2020-09-14     0.0535\n", "2020-09-15    0.05088\n", "2020-09-16    0.05013\n", "2020-09-17     0.0515\n", "2020-09-18    0.05263\n", "Name: liborGB<PERSON>, Length: 5144, dtype: object"]}, "metadata": {}, "execution_count": 21}], "source": ["# read the data\n", "file = \"./DATA/libor_GBP.csv\"\n", "liborGBP = pd.read_csv(file, index_col='Date', parse_dates=True, squeeze=True)\n", "liborGBP"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Date\n", "2001-01-02    6.65125\n", "2001-01-03    6.65375\n", "2001-01-04    6.09625\n", "2001-01-05    6.01625\n", "2001-01-08      6.015\n", "               ...   \n", "2020-09-14    0.08388\n", "2020-09-15    0.08325\n", "2020-09-16     0.0825\n", "2020-09-17    0.08225\n", "2020-09-18    0.08313\n", "Name: liborUSD, Length: 5144, dtype: object"]}, "metadata": {}, "execution_count": 22}], "source": ["# read the data\n", "file = \"./DATA/libor_USD.csv\"\n", "liborUSD = pd.read_csv(file, index_col='Date', parse_dates=True, squeeze=True)\n", "liborUSD"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["           liborGBP liborUSD\n", "Date                        \n", "2001-01-02  5.81094  6.65125\n", "2001-01-03   6.0975  6.65375\n", "2001-01-04  5.57125  6.09625\n", "2001-01-05  5.37813  6.01625\n", "2001-01-08      5.5    6.015\n", "2001-01-09  5.34344  6.01125\n", "2001-01-10      5.5    6.015\n", "2001-01-11        6   6.0375\n", "2001-01-12  6.12188    6.025\n", "2001-01-15        6        .\n", "2001-01-16  6.09844  6.17625"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>liborGBP</th>\n      <th>liborUSD</th>\n    </tr>\n    <tr>\n      <th>Date</th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2001-01-02</th>\n      <td>5.81094</td>\n      <td>6.65125</td>\n    </tr>\n    <tr>\n      <th>2001-01-03</th>\n      <td>6.0975</td>\n      <td>6.65375</td>\n    </tr>\n    <tr>\n      <th>2001-01-04</th>\n      <td>5.57125</td>\n      <td>6.09625</td>\n    </tr>\n    <tr>\n      <th>2001-01-05</th>\n      <td>5.37813</td>\n      <td>6.01625</td>\n    </tr>\n    <tr>\n      <th>2001-01-08</th>\n      <td>5.5</td>\n      <td>6.015</td>\n    </tr>\n    <tr>\n      <th>2001-01-09</th>\n      <td>5.34344</td>\n      <td>6.01125</td>\n    </tr>\n    <tr>\n      <th>2001-01-10</th>\n      <td>5.5</td>\n      <td>6.015</td>\n    </tr>\n    <tr>\n      <th>2001-01-11</th>\n      <td>6</td>\n      <td>6.0375</td>\n    </tr>\n    <tr>\n      <th>2001-01-12</th>\n      <td>6.12188</td>\n      <td>6.025</td>\n    </tr>\n    <tr>\n      <th>2001-01-15</th>\n      <td>6</td>\n      <td>.</td>\n    </tr>\n    <tr>\n      <th>2001-01-16</th>\n      <td>6.09844</td>\n      <td>6.17625</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}, "execution_count": 23}], "source": ["# concatanate two series and make them a dataframe to be merged with the previous one\n", "libor = pd.concat([liborGBP, liborUSD], axis=1)\n", "libor[:11]"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"tags": []}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\nDatetimeIndex: 5144 entries, 2001-01-02 to 2020-09-18\nData columns (total 2 columns):\n #   Column    Non-Null Count  Dtype  \n---  ------    --------------  -----  \n 0   liborGBP  4984 non-null   float64\n 1   liborUSD  4854 non-null   float64\ndtypes: float64(2)\nmemory usage: 120.6 KB\n"]}], "source": ["# Some of the values are in \".\"\n", "# we need to change them and convert the series to numeric\n", "# https://stackoverflow.com/a/56474682/********\n", "libor = libor.apply(pd.to_numeric, errors='coerce')\n", "libor.info()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["               AUD     EUR     NZD     GBP     BRL     CAD     CNY     DKK  \\\n", "Date                                                                         \n", "2000-01-01     NaN     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "2000-01-03  0.6591  1.0155  0.5254  1.6270  1.8050  1.4465  8.2798  7.3290   \n", "2000-01-04  0.6562  1.0309  0.5198  1.6370  1.8405  1.4518  8.2799  7.2180   \n", "2000-01-05  0.6550  1.0335  0.5171  1.6415  1.8560  1.4518  8.2798  7.2080   \n", "2000-01-06  0.6540  1.0324  0.5145  1.6475  1.8400  1.4571  8.2797  7.2125   \n", "...            ...     ...     ...     ...     ...     ...     ...     ...   \n", "2020-09-14     NaN     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "2020-09-15     NaN     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "2020-09-16     NaN     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "2020-09-17     NaN     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "2020-09-18     NaN     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "\n", "               HKD    INR  ...     CHF    TWD    THB     VEB      gdpGBP  \\\n", "Date                       ...                                             \n", "2000-01-01     NaN    NaN  ...     NaN    NaN    NaN     NaN  100.905789   \n", "2000-01-03  7.7765  43.55  ...  1.5808  31.38  36.97  0.6498         NaN   \n", "2000-01-04  7.7775  43.55  ...  1.5565  30.60  37.13  0.6503         NaN   \n", "2000-01-05  7.7780  43.55  ...  1.5526  30.80  37.10  0.6515         NaN   \n", "2000-01-06  7.7785  43.55  ...  1.5540  31.75  37.62  0.6503         NaN   \n", "...            ...    ...  ...     ...    ...    ...     ...         ...   \n", "2020-09-14     NaN    NaN  ...     NaN    NaN    NaN     NaN         NaN   \n", "2020-09-15     NaN    NaN  ...     NaN    NaN    NaN     NaN         NaN   \n", "2020-09-16     NaN    NaN  ...     NaN    NaN    NaN     NaN         NaN   \n", "2020-09-17     NaN    NaN  ...     NaN    NaN    NaN     NaN         NaN   \n", "2020-09-18     NaN    NaN  ...     NaN    NaN    NaN     NaN         NaN   \n", "\n", "                gdpUSD  GBR_Value  USA_Value  liborGBP  liborUSD  \n", "Date                                                              \n", "2000-01-01  101.451842  -2.224836  -3.789797       NaN       NaN  \n", "2000-01-03         NaN        NaN        NaN       NaN       NaN  \n", "2000-01-04         NaN        NaN        NaN       NaN       NaN  \n", "2000-01-05         NaN        NaN        NaN       NaN       NaN  \n", "2000-01-06         NaN        NaN        NaN       NaN       NaN  \n", "...                ...        ...        ...       ...       ...  \n", "2020-09-14         NaN        NaN        NaN   0.05350   0.08388  \n", "2020-09-15         NaN        NaN        NaN   0.05088   0.08325  \n", "2020-09-16         NaN        NaN        NaN   0.05013   0.08250  \n", "2020-09-17         NaN        NaN        NaN   0.05150   0.08225  \n", "2020-09-18         NaN        NaN        NaN   0.05263   0.08313  \n", "\n", "[5469 rows x 29 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>AUD</th>\n      <th>EUR</th>\n      <th>NZD</th>\n      <th>GBP</th>\n      <th>BRL</th>\n      <th>CAD</th>\n      <th>CNY</th>\n      <th>DKK</th>\n      <th>HKD</th>\n      <th>INR</th>\n      <th>...</th>\n      <th>CHF</th>\n      <th>TWD</th>\n      <th>THB</th>\n      <th>VEB</th>\n      <th>gdpGBP</th>\n      <th>gdpUSD</th>\n      <th>GBR_Value</th>\n      <th>USA_Value</th>\n      <th>liborGBP</th>\n      <th>liborUSD</th>\n    </tr>\n    <tr>\n      <th>Date</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2000-01-01</th>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>100.905789</td>\n      <td>101.451842</td>\n      <td>-2.224836</td>\n      <td>-3.789797</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2000-01-03</th>\n      <td>0.6591</td>\n      <td>1.0155</td>\n      <td>0.5254</td>\n      <td>1.6270</td>\n      <td>1.8050</td>\n      <td>1.4465</td>\n      <td>8.2798</td>\n      <td>7.3290</td>\n      <td>7.7765</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>1.5808</td>\n      <td>31.38</td>\n      <td>36.97</td>\n      <td>0.6498</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2000-01-04</th>\n      <td>0.6562</td>\n      <td>1.0309</td>\n      <td>0.5198</td>\n      <td>1.6370</td>\n      <td>1.8405</td>\n      <td>1.4518</td>\n      <td>8.2799</td>\n      <td>7.2180</td>\n      <td>7.7775</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>1.5565</td>\n      <td>30.60</td>\n      <td>37.13</td>\n      <td>0.6503</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2000-01-05</th>\n      <td>0.6550</td>\n      <td>1.0335</td>\n      <td>0.5171</td>\n      <td>1.6415</td>\n      <td>1.8560</td>\n      <td>1.4518</td>\n      <td>8.2798</td>\n      <td>7.2080</td>\n      <td>7.7780</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>1.5526</td>\n      <td>30.80</td>\n      <td>37.10</td>\n      <td>0.6515</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2000-01-06</th>\n      <td>0.6540</td>\n      <td>1.0324</td>\n      <td>0.5145</td>\n      <td>1.6475</td>\n      <td>1.8400</td>\n      <td>1.4571</td>\n      <td>8.2797</td>\n      <td>7.2125</td>\n      <td>7.7785</td>\n      <td>43.55</td>\n      <td>...</td>\n      <td>1.5540</td>\n      <td>31.75</td>\n      <td>37.62</td>\n      <td>0.6503</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2020-09-14</th>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>0.05350</td>\n      <td>0.08388</td>\n    </tr>\n    <tr>\n      <th>2020-09-15</th>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>0.05088</td>\n      <td>0.08325</td>\n    </tr>\n    <tr>\n      <th>2020-09-16</th>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>0.05013</td>\n      <td>0.08250</td>\n    </tr>\n    <tr>\n      <th>2020-09-17</th>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>0.05150</td>\n      <td>0.08225</td>\n    </tr>\n    <tr>\n      <th>2020-09-18</th>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>0.05263</td>\n      <td>0.08313</td>\n    </tr>\n  </tbody>\n</table>\n<p>5469 rows × 29 columns</p>\n</div>"}, "metadata": {}, "execution_count": 25}], "source": ["# merged dataframes\n", "merged3 = pd.merge(merged2, libor, left_index=True, right_index=True, how='outer')\n", "merged3"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["           Date     AUD     EUR     NZD     GBP     BRL     CAD     CNY  \\\n", "0    2000-01-01     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "1    2000-01-03  0.6591  1.0155  0.5254  1.6270  1.8050  1.4465  8.2798   \n", "2    2000-01-04  0.6562  1.0309  0.5198  1.6370  1.8405  1.4518  8.2799   \n", "3    2000-01-05  0.6550  1.0335  0.5171  1.6415  1.8560  1.4518  8.2798   \n", "4    2000-01-06  0.6540  1.0324  0.5145  1.6475  1.8400  1.4571  8.2797   \n", "...         ...     ...     ...     ...     ...     ...     ...     ...   \n", "5464 2020-09-14     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "5465 2020-09-15     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "5466 2020-09-16     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "5467 2020-09-17     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "5468 2020-09-18     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "\n", "         DKK     HKD  ...     CHF    TWD    THB     VEB      gdpGBP  \\\n", "0        NaN     NaN  ...     NaN    NaN    NaN     NaN  100.905789   \n", "1     7.3290  7.7765  ...  1.5808  31.38  36.97  0.6498         NaN   \n", "2     7.2180  7.7775  ...  1.5565  30.60  37.13  0.6503         NaN   \n", "3     7.2080  7.7780  ...  1.5526  30.80  37.10  0.6515         NaN   \n", "4     7.2125  7.7785  ...  1.5540  31.75  37.62  0.6503         NaN   \n", "...      ...     ...  ...     ...    ...    ...     ...         ...   \n", "5464     NaN     NaN  ...     NaN    NaN    NaN     NaN         NaN   \n", "5465     NaN     NaN  ...     NaN    NaN    NaN     NaN         NaN   \n", "5466     NaN     NaN  ...     NaN    NaN    NaN     NaN         NaN   \n", "5467     NaN     NaN  ...     NaN    NaN    NaN     NaN         NaN   \n", "5468     NaN     NaN  ...     NaN    NaN    NaN     NaN         NaN   \n", "\n", "          gdpUSD  GBR_Value  USA_Value  liborGBP  liborUSD  \n", "0     101.451842  -2.224836  -3.789797       NaN       NaN  \n", "1            NaN        NaN        NaN       NaN       NaN  \n", "2            NaN        NaN        NaN       NaN       NaN  \n", "3            NaN        NaN        NaN       NaN       NaN  \n", "4            NaN        NaN        NaN       NaN       NaN  \n", "...          ...        ...        ...       ...       ...  \n", "5464         NaN        NaN        NaN   0.05350   0.08388  \n", "5465         NaN        NaN        NaN   0.05088   0.08325  \n", "5466         NaN        NaN        NaN   0.05013   0.08250  \n", "5467         NaN        NaN        NaN   0.05150   0.08225  \n", "5468         NaN        NaN        NaN   0.05263   0.08313  \n", "\n", "[5469 rows x 30 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>Date</th>\n      <th>AUD</th>\n      <th>EUR</th>\n      <th>NZD</th>\n      <th>GBP</th>\n      <th>BRL</th>\n      <th>CAD</th>\n      <th>CNY</th>\n      <th>DKK</th>\n      <th>HKD</th>\n      <th>...</th>\n      <th>CHF</th>\n      <th>TWD</th>\n      <th>THB</th>\n      <th>VEB</th>\n      <th>gdpGBP</th>\n      <th>gdpUSD</th>\n      <th>GBR_Value</th>\n      <th>USA_Value</th>\n      <th>liborGBP</th>\n      <th>liborUSD</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>2000-01-01</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>100.905789</td>\n      <td>101.451842</td>\n      <td>-2.224836</td>\n      <td>-3.789797</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2000-01-03</td>\n      <td>0.6591</td>\n      <td>1.0155</td>\n      <td>0.5254</td>\n      <td>1.6270</td>\n      <td>1.8050</td>\n      <td>1.4465</td>\n      <td>8.2798</td>\n      <td>7.3290</td>\n      <td>7.7765</td>\n      <td>...</td>\n      <td>1.5808</td>\n      <td>31.38</td>\n      <td>36.97</td>\n      <td>0.6498</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>2000-01-04</td>\n      <td>0.6562</td>\n      <td>1.0309</td>\n      <td>0.5198</td>\n      <td>1.6370</td>\n      <td>1.8405</td>\n      <td>1.4518</td>\n      <td>8.2799</td>\n      <td>7.2180</td>\n      <td>7.7775</td>\n      <td>...</td>\n      <td>1.5565</td>\n      <td>30.60</td>\n      <td>37.13</td>\n      <td>0.6503</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>2000-01-05</td>\n      <td>0.6550</td>\n      <td>1.0335</td>\n      <td>0.5171</td>\n      <td>1.6415</td>\n      <td>1.8560</td>\n      <td>1.4518</td>\n      <td>8.2798</td>\n      <td>7.2080</td>\n      <td>7.7780</td>\n      <td>...</td>\n      <td>1.5526</td>\n      <td>30.80</td>\n      <td>37.10</td>\n      <td>0.6515</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>2000-01-06</td>\n      <td>0.6540</td>\n      <td>1.0324</td>\n      <td>0.5145</td>\n      <td>1.6475</td>\n      <td>1.8400</td>\n      <td>1.4571</td>\n      <td>8.2797</td>\n      <td>7.2125</td>\n      <td>7.7785</td>\n      <td>...</td>\n      <td>1.5540</td>\n      <td>31.75</td>\n      <td>37.62</td>\n      <td>0.6503</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>5464</th>\n      <td>2020-09-14</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>0.05350</td>\n      <td>0.08388</td>\n    </tr>\n    <tr>\n      <th>5465</th>\n      <td>2020-09-15</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>0.05088</td>\n      <td>0.08325</td>\n    </tr>\n    <tr>\n      <th>5466</th>\n      <td>2020-09-16</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>0.05013</td>\n      <td>0.08250</td>\n    </tr>\n    <tr>\n      <th>5467</th>\n      <td>2020-09-17</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>0.05150</td>\n      <td>0.08225</td>\n    </tr>\n    <tr>\n      <th>5468</th>\n      <td>2020-09-18</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>0.05263</td>\n      <td>0.08313</td>\n    </tr>\n  </tbody>\n</table>\n<p>5469 rows × 30 columns</p>\n</div>"}, "metadata": {}, "execution_count": 26}], "source": ["# interpolation needs continuous datetime\n", "# or numerical objects for index values\n", "merged3.reset_index(level=0, inplace=True)\n", "merged3"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["           Date     AUD     EUR     NZD     GBP     BRL     CAD     CNY  \\\n", "0    2000-01-01     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "1    2000-01-03  0.6591  1.0155  0.5254  1.6270  1.8050  1.4465  8.2798   \n", "2    2000-01-04  0.6562  1.0309  0.5198  1.6370  1.8405  1.4518  8.2799   \n", "3    2000-01-05  0.6550  1.0335  0.5171  1.6415  1.8560  1.4518  8.2798   \n", "4    2000-01-06  0.6540  1.0324  0.5145  1.6475  1.8400  1.4571  8.2797   \n", "...         ...     ...     ...     ...     ...     ...     ...     ...   \n", "5464 2020-09-14     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "5465 2020-09-15     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "5466 2020-09-16     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "5467 2020-09-17     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "5468 2020-09-18     NaN     NaN     NaN     NaN     NaN     NaN     NaN   \n", "\n", "         DKK     HKD  ...     CHF    TWD    THB     VEB      gdpGBP  \\\n", "0        NaN     NaN  ...     NaN    NaN    NaN     NaN  100.905789   \n", "1     7.3290  7.7765  ...  1.5808  31.38  36.97  0.6498  100.904815   \n", "2     7.2180  7.7775  ...  1.5565  30.60  37.13  0.6503  100.903842   \n", "3     7.2080  7.7780  ...  1.5526  30.80  37.10  0.6515  100.902869   \n", "4     7.2125  7.7785  ...  1.5540  31.75  37.62  0.6503  100.901896   \n", "...      ...     ...  ...     ...    ...    ...     ...         ...   \n", "5464     NaN     NaN  ...     NaN    NaN    NaN     NaN   79.588906   \n", "5465     NaN     NaN  ...     NaN    NaN    NaN     NaN   79.588906   \n", "5466     NaN     NaN  ...     NaN    NaN    NaN     NaN   79.588906   \n", "5467     NaN     NaN  ...     NaN    NaN    NaN     NaN   79.588906   \n", "5468     NaN     NaN  ...     NaN    NaN    NaN     NaN   79.588906   \n", "\n", "          gdpUSD  GBR_Value  USA_Value  liborGBP  liborUSD  \n", "0     101.451842  -2.224836  -3.789797       NaN       NaN  \n", "1     101.454898  -2.229799  -3.789371       NaN       NaN  \n", "2     101.457953  -2.234762  -3.788944       NaN       NaN  \n", "3     101.461009  -2.239725  -3.788517       NaN       NaN  \n", "4     101.464064  -2.244688  -3.788091       NaN       NaN  \n", "...          ...        ...        ...       ...       ...  \n", "5464   90.264283  -3.837938  -2.068833   0.05350   0.08388  \n", "5465   90.264283  -3.837938  -2.068833   0.05088   0.08325  \n", "5466   90.264283  -3.837938  -2.068833   0.05013   0.08250  \n", "5467   90.264283  -3.837938  -2.068833   0.05150   0.08225  \n", "5468   90.264283  -3.837938  -2.068833   0.05263   0.08313  \n", "\n", "[5469 rows x 30 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>Date</th>\n      <th>AUD</th>\n      <th>EUR</th>\n      <th>NZD</th>\n      <th>GBP</th>\n      <th>BRL</th>\n      <th>CAD</th>\n      <th>CNY</th>\n      <th>DKK</th>\n      <th>HKD</th>\n      <th>...</th>\n      <th>CHF</th>\n      <th>TWD</th>\n      <th>THB</th>\n      <th>VEB</th>\n      <th>gdpGBP</th>\n      <th>gdpUSD</th>\n      <th>GBR_Value</th>\n      <th>USA_Value</th>\n      <th>liborGBP</th>\n      <th>liborUSD</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>2000-01-01</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>100.905789</td>\n      <td>101.451842</td>\n      <td>-2.224836</td>\n      <td>-3.789797</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2000-01-03</td>\n      <td>0.6591</td>\n      <td>1.0155</td>\n      <td>0.5254</td>\n      <td>1.6270</td>\n      <td>1.8050</td>\n      <td>1.4465</td>\n      <td>8.2798</td>\n      <td>7.3290</td>\n      <td>7.7765</td>\n      <td>...</td>\n      <td>1.5808</td>\n      <td>31.38</td>\n      <td>36.97</td>\n      <td>0.6498</td>\n      <td>100.904815</td>\n      <td>101.454898</td>\n      <td>-2.229799</td>\n      <td>-3.789371</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>2000-01-04</td>\n      <td>0.6562</td>\n      <td>1.0309</td>\n      <td>0.5198</td>\n      <td>1.6370</td>\n      <td>1.8405</td>\n      <td>1.4518</td>\n      <td>8.2799</td>\n      <td>7.2180</td>\n      <td>7.7775</td>\n      <td>...</td>\n      <td>1.5565</td>\n      <td>30.60</td>\n      <td>37.13</td>\n      <td>0.6503</td>\n      <td>100.903842</td>\n      <td>101.457953</td>\n      <td>-2.234762</td>\n      <td>-3.788944</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>2000-01-05</td>\n      <td>0.6550</td>\n      <td>1.0335</td>\n      <td>0.5171</td>\n      <td>1.6415</td>\n      <td>1.8560</td>\n      <td>1.4518</td>\n      <td>8.2798</td>\n      <td>7.2080</td>\n      <td>7.7780</td>\n      <td>...</td>\n      <td>1.5526</td>\n      <td>30.80</td>\n      <td>37.10</td>\n      <td>0.6515</td>\n      <td>100.902869</td>\n      <td>101.461009</td>\n      <td>-2.239725</td>\n      <td>-3.788517</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>2000-01-06</td>\n      <td>0.6540</td>\n      <td>1.0324</td>\n      <td>0.5145</td>\n      <td>1.6475</td>\n      <td>1.8400</td>\n      <td>1.4571</td>\n      <td>8.2797</td>\n      <td>7.2125</td>\n      <td>7.7785</td>\n      <td>...</td>\n      <td>1.5540</td>\n      <td>31.75</td>\n      <td>37.62</td>\n      <td>0.6503</td>\n      <td>100.901896</td>\n      <td>101.464064</td>\n      <td>-2.244688</td>\n      <td>-3.788091</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>5464</th>\n      <td>2020-09-14</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05350</td>\n      <td>0.08388</td>\n    </tr>\n    <tr>\n      <th>5465</th>\n      <td>2020-09-15</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05088</td>\n      <td>0.08325</td>\n    </tr>\n    <tr>\n      <th>5466</th>\n      <td>2020-09-16</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05013</td>\n      <td>0.08250</td>\n    </tr>\n    <tr>\n      <th>5467</th>\n      <td>2020-09-17</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05150</td>\n      <td>0.08225</td>\n    </tr>\n    <tr>\n      <th>5468</th>\n      <td>2020-09-18</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>...</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05263</td>\n      <td>0.08313</td>\n    </tr>\n  </tbody>\n</table>\n<p>5469 rows × 30 columns</p>\n</div>"}, "metadata": {}, "execution_count": 27}], "source": ["# fill the missing values by interpolation\n", "merged3.iloc[:, -6:] = merged3.iloc[:, -6:].interpolate()\n", "merged3"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["           Date     AUD     EUR     NZD     GBP     BRL     CAD     CNY  \\\n", "1    2000-01-03  0.6591  1.0155  0.5254  1.6270  1.8050  1.4465  8.2798   \n", "2    2000-01-04  0.6562  1.0309  0.5198  1.6370  1.8405  1.4518  8.2799   \n", "3    2000-01-05  0.6550  1.0335  0.5171  1.6415  1.8560  1.4518  8.2798   \n", "4    2000-01-06  0.6540  1.0324  0.5145  1.6475  1.8400  1.4571  8.2797   \n", "5    2000-01-07  0.6548  1.0294  0.5160  1.6384  1.8310  1.4505  8.2794   \n", "...         ...     ...     ...     ...     ...     ...     ...     ...   \n", "5444 2020-08-17  0.7211  1.1869  0.6547  1.3105  5.4734  1.3202  6.9318   \n", "5445 2020-08-18  0.7235  1.1928  0.6588  1.3228  5.4845  1.3181  6.9215   \n", "5446 2020-08-19  0.7234  1.1898  0.6607  1.3191  5.5045  1.3173  6.9192   \n", "5447 2020-08-20  0.7178  1.1862  0.6519  1.3190  5.6370  1.3177  6.9143   \n", "5448 2020-08-21  0.7156  1.1775  0.6533  1.3098  5.5949  1.3201  6.9179   \n", "\n", "         DKK     HKD  ...     CHF    TWD    THB          VEB      gdpGBP  \\\n", "1     7.3290  7.7765  ...  1.5808  31.38  36.97       0.6498  100.904815   \n", "2     7.2180  7.7775  ...  1.5565  30.60  37.13       0.6503  100.903842   \n", "3     7.2080  7.7780  ...  1.5526  30.80  37.10       0.6515  100.902869   \n", "4     7.2125  7.7785  ...  1.5540  31.75  37.62       0.6503  100.901896   \n", "5     7.2285  7.7783  ...  1.5623  30.85  37.30       0.6508  100.900922   \n", "...      ...     ...  ...     ...    ...    ...          ...         ...   \n", "5444  6.2727  7.7503  ...  0.9058  29.38  31.15  293920.7455   79.588906   \n", "5445  6.2406  7.7501  ...  0.9029  29.39  31.16  289550.7506   79.588906   \n", "5446  6.2580  7.7500  ...  0.9114  29.37  31.25  291796.8124   79.588906   \n", "5447  6.2783  7.7500  ...  0.9082  29.42  31.41  296027.2710   79.588906   \n", "5448  6.3235  7.7502  ...  0.9131  29.40  31.56  302779.1537   79.588906   \n", "\n", "          gdpUSD  GBR_Value  USA_Value  liborGBP  liborUSD  \n", "1     101.454898  -2.229799  -3.789371       NaN       NaN  \n", "2     101.457953  -2.234762  -3.788944       NaN       NaN  \n", "3     101.461009  -2.239725  -3.788517       NaN       NaN  \n", "4     101.464064  -2.244688  -3.788091       NaN       NaN  \n", "5     101.467119  -2.249651  -3.787664       NaN       NaN  \n", "...          ...        ...        ...       ...       ...  \n", "5444   90.264283  -3.837938  -2.068833   0.05088   0.08475  \n", "5445   90.264283  -3.837938  -2.068833   0.04838   0.08363  \n", "5446   90.264283  -3.837938  -2.068833   0.04925   0.08463  \n", "5447   90.264283  -3.837938  -2.068833   0.05063   0.08300  \n", "5448   90.264283  -3.837938  -2.068833   0.05150   0.08175  \n", "\n", "[5172 rows x 30 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>Date</th>\n      <th>AUD</th>\n      <th>EUR</th>\n      <th>NZD</th>\n      <th>GBP</th>\n      <th>BRL</th>\n      <th>CAD</th>\n      <th>CNY</th>\n      <th>DKK</th>\n      <th>HKD</th>\n      <th>...</th>\n      <th>CHF</th>\n      <th>TWD</th>\n      <th>THB</th>\n      <th>VEB</th>\n      <th>gdpGBP</th>\n      <th>gdpUSD</th>\n      <th>GBR_Value</th>\n      <th>USA_Value</th>\n      <th>liborGBP</th>\n      <th>liborUSD</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>1</th>\n      <td>2000-01-03</td>\n      <td>0.6591</td>\n      <td>1.0155</td>\n      <td>0.5254</td>\n      <td>1.6270</td>\n      <td>1.8050</td>\n      <td>1.4465</td>\n      <td>8.2798</td>\n      <td>7.3290</td>\n      <td>7.7765</td>\n      <td>...</td>\n      <td>1.5808</td>\n      <td>31.38</td>\n      <td>36.97</td>\n      <td>0.6498</td>\n      <td>100.904815</td>\n      <td>101.454898</td>\n      <td>-2.229799</td>\n      <td>-3.789371</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>2000-01-04</td>\n      <td>0.6562</td>\n      <td>1.0309</td>\n      <td>0.5198</td>\n      <td>1.6370</td>\n      <td>1.8405</td>\n      <td>1.4518</td>\n      <td>8.2799</td>\n      <td>7.2180</td>\n      <td>7.7775</td>\n      <td>...</td>\n      <td>1.5565</td>\n      <td>30.60</td>\n      <td>37.13</td>\n      <td>0.6503</td>\n      <td>100.903842</td>\n      <td>101.457953</td>\n      <td>-2.234762</td>\n      <td>-3.788944</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>2000-01-05</td>\n      <td>0.6550</td>\n      <td>1.0335</td>\n      <td>0.5171</td>\n      <td>1.6415</td>\n      <td>1.8560</td>\n      <td>1.4518</td>\n      <td>8.2798</td>\n      <td>7.2080</td>\n      <td>7.7780</td>\n      <td>...</td>\n      <td>1.5526</td>\n      <td>30.80</td>\n      <td>37.10</td>\n      <td>0.6515</td>\n      <td>100.902869</td>\n      <td>101.461009</td>\n      <td>-2.239725</td>\n      <td>-3.788517</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>2000-01-06</td>\n      <td>0.6540</td>\n      <td>1.0324</td>\n      <td>0.5145</td>\n      <td>1.6475</td>\n      <td>1.8400</td>\n      <td>1.4571</td>\n      <td>8.2797</td>\n      <td>7.2125</td>\n      <td>7.7785</td>\n      <td>...</td>\n      <td>1.5540</td>\n      <td>31.75</td>\n      <td>37.62</td>\n      <td>0.6503</td>\n      <td>100.901896</td>\n      <td>101.464064</td>\n      <td>-2.244688</td>\n      <td>-3.788091</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>5</th>\n      <td>2000-01-07</td>\n      <td>0.6548</td>\n      <td>1.0294</td>\n      <td>0.5160</td>\n      <td>1.6384</td>\n      <td>1.8310</td>\n      <td>1.4505</td>\n      <td>8.2794</td>\n      <td>7.2285</td>\n      <td>7.7783</td>\n      <td>...</td>\n      <td>1.5623</td>\n      <td>30.85</td>\n      <td>37.30</td>\n      <td>0.6508</td>\n      <td>100.900922</td>\n      <td>101.467119</td>\n      <td>-2.249651</td>\n      <td>-3.787664</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>5444</th>\n      <td>2020-08-17</td>\n      <td>0.7211</td>\n      <td>1.1869</td>\n      <td>0.6547</td>\n      <td>1.3105</td>\n      <td>5.4734</td>\n      <td>1.3202</td>\n      <td>6.9318</td>\n      <td>6.2727</td>\n      <td>7.7503</td>\n      <td>...</td>\n      <td>0.9058</td>\n      <td>29.38</td>\n      <td>31.15</td>\n      <td>293920.7455</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05088</td>\n      <td>0.08475</td>\n    </tr>\n    <tr>\n      <th>5445</th>\n      <td>2020-08-18</td>\n      <td>0.7235</td>\n      <td>1.1928</td>\n      <td>0.6588</td>\n      <td>1.3228</td>\n      <td>5.4845</td>\n      <td>1.3181</td>\n      <td>6.9215</td>\n      <td>6.2406</td>\n      <td>7.7501</td>\n      <td>...</td>\n      <td>0.9029</td>\n      <td>29.39</td>\n      <td>31.16</td>\n      <td>289550.7506</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.04838</td>\n      <td>0.08363</td>\n    </tr>\n    <tr>\n      <th>5446</th>\n      <td>2020-08-19</td>\n      <td>0.7234</td>\n      <td>1.1898</td>\n      <td>0.6607</td>\n      <td>1.3191</td>\n      <td>5.5045</td>\n      <td>1.3173</td>\n      <td>6.9192</td>\n      <td>6.2580</td>\n      <td>7.7500</td>\n      <td>...</td>\n      <td>0.9114</td>\n      <td>29.37</td>\n      <td>31.25</td>\n      <td>291796.8124</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.04925</td>\n      <td>0.08463</td>\n    </tr>\n    <tr>\n      <th>5447</th>\n      <td>2020-08-20</td>\n      <td>0.7178</td>\n      <td>1.1862</td>\n      <td>0.6519</td>\n      <td>1.3190</td>\n      <td>5.6370</td>\n      <td>1.3177</td>\n      <td>6.9143</td>\n      <td>6.2783</td>\n      <td>7.7500</td>\n      <td>...</td>\n      <td>0.9082</td>\n      <td>29.42</td>\n      <td>31.41</td>\n      <td>296027.2710</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05063</td>\n      <td>0.08300</td>\n    </tr>\n    <tr>\n      <th>5448</th>\n      <td>2020-08-21</td>\n      <td>0.7156</td>\n      <td>1.1775</td>\n      <td>0.6533</td>\n      <td>1.3098</td>\n      <td>5.5949</td>\n      <td>1.3201</td>\n      <td>6.9179</td>\n      <td>6.3235</td>\n      <td>7.7502</td>\n      <td>...</td>\n      <td>0.9131</td>\n      <td>29.40</td>\n      <td>31.56</td>\n      <td>302779.1537</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05150</td>\n      <td>0.08175</td>\n    </tr>\n  </tbody>\n</table>\n<p>5172 rows × 30 columns</p>\n</div>"}, "metadata": {}, "execution_count": 28}], "source": ["# drop rows where there is no data for GBP (or any forex)\n", "merged3 = merged3[merged3.GBP.notna()]\n", "merged3"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["           Date     AUD     EUR     NZD     GBP     BRL     CAD     CNY  \\\n", "257  2001-01-02  0.5592  0.9465  0.4432  1.4977  1.9380  1.4963  8.2779   \n", "258  2001-01-03  0.5635  0.9473  0.4463  1.5045  1.9460  1.4982  8.2773   \n", "259  2001-01-04  0.5655  0.9448  0.4457  1.4930  1.9380  1.4985  8.2781   \n", "260  2001-01-05  0.5712  0.9535  0.4518  1.4990  1.9530  1.5003  8.2775   \n", "261  2001-01-08  0.5660  0.9486  0.4505  1.4969  1.9540  1.4944  8.2778   \n", "...         ...     ...     ...     ...     ...     ...     ...     ...   \n", "5444 2020-08-17  0.7211  1.1869  0.6547  1.3105  5.4734  1.3202  6.9318   \n", "5445 2020-08-18  0.7235  1.1928  0.6588  1.3228  5.4845  1.3181  6.9215   \n", "5446 2020-08-19  0.7234  1.1898  0.6607  1.3191  5.5045  1.3173  6.9192   \n", "5447 2020-08-20  0.7178  1.1862  0.6519  1.3190  5.6370  1.3177  6.9143   \n", "5448 2020-08-21  0.7156  1.1775  0.6533  1.3098  5.5949  1.3201  6.9179   \n", "\n", "         DKK     HKD  ...     CHF     TWD    THB          VEB     gdpGBP  \\\n", "257   7.8845  7.8000  ...  1.6075  33.000  43.79       0.7008  99.977643   \n", "258   7.8750  7.8000  ...  1.6025  33.078  43.70       0.7002  99.980866   \n", "259   7.8991  7.7998  ...  1.6115  33.000  43.53       0.6994  99.984089   \n", "260   7.8260  7.7993  ...  1.6025  32.927  43.26       0.6988  99.987312   \n", "261   7.8705  7.7998  ...  1.6076  32.850  42.95       0.6990  99.990535   \n", "...      ...     ...  ...     ...     ...    ...          ...        ...   \n", "5444  6.2727  7.7503  ...  0.9058  29.380  31.15  293920.7455  79.588906   \n", "5445  6.2406  7.7501  ...  0.9029  29.390  31.16  289550.7506  79.588906   \n", "5446  6.2580  7.7500  ...  0.9114  29.370  31.25  291796.8124  79.588906   \n", "5447  6.2783  7.7500  ...  0.9082  29.420  31.41  296027.2710  79.588906   \n", "5448  6.3235  7.7502  ...  0.9131  29.400  31.56  302779.1537  79.588906   \n", "\n", "          gdpUSD  GBR_Value  USA_Value  liborGBP  liborUSD  \n", "257   100.815277  -1.815904  -4.084297   5.81094   6.65125  \n", "258   100.807191  -1.824859  -4.076654   6.09750   6.65375  \n", "259   100.799105  -1.833815  -4.069010   5.57125   6.09625  \n", "260   100.791019  -1.842770  -4.061367   5.37813   6.01625  \n", "261   100.782933  -1.851726  -4.053723   5.50000   6.01500  \n", "...          ...        ...        ...       ...       ...  \n", "5444   90.264283  -3.837938  -2.068833   0.05088   0.08475  \n", "5445   90.264283  -3.837938  -2.068833   0.04838   0.08363  \n", "5446   90.264283  -3.837938  -2.068833   0.04925   0.08463  \n", "5447   90.264283  -3.837938  -2.068833   0.05063   0.08300  \n", "5448   90.264283  -3.837938  -2.068833   0.05150   0.08175  \n", "\n", "[4920 rows x 30 columns]"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>Date</th>\n      <th>AUD</th>\n      <th>EUR</th>\n      <th>NZD</th>\n      <th>GBP</th>\n      <th>BRL</th>\n      <th>CAD</th>\n      <th>CNY</th>\n      <th>DKK</th>\n      <th>HKD</th>\n      <th>...</th>\n      <th>CHF</th>\n      <th>TWD</th>\n      <th>THB</th>\n      <th>VEB</th>\n      <th>gdpGBP</th>\n      <th>gdpUSD</th>\n      <th>GBR_Value</th>\n      <th>USA_Value</th>\n      <th>liborGBP</th>\n      <th>liborUSD</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>257</th>\n      <td>2001-01-02</td>\n      <td>0.5592</td>\n      <td>0.9465</td>\n      <td>0.4432</td>\n      <td>1.4977</td>\n      <td>1.9380</td>\n      <td>1.4963</td>\n      <td>8.2779</td>\n      <td>7.8845</td>\n      <td>7.8000</td>\n      <td>...</td>\n      <td>1.6075</td>\n      <td>33.000</td>\n      <td>43.79</td>\n      <td>0.7008</td>\n      <td>99.977643</td>\n      <td>100.815277</td>\n      <td>-1.815904</td>\n      <td>-4.084297</td>\n      <td>5.81094</td>\n      <td>6.65125</td>\n    </tr>\n    <tr>\n      <th>258</th>\n      <td>2001-01-03</td>\n      <td>0.5635</td>\n      <td>0.9473</td>\n      <td>0.4463</td>\n      <td>1.5045</td>\n      <td>1.9460</td>\n      <td>1.4982</td>\n      <td>8.2773</td>\n      <td>7.8750</td>\n      <td>7.8000</td>\n      <td>...</td>\n      <td>1.6025</td>\n      <td>33.078</td>\n      <td>43.70</td>\n      <td>0.7002</td>\n      <td>99.980866</td>\n      <td>100.807191</td>\n      <td>-1.824859</td>\n      <td>-4.076654</td>\n      <td>6.09750</td>\n      <td>6.65375</td>\n    </tr>\n    <tr>\n      <th>259</th>\n      <td>2001-01-04</td>\n      <td>0.5655</td>\n      <td>0.9448</td>\n      <td>0.4457</td>\n      <td>1.4930</td>\n      <td>1.9380</td>\n      <td>1.4985</td>\n      <td>8.2781</td>\n      <td>7.8991</td>\n      <td>7.7998</td>\n      <td>...</td>\n      <td>1.6115</td>\n      <td>33.000</td>\n      <td>43.53</td>\n      <td>0.6994</td>\n      <td>99.984089</td>\n      <td>100.799105</td>\n      <td>-1.833815</td>\n      <td>-4.069010</td>\n      <td>5.57125</td>\n      <td>6.09625</td>\n    </tr>\n    <tr>\n      <th>260</th>\n      <td>2001-01-05</td>\n      <td>0.5712</td>\n      <td>0.9535</td>\n      <td>0.4518</td>\n      <td>1.4990</td>\n      <td>1.9530</td>\n      <td>1.5003</td>\n      <td>8.2775</td>\n      <td>7.8260</td>\n      <td>7.7993</td>\n      <td>...</td>\n      <td>1.6025</td>\n      <td>32.927</td>\n      <td>43.26</td>\n      <td>0.6988</td>\n      <td>99.987312</td>\n      <td>100.791019</td>\n      <td>-1.842770</td>\n      <td>-4.061367</td>\n      <td>5.37813</td>\n      <td>6.01625</td>\n    </tr>\n    <tr>\n      <th>261</th>\n      <td>2001-01-08</td>\n      <td>0.5660</td>\n      <td>0.9486</td>\n      <td>0.4505</td>\n      <td>1.4969</td>\n      <td>1.9540</td>\n      <td>1.4944</td>\n      <td>8.2778</td>\n      <td>7.8705</td>\n      <td>7.7998</td>\n      <td>...</td>\n      <td>1.6076</td>\n      <td>32.850</td>\n      <td>42.95</td>\n      <td>0.6990</td>\n      <td>99.990535</td>\n      <td>100.782933</td>\n      <td>-1.851726</td>\n      <td>-4.053723</td>\n      <td>5.50000</td>\n      <td>6.01500</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>5444</th>\n      <td>2020-08-17</td>\n      <td>0.7211</td>\n      <td>1.1869</td>\n      <td>0.6547</td>\n      <td>1.3105</td>\n      <td>5.4734</td>\n      <td>1.3202</td>\n      <td>6.9318</td>\n      <td>6.2727</td>\n      <td>7.7503</td>\n      <td>...</td>\n      <td>0.9058</td>\n      <td>29.380</td>\n      <td>31.15</td>\n      <td>293920.7455</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05088</td>\n      <td>0.08475</td>\n    </tr>\n    <tr>\n      <th>5445</th>\n      <td>2020-08-18</td>\n      <td>0.7235</td>\n      <td>1.1928</td>\n      <td>0.6588</td>\n      <td>1.3228</td>\n      <td>5.4845</td>\n      <td>1.3181</td>\n      <td>6.9215</td>\n      <td>6.2406</td>\n      <td>7.7501</td>\n      <td>...</td>\n      <td>0.9029</td>\n      <td>29.390</td>\n      <td>31.16</td>\n      <td>289550.7506</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.04838</td>\n      <td>0.08363</td>\n    </tr>\n    <tr>\n      <th>5446</th>\n      <td>2020-08-19</td>\n      <td>0.7234</td>\n      <td>1.1898</td>\n      <td>0.6607</td>\n      <td>1.3191</td>\n      <td>5.5045</td>\n      <td>1.3173</td>\n      <td>6.9192</td>\n      <td>6.2580</td>\n      <td>7.7500</td>\n      <td>...</td>\n      <td>0.9114</td>\n      <td>29.370</td>\n      <td>31.25</td>\n      <td>291796.8124</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.04925</td>\n      <td>0.08463</td>\n    </tr>\n    <tr>\n      <th>5447</th>\n      <td>2020-08-20</td>\n      <td>0.7178</td>\n      <td>1.1862</td>\n      <td>0.6519</td>\n      <td>1.3190</td>\n      <td>5.6370</td>\n      <td>1.3177</td>\n      <td>6.9143</td>\n      <td>6.2783</td>\n      <td>7.7500</td>\n      <td>...</td>\n      <td>0.9082</td>\n      <td>29.420</td>\n      <td>31.41</td>\n      <td>296027.2710</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05063</td>\n      <td>0.08300</td>\n    </tr>\n    <tr>\n      <th>5448</th>\n      <td>2020-08-21</td>\n      <td>0.7156</td>\n      <td>1.1775</td>\n      <td>0.6533</td>\n      <td>1.3098</td>\n      <td>5.5949</td>\n      <td>1.3201</td>\n      <td>6.9179</td>\n      <td>6.3235</td>\n      <td>7.7502</td>\n      <td>...</td>\n      <td>0.9131</td>\n      <td>29.400</td>\n      <td>31.56</td>\n      <td>302779.1537</td>\n      <td>79.588906</td>\n      <td>90.264283</td>\n      <td>-3.837938</td>\n      <td>-2.068833</td>\n      <td>0.05150</td>\n      <td>0.08175</td>\n    </tr>\n  </tbody>\n</table>\n<p>4920 rows × 30 columns</p>\n</div>"}, "metadata": {}, "execution_count": 29}], "source": ["# there is not enough data for libor series at the beginning\n", "# I drop the beginning rows of the whole dataframe\n", "merged3 = merged3[merged3.liborGBP.notna()]\n", "merged3"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"tags": []}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\nInt64Index: 4920 entries, 257 to 5448\nData columns (total 30 columns):\n #   Column     Non-Null Count  Dtype         \n---  ------     --------------  -----         \n 0   Date       4920 non-null   datetime64[ns]\n 1   AUD        4920 non-null   float64       \n 2   EUR        4920 non-null   float64       \n 3   NZD        4920 non-null   float64       \n 4   GBP        4920 non-null   float64       \n 5   BRL        4920 non-null   float64       \n 6   CAD        4920 non-null   float64       \n 7   CNY        4920 non-null   float64       \n 8   DKK        4920 non-null   float64       \n 9   HKD        4920 non-null   float64       \n 10  INR        4920 non-null   float64       \n 11  JPY        4920 non-null   float64       \n 12  MYR        4920 non-null   float64       \n 13  MXN        4920 non-null   float64       \n 14  NOK        4920 non-null   float64       \n 15  ZAR        4920 non-null   float64       \n 16  SGD        4920 non-null   float64       \n 17  KRW        4920 non-null   float64       \n 18  LKR        4920 non-null   float64       \n 19  SEK        4920 non-null   float64       \n 20  CHF        4920 non-null   float64       \n 21  TWD        4920 non-null   float64       \n 22  THB        4920 non-null   float64       \n 23  VEB        4920 non-null   float64       \n 24  gdpGBP     4920 non-null   float64       \n 25  gdpUSD     4920 non-null   float64       \n 26  GBR_Value  4920 non-null   float64       \n 27  USA_Value  4920 non-null   float64       \n 28  liborGBP   4920 non-null   float64       \n 29  liborUSD   4920 non-null   float64       \ndtypes: datetime64[ns](1), float64(29)\nmemory usage: 1.2 MB\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["(None,\n", "                AUD          EUR          NZD          GBP          BRL  \\\n", " count  4920.000000  4920.000000  4920.000000  4920.000000  4920.000000   \n", " mean      0.786383     1.220803     0.682997     1.580702     2.668149   \n", " std       0.144447     0.155491     0.109668     0.215830     0.839373   \n", " min       0.482800     0.837000     0.399300     1.149200     1.537500   \n", " 25%       0.704675     1.118600     0.640600     1.427975     2.021950   \n", " 50%       0.764100     1.228750     0.695150     1.569000     2.389200   \n", " 75%       0.899050     1.333025     0.756400     1.714975     3.209675   \n", " max       1.102600     1.601000     0.881400     2.110400     5.920400   \n", " \n", "                CAD          CNY          DKK          HKD          INR  ...  \\\n", " count  4920.000000  4920.000000  4920.000000  4920.000000  4920.000000  ...   \n", " mean      1.221935     7.139879     6.208932     7.781377    53.845028  ...   \n", " std       0.175890     0.789855     0.853825     0.028063    10.329285  ...   \n", " min       0.916800     6.040200     4.660500     7.708500    38.480000  ...   \n", " 25%       1.053200     6.465375     5.587150     7.755500    45.460000  ...   \n", " 50%       1.232750     6.847050     6.058000     7.775100    48.720000  ...   \n", " 75%       1.329800     8.082675     6.664250     7.799800    64.040000  ...   \n", " max       1.612800     8.280000     8.890000     7.849900    76.950000  ...   \n", " \n", "                CHF          TWD          THB            VEB       gdpGBP  \\\n", " count  4920.000000  4920.000000  4920.000000    4920.000000  4920.000000   \n", " mean      1.116525    31.726995    35.153803    7924.671647    99.587670   \n", " std       0.219637     1.709525     4.497721   33857.554034     3.110936   \n", " min       0.729600    28.500000    28.600000       0.698800    79.588906   \n", " 25%       0.962400    30.200000    31.570000       2.144600    99.498920   \n", " 50%       1.012050    31.710000    33.340000       4.289300   100.036776   \n", " 75%       1.241150    32.980000    39.032500       6.284200   100.431762   \n", " max       1.818500    35.210000    45.820000  302779.153700   103.064911   \n", " \n", "             gdpUSD    GBR_Value    USA_Value     liborGBP     liborUSD  \n", " count  4920.000000  4920.000000  4920.000000  4920.000000  4920.000000  \n", " mean     99.720588    -3.336478    -3.362211     2.182617     1.579345  \n", " std       1.663608     1.224017     1.380993     2.100246     1.679446  \n", " min      90.264283    -6.492008    -6.295718     0.048380     0.050750  \n", " 25%      99.507973    -4.050640    -4.604743     0.480630     0.159000  \n", " 50%      99.914284    -3.359743    -2.788938     0.630000     1.067500  \n", " 75%     100.382084    -2.437674    -2.165422     4.535625     2.314687  \n", " max     101.845772    -0.557912    -1.718115     7.000000     6.875000  \n", " \n", " [8 rows x 29 columns])"]}, "metadata": {}, "execution_count": 30}], "source": ["# examine the final data\n", "merged3.info(), merged3.describe()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["file = \"./DATA/Merged.csv\"\n", "merged3.to_csv(file, index=False)"]}]}